<template>
  <!-- 人工填报界面 -->
  <div class="app-container">

    <!-- <p class="history_log">历史填报记录：共{{ totalSum }}条数据</p> -->
    <div class="ac_header">
      <div class="ach_left">
        <p class="title">历史填报记录（条）</p>
        <p class="nums">{{ totalSum }}</p>
      </div>
      <div class="ach_right" v-hasPermi="['data:datain:fileImportAdd']" @click="handleAdd">
        <div class="public_button position_add_btn search_btn">
          <i class="mr10 el-icon-circle-plus-outline"></i>创建任务
        </div>
      </div>
      <div style="clear: both"></div>
      <!-- 清除浮动 -->
    </div>
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" class="search_form">
      <el-row :gutter="24">
        <el-col :span="6">
          <el-form-item label="事件名称" prop="eventName">
            <el-input v-model="queryParams.eventName" placeholder="请输入事件名称" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="事件主题" prop="themeId">
            <el-select v-model="queryParams.themeId" placeholder="请选择主题" clearable style="width: 240px">
              <el-option v-for="dict in dict.type.manual_theme" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="事件专题" prop="mainTopicId">
            <el-select v-model="queryParams.mainTopicId" placeholder="请选择专题" clearable style="width: 240px">
              <el-option v-for="dict in dict.type.manual_main_topic" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="事件类别" prop="mainCategoryId">
            <el-select v-model="queryParams.mainCategoryId" placeholder="请选择类别" clearable style="width: 240px">
              <el-option v-for="dict in dict.type.manual_main_category" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="6">
          <el-form-item label="事件情感" prop="mainSentimentId">
            <el-select v-model="queryParams.mainSentimentId" placeholder="请选择情感" clearable style="width: 240px">
              <el-option v-for="dict in dict.type.manual_main_sentiment" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="填报状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
              <el-option v-for="dict in manualstatus" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="填报时间">
            <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
              range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <!-- <el-form-item style="text-align: right">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">应用筛选</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item> -->
        </el-col>
      </el-row>
      <el-row :gutter="24">


        <el-form-item>
          <div class="public_button clear_button fl position_btn" @click="resetQuery">
            <i class="mr10 el-icon-refresh"></i>重置
          </div>

          <div @click="handleQuery" class="public_button search_btn fl position_btn">
            <i class="mr10 el-icon-search"></i>应用筛选
          </div>
          <div @click="handleExport" v-hasPermi="['data:datain:fileImportUpload']"
            class="public_button export_button fl position_btn">
            <i class="mr10 el-icon-upload2"></i>批量导出
          </div>
          <div @click="handleDelete_pl()" v-hasPermi="['data:datain:fileImportDelete']"
            class="public_button delete_button fl position_btn">
            <i class="mr10 el-icon-delete"></i>批量删除
          </div>
        </el-form-item>
      </el-row>
    </el-form>

    <!-- <el-row :gutter="24" class="mb8">
      <el-col :span="1.5">

      </el-col>

      <el-col :span="1.5">
        <el-button type="success" plain :disabled="multiple" icon="el-icon-upload2" size="mini"
          @click="handleExport">批量导出</el-button>
      </el-col>
      <el-col :span="18">
        <el-button type="danger" plain icon="el-icon-circle-close" size="mini" :disabled="multiple"
          @click="handleDelete_pl()" v-hasPermi="['system:role:remove']">批量删除</el-button>
      </el-col>
      <el-col :span="1.5">

      </el-col>
    </el-row> -->
    <el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="120" />
      <el-table-column label="事件名称" prop="eventName" :show-overflow-tooltip="true" width="150" sortable />
      <el-table-column label="主题" prop="themeId" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="专题" prop="mainTopicId" width="100" />
      <el-table-column label="类别" prop="mainCategoryId" width="100" />
      <!-- 正面、中立、负面。绿蓝红 -->
      <el-table-column label="情感" prop="mainSentimentId" width="100">
        <!-- <el-button>{{mainSentimentId}}</el-button> -->
        <template slot-scope="scope">
          <el-button  style='background-color: #e7faf071 !important;
    border-color: #e7faf071 !important;
    color: #13ce66 !important;' v-if="scope.row.mainSentimentId == '正面'">{{ scope.row.mainSentimentId
          }}</el-button>
          <el-button  style='background-color: #ffeded5e !important;
    border-color: #ffeded5e !important;
    color: #ff4949 !important;' v-if="scope.row.mainSentimentId == '负面'">{{ scope.row.mainSentimentId
          }}</el-button>
          <el-button  style="background-color: #e7faf071 !important;
    border-color: #e7faf071 !important;
    color:#e6a23c !important;" v-if="scope.row.mainSentimentId == '中立'">{{ scope.row.mainSentimentId
          }}</el-button>
          <!-- <el-button type="danger">{{ scope.row.status }}</el-button> -->
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" width="120">
        <template slot-scope="scope">
          <el-button style='background-color: #e7faf071 !important;
    border-color: #e7faf071 !important;
    color: #13ce66 !important;' v-if="scope.row.status == '已提交'">{{ scope.row.status }}</el-button>
          <el-button style="background-color: #ffeded5e !important;
    border-color: #ffeded5e !important;
    color: #ff4949 !important;" v-if="scope.row.status == '草稿'">{{
      scope.row.status
    }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="填报人" prop="createByName" width="100" />
      <el-table-column label="填报时间" align="center" prop="create_time" width="180" sortable>
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <!-- v-if 已提交-->
          <el-button v-if="scope.row.status == '已提交'" size="mini" type="text" icon="el-icon-view"
            @click="handleViewDetails(scope.row)" v-hasPermi="['system:role:edit']">查看</el-button>
          <!-- v-if 草稿 -->
          <el-button v-if="scope.row.status == '草稿'" size="mini" type="text" icon="el-icon-edit"
            @click="handlechange(scope.row)" v-hasPermi="['system:role:edit']">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:role:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    <!-- 新增填报任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="820px" append-to-body>
      <p class="dialogTitle">
        人工填报事件信息、舆情日报、舆情专报、理论研究成果等信息
      </p>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <p class="dialogText">分类信息</p>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="主题" prop="themeId">
              <el-select v-model="form.themeId" placeholder="请选择主题" clearable style="width: 240px">
                <el-option v-for="dict in dict.type.manual_theme" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专题" prop="mainTopicId">
              <el-select v-model="form.mainTopicId" placeholder="请选择专题" clearable style="width: 240px">
                <el-option v-for="dict in dict.type.manual_main_topic" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="类别" prop="mainCategoryId">
              <el-select v-model="form.mainCategoryId" placeholder="请选择类别" clearable style="width: 240px">
                <el-option v-for="dict in dict.type.manual_main_category" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="情感" prop="mainSentimentId">
              <el-select v-model="form.mainSentimentId" placeholder="请选择情感" clearable style="width: 240px">
                <el-option v-for="dict in dict.type.manual_main_sentiment" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <p class="dialogText">基本信息</p>

        <el-form-item label="事件名称" prop="eventName">
          <el-input v-model="form.eventName" placeholder="事件名称"></el-input>
        </el-form-item>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="国家/地区">
              <el-input v-model="form.countryRegion" placeholder="国家/地区"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生地点">
              <el-input v-model="form.localtion" placeholder="发生地点"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="发生时间">
              <!-- <el-input
                v-model="form.occurrenceTime"
                placeholder="发生地点"
              ></el-input> -->
              <el-date-picker v-model="form.occurrenceTime" style="width: 240px" value-format="yyyy-MM-dd"
                placeholder="发生时间"></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="持续时间">
              <el-input v-model="form.duration" placeholder="持续时间"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="事件概况" prop="eventOverview">
          <el-input v-model="form.eventOverview" placeholder="事件的起因、经过、结果等关键信息"></el-input>
        </el-form-item>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="冲突类型" prop="conflictType">
              <el-select v-model="form.conflictType" placeholder="请选择冲突类型" clearable style="width: 240px">
                <el-option v-for="dict in dict.type.manual_ct_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="冲突规模">
              <el-input v-model="form.conflictScale" placeholder="冲突规模"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <!-- 0是1否 -->
            <el-form-item label="是否开火" prop="isOpenFire">
              <el-radio-group v-model="form.isOpenFire">
                <el-radio :label="0">是</el-radio>
                <el-radio :label="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- <el-radio v-model="isOpenFire" :valule="0"> 是 </el-radio>
            <el-radio v-model="isOpenFire" :valule="1"> 否 </el-radio> -->
          </el-col>

          <el-col :span="12">
            <el-form-item label="伤亡人数">
              <el-input v-model="form.casualtiesCount" placeholder="伤亡人数"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="信源">
          <el-input v-model="form.source" placeholder="数据来源"></el-input>
        </el-form-item>
        <p class="dialogText">相关实体</p>
        <el-form-item label="我方">
          <el-input v-model="form.ourPersonnelOrg" placeholder="我方相关人员、组织机构"></el-input>
        </el-form-item>
        <el-form-item label="对方">
          <el-input v-model="form.opponentPersonnelOrg" placeholder="对方相关人员、组织机构"></el-input>
        </el-form-item>
        <p class="dialogText">附件</p>
        <p class="secondTitle">
          请上传开源信息监测软件系统的数据文件，支持一次批量导入多个文件
        </p>
        <el-form-item label-width="0" label="" prop="files">
          <commonUpload v-model="form.files" :file-size="10" :file-type="['docx', 'wps', 'pdf', 'png', 'jpg']"
            upload-url="/api/upload" />
        </el-form-item>
      </el-form>

      <el-popover placement="left" width="400" class="dialogPopover" trigger="hover">
        <div class="notice-container">
          <div class="notice-header">填报须知</div>
          <div class="notice-content">
            <h3>填写规范</h3>
            <ul>
              <li>事件标题应简明扼要，准确反映事件核心内容</li>
              <li>确保时间信息的准确性</li>
              <li>事件概括尽量详细，包含事件的起因、经过、结果等关键信息</li>
              <li>相关实体不可遗漏</li>

              <!-- 相关实体不可遗漏 -->
            </ul>
            <h3>常见问题</h3>
            <p>Q：如何修改已提交的数据？</p>
            <p>A：已提交的数据可在数据资源-xx进行修改。</p>
          </div>
        </div>
        <el-tag slot="reference" type="success">填报须知</el-tag>
      </el-popover>
      <div slot="footer" class="dialog-footer">
        <!-- 暂存和提交传入参数不一 -->
        <!-- 1草、2提交 -->
        <div class="public_button clear_button fr position_btn" @click="submitForm('2')">
          <i class="mr10 el-icon-close"></i>提交
        </div>

        <div @click="submitForm('1')" class="public_button search_btn fr position_btn">
          <i class="mr10 el-icon-finished"></i>暂存草稿
        </div>
        <div style="clear:both"></div>

      </div>
    </el-dialog>
    <!-- 编辑填报任务对话框 -->
    <el-dialog :title="title" :visible.sync="open_change" width="820px" append-to-body>
      <p class="dialogTitle">
        人工填报事件信息、舆情日报、舆情专报、理论研究成果等信息
      </p>
      <el-form ref="form_xq" :model="form_xq" :rules="rules" label-width="150px">
        <p class="dialogText">分类信息</p>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="主题" prop="themeId">
              <el-select v-model="form_xq.themeId" placeholder="请选择主题" clearable style="width: 240px">
                <el-option v-for="dict in dict.type.manual_theme" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专题" prop="mainTopicId">
              <el-select v-model="form_xq.mainTopicId" placeholder="请选择专题" clearable style="width: 240px">
                <el-option v-for="dict in dict.type.manual_main_topic" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="类别" prop="mainCategoryId">
              <el-select v-model="form_xq.mainCategoryId" placeholder="请选择类别" clearable style="width: 240px">
                <el-option v-for="dict in dict.type.manual_main_category" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="情感" prop="mainSentimentId">
              <el-select v-model="form_xq.mainSentimentId" placeholder="请选择情感" clearable style="width: 240px">
                <el-option v-for="dict in dict.type.manual_main_sentiment" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <p class="dialogText">基本信息</p>

        <el-form-item label="事件名称" prop="eventName">
          <el-input v-model="form_xq.eventName" placeholder="事件名称"></el-input>
        </el-form-item>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="国家/地区">
              <el-input v-model="form_xq.countryRegion" placeholder="国家/地区"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生地点">
              <el-input v-model="form_xq.localtion" placeholder="发生地点"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="发生时间">
              <!-- <el-input
                v-model="form.occurrenceTime"
                placeholder="发生地点"
              ></el-input> -->
              <el-date-picker v-model="form_xq.occurrenceTime" style="width: 240px" value-format="yyyy-MM-dd"
                placeholder="发生时间"></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="持续时间">
              <el-input v-model="form_xq.duration" placeholder="持续时间"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="事件概况" prop="eventOverview">
          <el-input v-model="form_xq.eventOverview" placeholder="事件的起因、经过、结果等关键信息"></el-input>
        </el-form-item>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="冲突类型" prop="conflictType">
              <el-select v-model="form_xq.conflictType" placeholder="请选择冲突类型" clearable style="width: 240px">
                <el-option v-for="dict in dict.type.manual_ct_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="冲突规模">
              <el-input v-model="form_xq.conflictScale" placeholder="冲突规模"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <!-- 0是1否 -->
            <el-form-item label="是否开火" prop="isOpenFire">
              <el-radio-group v-model="form_xq.isOpenFire">
                <el-radio :label="0">是</el-radio>
                <el-radio :label="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- <el-radio v-model="isOpenFire" :valule="0"> 是 </el-radio>
            <el-radio v-model="isOpenFire" :valule="1"> 否 </el-radio> -->
          </el-col>

          <el-col :span="12">
            <el-form-item label="伤亡人数">
              <el-input v-model="form_xq.casualtiesCount" placeholder="伤亡人数"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="信源">
          <el-input v-model="form_xq.source" placeholder="数据来源"></el-input>
        </el-form-item>
        <p class="dialogText">相关实体</p>
        <el-form-item label="我方">
          <el-input v-model="form_xq.ourPersonnelOrg" placeholder="我方相关人员、组织机构"></el-input>
        </el-form-item>
        <el-form-item label="对方">
          <el-input v-model="form_xq.opponentPersonnelOrg" placeholder="对方相关人员、组织机构"></el-input>
        </el-form-item>
        <p class="dialogText">附件</p>
        <p class="secondTitle">
          请上传开源信息监测软件系统的数据文件，支持一次批量导入多个文件
        </p>

        <el-form-item label-width="0" label="" prop="fileList">
          <commonUpload v-model="fileList" :file-size="10" :file-type="['docx', 'wps', 'pdf', 'png', 'jpg']"
            upload-url="/api/upload" />
        </el-form-item>

        <!-- 已上传文件显示区域 -->
        <div class="uploaded-files-section" v-if="uploadedFiles.length > 0">
          <div class="section-title">暂存文件（{{ uploadedFiles.length }}）</div>
          <div class="file-list">
            <div v-for="(file, index) in uploadedFiles" :key="file.id || file.uid" class="file-item">
              <div class="file-info">
                <div class="file-icon" :class="getFileIconClass(file.filename)">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>
                </div>

                <div class="file-details">
                  <p class="file-name" :title="file.filename">{{ file.filename }}</p>
                  <p class="file-size">{{ formatFileSize(file.fileSize) }}</p>
                  <!-- <p class="file-status">已上传</p> -->
                </div>
              </div>

              <div class="file-actions">
                <button type="button" class="action-btn download-btn" @click="handleDownload(file)" title="下载">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" />
                    <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>
                </button>

                <button type="button" class="action-btn delete-btn" @click="handleDeleteUploadedFile(file, index)"
                  title="删除">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" />
                    <path
                      d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
        <!-- </div> -->
      </el-form>

      <el-popover placement="left" width="400" class="dialogPopover" trigger="hover">
        <div class="notice-container">
          <div class="notice-header">填报须知</div>
          <div class="notice-content">
            <h3>填写规范</h3>
            <ul>
              <li>事件标题应简明扼要，准确反映事件核心内容</li>
              <li>确保时间信息的准确性</li>
              <li>事件概括尽量详细，包含事件的起因、经过、结果等关键信息</li>
              <li>相关实体不可遗漏</li>

              <!-- 相关实体不可遗漏 -->
            </ul>
            <h3>常见问题</h3>
            <p>Q：如何修改已提交的数据？</p>
            <p>A：已提交的数据可在数据资源-xx进行修改。</p>
          </div>
        </div>
        <el-tag slot="reference" type="success">填报须知</el-tag>
      </el-popover>
      <div slot="footer" class="dialog-footer">
        <!-- 暂存和提交传入参数不一 -->
        <!-- 1草、2提交 -->
        <div class="public_button clear_button fr position_btn" @click="submitForm_change('2')">
          <i class="mr10 el-icon-close"></i>提交
        </div>

        <div @click="submitForm_change('1')" class="public_button search_btn fr position_btn">
          <i class="mr10 el-icon-finished"></i>暂存草稿
        </div>
        <div style="clear:both"></div>
        <!-- <el-button @click="submitForm_change('1')">暂存草稿</el-button>
        <el-button type="primary" @click="submitForm_change('2')">提交</el-button> -->
      </div>
    </el-dialog>
    <!-- 查看详情弹框 -->
    <el-dialog title="任务详细信息" :visible.sync="openDetailDialog" width="820px" append-to-body>
      <el-form ref="form" :model="form_xq" label-width="150px">
        <p class="dialogText">分类信息</p>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="主题" prop="form_xq.themeId">
              <el-select v-model="form_xq.themeId" placeholder="" clearable style="width: 240px" disabled>
                <el-option v-for="dict in dict.type.manual_theme" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专题" prop="form_xq.mainTopicId">
              <el-select placeholder="" v-model="form_xq.mainTopicId" clearable style="width: 240px" disabled>
                <el-option v-for="dict in dict.type.manual_main_topic" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="类别" prop="form_xq.mainCategoryId">
              <!-- <el-input
                v-model="form_xq.mainCategoryName"
                placeholder="类别"
              ></el-input> -->
              <el-select placeholder="" v-model="form_xq.mainCategoryId" clearable style="width: 240px" disabled>
                <el-option v-for="dict in dict.type.manual_main_category" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="情感" prop="form_xq.mainSentimentId">
              <!-- <el-input
                v-model="form_xq.mainSentimentName"
                placeholder="情感"
              ></el-input> -->
              <el-select placeholder="" v-model="form_xq.mainSentimentId" clearable style="width: 240px" disabled>
                <el-option v-for="dict in dict.type.manual_main_sentiment" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <p class="dialogText">基本信息</p>

        <el-form-item label="事件名称">
          <el-input disabled v-model="form_xq.eventName" placeholder="事件名称"></el-input>
        </el-form-item>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="国家/地区">
              <el-input disabled v-model="form_xq.countryRegion" placeholder="国家/地区"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生地点">
              <el-input disabled v-model="form_xq.localtion" placeholder="发生地点"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="发生时间">
              <el-input disabled v-model="form_xq.occurrenceTime" placeholder="发生时间"></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="持续时间">
              <el-input disabled v-model="form_xq.duration" placeholder="持续时间"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="事件概况">
          <el-input disabled v-model="form_xq.eventOverview" placeholder="事件的起因、经过、结果等关键信息"></el-input>
        </el-form-item>

        <el-form-item label="信源">
          <el-input disabled v-model="form_xq.source" placeholder="数据来源"></el-input>
        </el-form-item>
        <p class="dialogText">相关实体</p>
        <el-form-item label="我方">
          <el-input disabled v-model="form_xq.ourPersonnelOrg" placeholder="我方相关人员、组织机构"></el-input>
        </el-form-item>
        <el-form-item label="对方">
          <el-input disabled v-model="form_xq.opponentPersonnelOrg" placeholder="对方相关人员、组织机构"></el-input>
        </el-form-item>
        <!-- <p class="dialogText">附件</p> -->
        <el-form-item label="附件">
          <!-- 文件列表 可下载 -->
          <!-- 根据文件类型显示不同的图标去掉了-->
          <transition-group name="file-list" tag="div" class="file-list" v-if="uploadedFiles.length > 0">
            <div v-for="file in uploadedFiles" :key="file.id" class="file-item">
              <div class="file-info">
                <div class="file-icon" :class="getFileIconClass(file.filename)">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                      stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>
                </div>

                <div class="file-details">
                  <p class="file-name" :title="file.filename || file.filename">
                    {{ file.filename || file.filename }}
                  </p>
                </div>
              </div>

            </div>
          </transition-group>
        </el-form-item>
      </el-form>




    </el-dialog>
  </div>
</template>

<script>
import commonUpload from "@/views/components/commonUpload.vue";
import {
  // manual_theme
  // dictCode 不会重复，当下拉框传的键值
  exports,
  datain_record_list,
  // listRole,
  // getRole,
  getrecord,
  delrecord,
  addrecord,
  // 提交el-form
  updaterecord,
  // 暂存
  save_record,
  // dataScope,
  // changeRoleStatus,
  // deptTreeSelect,
} from "@/api/data/datain";
import {
  treeselect as menuTreeselect,
  roleMenuTreeselect,
} from "@/api/system/menu";

export default {
  components: { commonUpload },

  name: "Role",
  dicts: [
    "manual_theme",
    "manual_status",
    "manual_main_sentiment",
    "manual_main_category",
    "manual_main_topic",
    "manual_ct_type",
  ],

  data() {
    return {
      fileList: [], // 新上传的文件
      uploadedFiles: [], // 从后端获取的已上传文件
      manualstatus: [
        { value: 2, label: "提交" },
        { value: 1, label: "草稿" },
      ],


      openDetailDialog: false,
      open_change: false,
      // open_change
      totalSum: 0,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: "1",
          label: "全部数据权限",
        },
        {
          value: "2",
          label: "自定数据权限",
        },
        {
          value: "3",
          label: "本部门数据权限",
        },
        {
          value: "4",
          label: "本部门及以下数据权限",
        },
        {
          value: "5",
          label: "仅本人数据权限",
        },
      ],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 详情返回并展示的字段
      queryParams_xq: {
        // 主题
        themeId: undefined,
        // 专题
        mainTopicId: undefined,
        //类别
        mainCategoryId: undefined,
        // 情感
        mainSentimentId: undefined,
        // 事件名称
        eventName: undefined,
        // 国家、地区
        countryRegion: undefined,
        // 发生地点
        locltion: undefined,
        // 发生时间
        occurrenceTime: undefined,
        // 持续时间
        duration: undefined,
        // 事件概况
        eventOverview: undefined,
        // 信源
        source: undefined,
        // 我方
        ourPersonnelOrg: undefined,
        // 对方
        opponentPersonnelOrg: undefined,
        // 附件信息
        fj: [
          {
            fjid: "",
            fjName: "",
          },
        ],
      },
      // 显示新增弹窗
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // 在这里改成研判的查询字段
        //
        eventName: undefined,
        themeId: undefined,
        mainSentimentId: undefined,
        mainTopicId: undefined,
        mainCategoryId: undefined,
        status: undefined,
      },
      // 继续编辑记录的id
      changeid: "",
      // 新增表单参数
      form: {
        themeId: "", //主题id
        mainTopicId: "", //专题id
        mainSentimentId: "", //情感id
        mainCategoryId: "", //类别id
        eventName: "", //事件名称
        countryRegion: "", //国家/地区
        localtion: "", //发生地点
        occurrenceTime: "", //发生时间
        duration: "", //持续时间
        eventOverview: "", //事件概况
        conflictType: "", //冲突类型
        conflictScale: "", //冲突规模
        isOpenFire: "", //是否开火
        casualtiesCount: "", //伤亡人数
        source: "", //信源
        ourPersonnelOrg: "", //我方相关人员、组织机构
        opponentPersonnelOrg: "", //对方相关人员、组织机构
        status: "",
        // 继续编辑记录的id

        changeid: "",
        files: undefined,

      },
      // 查询单条目、修改表单参数
      form_xq: {
        themeName: "", //主题名称
        themeId: "", //主题id
        mainTopicId: "", //专题id
        mainSentimentId: "", //情感id
        mainCategoryId: "", //类别id
        eventName: "", //事件名称
        countryRegion: "", //国家/地区
        localtion: "", //发生地点
        occurrenceTime: "", //发生时间
        duration: "", //持续时间
        eventOverview: "", //事件概况
        conflictType: "", //冲突类型
        conflictScale: "", //冲突规模
        isOpenFire: "", //是否开火
        casualtiesCount: "", //伤亡人数
        source: "", //信源
        ourPersonnelOrg: "", //我方相关人员、组织机构
        opponentPersonnelOrg: "", //对方相关人员、组织机构
        status: "",
        // 继续编辑记录的id

        id: "",
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 表单校验
      rules: {
        themeId: [{ required: true, message: "主题不能为空", trigger: "blur" }],
        mainTopicId: [
          { required: true, message: "专题不能为空", trigger: "blur" },
        ],
        eventName: [
          { required: true, message: "事件名称不能为空", trigger: "blur" },
        ],
        eventOverview: [
          { required: true, message: "事件概况不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.what();
  },
  methods: {

    // 从后端加载已上传文件列表
    loadUploadedFiles() {
      if (this.manualWordEntriesFile && this.manualWordEntriesFile.length > 0) {
        this.uploadedFiles = this.manualWordEntriesFile.map(file => ({
          id: file.id,
          name: file.filename, // 使用 filename 字段
          size: file.fileSize,
          fileData: file.fileData, // 文件流数据
          filePath: file.filePath, // 文件路径
          createTime: file.createTime,
          createByName: file.createByName,
          fileType: this.getFileType(file.filename),
          uid: `uploaded-${file.id}`
        }))
      }
    },

    // 下载文件（不调用接口）
    handleDownload(file) {
      try {
        if (file.fileData) {
          // 处理 base64 文件数据
          let blob

          // 检查是否是 base64 数据
          if (this.isBase64(file.fileData)) {
            // 方法1: 如果是完整的 data URL
            if (file.fileData.startsWith('data:')) {
              const link = document.createElement('a')
              link.href = file.fileData
              link.download = file.filename
              link.click()
              return
            }

            // 方法2: 如果是纯 base64 字符串
            const binaryString = atob(file.fileData)
            const bytes = new Uint8Array(binaryString.length)
            for (let i = 0; i < binaryString.length; i++) {
              bytes[i] = binaryString.charCodeAt(i)
            }
            blob = new Blob([bytes], { type: this.getMimeType(file.fileType) })
          } else {
            // 方法3: 如果是其他格式的二进制数据
            blob = new Blob([file.fileData], { type: this.getMimeType(file.fileType) })
          }

          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = file.filename
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        } else {
          this.$message.warning('文件数据不存在，无法下载')
        }
      } catch (error) {
        console.error('下载文件失败:', error)
        this.$message.error('下载文件失败：' + error.message)
      }
    },

    // 检查字符串是否为 base64
    isBase64(str) {
      if (typeof str !== 'string') return false
      // 检查是否是 data URL
      if (str.startsWith('data:')) return true
      // 检查是否是纯 base64（移除可能的换行符和空格）
      const cleanStr = str.replace(/\s/g, '')
      try {
        return btoa(atob(cleanStr)) === cleanStr
      } catch (err) {
        return false
      }
    },

    // 删除已上传文件（不调用接口）
    handleDeleteUploadedFile(file, index) {
      this.$confirm(`确定要删除文件 "${file.filename}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 直接从列表中移除
        this.uploadedFiles.splice(index, 1)

        // 如果需要，可以同步更新 manualWordEntriesFile
        if (this.manualWordEntriesFile) {
          const fileIndex = this.manualWordEntriesFile.findIndex(f => f.id === file.id)
          if (fileIndex > -1) {
            this.manualWordEntriesFile.splice(fileIndex, 1)
          }
        }

        // 触发事件通知父组件
        this.$emit('files-updated', this.uploadedFiles)

        this.$message.success('删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 根据文件类型获取 MIME 类型
    getMimeType(fileType) {
      const mimeTypes = {
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'zip': 'application/zip',
        'txt': 'text/plain',
        'pdf': 'application/pdf',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif'
      }
      return mimeTypes[fileType] || 'application/octet-stream'
    },

    // 文件类型判断方法
    getFileType(fileName) {
      if (!fileName) return 'file'
      const ext = fileName.split('.').pop().toLowerCase()
      if (['doc', 'docx'].includes(ext)) return 'doc'
      if (['xls', 'xlsx'].includes(ext)) return 'excel'
      if (['zip', 'rar', '7z'].includes(ext)) return 'zip'
      if (['txt', 'json', 'csv'].includes(ext)) return 'text'
      if (['pdf'].includes(ext)) return 'pdf'
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(ext)) return 'image'
      return 'file'
    },

    // 获取文件图标类名
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName)
      return `file-icon-${type}`
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return (bytes / Math.pow(k, i)).toFixed(2) + ' ' + sizes[i]
    },
    what() {
      console.log(this.dict.type.manual_main_sentiment);
    },
    formatFileSize(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toFixed(2) + " " + sizes[i];
    },
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },

    // 继续编辑
    async handlechange(row) {
      this.fileList = [];
      let res = await getrecord(row.id);
      // console.log(res.data)
      this.form_xq = res.data[0];
      this.uploadedFiles = res.data[0].manualWordEntriesFile;
      this.loadUploadedFiles()
      this.open_change = true;
      this.form_xq.id = row.id;
      this.task_id = row.id;
      console.log("获取当行id用于编辑");
    },
    // 查看详情
    async handleViewDetails(row) {
      let res = await getrecord(row.id);
      console.log(res.data);
      this.form_xq = res.data[0];
      this.uploadedFiles = res.data[0].manualWordEntriesFile;
      this.openDetailDialog = true;
      this.loadUploadedFiles();

    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "填报任务";
    },
    submitForm(c1) {
      this.$refs["form"].validate((valid) => {
        if (valid) {

          this.form.status = c1;
          // 构建FormData
          const formData = new FormData();
          // 遍历 this.form 对象的每个属性
          for (const key in this.form) {
            if (this.form.hasOwnProperty(key)) {
              if (key === 'files') {
                // 如果是文件数组，单独处理
                this.form.files.forEach((file) => {
                  formData.append("files", file.raw || file);
                });
              } else {
                // 其他属性直接添加到 formData 中
                formData.append(key, this.form[key]);
              }
            }
          }
          addrecord(formData)
            .then((response) => {
              // *******取后端提示
              // this.$message.success("操作成功");
              this.$modal.msgSuccess(response.msg);
              this.open = false;
              this.getList();
            })
            .catch((error) => {
              console.error(response.msg, error);
            });
        }
        else { }
      });

    },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      datain_record_list(
        this.addDateRange(this.queryParams, this.dateRange)
      ).then((response) => {
        this.roleList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.totalSum = response.totalSum;
      });
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect().then((response) => {
        this.menuOptions = response.data;
      });
    },
    // 所有菜单节点数据
    // getMenuAllCheckedKeys() {
    //   // 目前被选中的菜单节点
    //   let checkedKeys = this.$refs.menu.getCheckedKeys();
    //   // 半选中的菜单节点
    //   let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
    //   checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    //   return checkedKeys;
    // },
    // // 所有部门节点数据
    // getDeptAllCheckedKeys() {
    //   // 目前被选中的部门节点
    //   let checkedKeys = this.$refs.dept.getCheckedKeys();
    //   // 半选中的部门节点
    //   let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();
    //   checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    //   return checkedKeys;
    // },
    /** 根据角色ID查询菜单树结构 */
    getRoleMenuTreeselect(roleId) {
      return roleMenuTreeselect(roleId).then((response) => {
        this.menuOptions = response.menus;
        return response;
      });
    },
    /** 根据角色ID查询部门树结构 */
    getDeptTree(roleId) {
      return deptTreeSelect(roleId).then((response) => {
        this.deptOptions = response.depts;
        return response;
      });
    },
    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.roleName + '"角色吗？')
        .then(function () {
          return changeRoleStatus(row.roleId, row.status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      (this.menuExpand = false),
        (this.menuNodeAll = false),
        (this.deptExpand = true),
        (this.deptNodeAll = false),
        (this.form = {
          themeId: "", //主题id
          mainTopicId: "", //专题id
          mainSentimentId: "", //情感id
          mainCategoryId: "", //类别id
          EventName: "", //事件名称
          CountryRegion: "", //国家/地区
          localtion: "", //发生地点
          occurrenceTime: "", //发生时间
          duration: "", //持续时间
          eventOverview: "", //事件概况
          conflictType: "", //冲突类型
          conflictScale: "", //冲突规模
          isOpenFire: "", //是否开火
          casualtiesCount: "", //伤亡人数
          source: "", //信源
          ourPersonnelOrg: "", //我方相关人员、组织机构
          opponentPersonnelOrg: "", //对方相关人员、组织机构
          status: "",
        });
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleDataScope(row);
          break;
        case "handleAuthUser":
          this.handleAuthUser(row);
          break;
        default:
          break;
      }
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type == "menu") {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
        }
      } else if (type == "dept") {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type == "menu") {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
      } else if (type == "dept") {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions : []);
      }
    },


    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids;
      const roleMenu = this.getRoleMenuTreeselect(roleId);
      getrecord(roleId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.then((res) => {
            let checkedKeys = res.checkedKeys;
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false);
              });
            });
          });
        });
      });
      this.title = "修改角色";
    },
    /** 选择角色权限范围触发 */
    dataScopeSelectChange(value) {
      if (value !== "2") {
        this.$refs.dept.setCheckedKeys([]);
      }
    },


    submitForm_change: function (c1) {
      this.$refs["form_xq"].validate((valid) => {
        if (valid) {
          this.form_xq.status = c1;
          const formData = new FormData();

          // 调试：检查 uploadedFiles 结构
          console.log('uploadedFiles 结构:', this.uploadedFiles);

          // 1. 添加已上传的文件（从 uploadedFiles 中获取）
          if (this.uploadedFiles && this.uploadedFiles.length > 0) {
            this.uploadedFiles.forEach(file => {
              console.log('处理已上传文件:', file);

              // 根据实际数据结构获取文件名和文件数据
              const fileName = file.filename || file.name; // 尝试不同的字段名
              const fileData = file.fileData || file.data; // 尝试不同的字段名

              if (fileData && fileName) {
                try {
                  // 将 base64 数据转换为 Blob，然后创建 File 对象
                  const blob = this.base64ToBlob(fileData, fileName);
                  const fileObj = new File([blob], fileName, { type: blob.type });
                  // 使用 "files" 字段名添加文件
                  formData.append("files", fileObj);
                  console.log(`成功添加已上传文件: ${fileName}`);
                } catch (error) {
                  console.error(`处理已上传文件 ${fileName} 失败:`, error);
                }
              } else {
                console.warn('文件数据不完整，跳过:', file);
                console.log('fileName:', fileName);
                console.log('fileData:', fileData ? '有数据' : '无数据');
              }
            });
          }

          // 2. 添加新上传的文件（从 fileList 中获取）
          if (this.fileList && this.fileList.length > 0) {
            this.fileList.forEach(file => {
              console.log('处理新文件:', file);

              if (file.raw) {
                // 新上传的文件，直接使用 raw File 对象
                formData.append("files", file.raw);
                console.log(`成功添加新文件: ${file.name}`);
              } else if (file.fileData && file.name) {
                // 如果有文件数据，创建File对象
                try {
                  const blob = this.base64ToBlob(file.fileData, file.name);
                  const fileObj = new File([blob], file.name, { type: blob.type });
                  formData.append("files", fileObj);
                  console.log(`成功添加新文件(从fileData): ${file.name}`);
                } catch (error) {
                  console.error(`处理新文件 ${file.name} 失败:`, error);
                }
              } else {
                console.warn('新文件数据不完整，跳过:', file);
              }
            });
          }

          // 3. 添加其他表单数据
          const otherFields = [
            'themeId', 'mainTopicId', 'mainSentimentId', 'mainCategoryId',
            'EventName', 'CountryRegion', 'localtion', 'occurrenceTime',
            'duration', 'eventOverview', 'conflictType', 'conflictScale',
            'isOpenFire', 'casualtiesCount', 'source', 'ourPersonnelOrg',
            'opponentPersonnelOrg', 'status', 'eventName', 'countryRegion',
            'id'
          ];

          otherFields.forEach(field => {
            if (this.form_xq[field] !== null && this.form_xq[field] !== undefined && this.form_xq[field] !== '') {
              formData.append(field, this.form_xq[field]);
            }
          });



          // 调试：检查 FormData 内容
          console.log('FormData 内容:');
          for (let pair of formData.entries()) {
            if (pair[1] instanceof File) {
              console.log(`${pair[0]}: File - ${pair[1].name}, size: ${pair[1].size}`);
            } else {
              console.log(`${pair[0]}: ${pair[1]}`);
            }
          }

          // 调用更新接口
          updaterecord(formData).then((response) => {
            this.$modal.msgSuccess(response.msg);
            this.open_change = false;
            this.getList();
          }).catch((error) => {
            console.error(response.msg, error);
            this.$modal.msgError("操作失败: " + (error.message || '未知错误'));
          });
        }
        else { }
      });

    },
    // 辅助方法：将 base64 转换为 Blob
    // 辅助方法：将 base64 转换为 Blob
    base64ToBlob(base64Data, fileName) {
      // 添加参数验证
      if (!base64Data) {
        throw new Error('base64 数据为空');
      }
      if (!fileName) {
        throw new Error('文件名为空');
      }

      try {
        let mimeType;
        let base64String;

        // 检查是否是完整的 data URL 格式
        if (base64Data.startsWith('data:')) {
          // 提取 MIME 类型和 base64 数据
          const matches = base64Data.match(/^data:(.+);base64,(.+)$/);
          if (!matches || matches.length !== 3) {
            throw new Error('无效的 base64 数据格式');
          }

          mimeType = matches[1];
          base64String = matches[2];
        } else {
          // 如果是纯 base64 字符串（没有 data: 前缀）
          // 根据文件扩展名推断 MIME 类型
          const extension = fileName.split('.').pop().toLowerCase();
          const mimeTypes = {
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'zip': 'application/zip',
            'txt': 'text/plain',
            'pdf': 'application/pdf',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png'
          };

          mimeType = mimeTypes[extension] || 'application/octet-stream';
          base64String = base64Data;
        }

        const byteCharacters = atob(base64String);
        const byteArrays = [];

        for (let offset = 0; offset < byteCharacters.length; offset += 512) {
          const slice = byteCharacters.slice(offset, offset + 512);
          const byteNumbers = new Array(slice.length);

          for (let i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
          }

          const byteArray = new Uint8Array(byteNumbers);
          byteArrays.push(byteArray);
        }

        return new Blob(byteArrays, { type: mimeType });
      } catch (error) {
        console.error('base64 转换失败:', error);
        throw new Error(`文件 ${fileName} 数据格式错误: ${error.message}`);
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除选中的数据项？")
        .then(function () {
          return delrecord(roleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    handleDelete_pl() {
      const roleIds = this.ids;
      // this.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？').then(function() {
      this.$modal
        .confirm("是否确认删除选中的数据项？")
        .then(function () {
          return delrecord(roleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      // this.download(s
      //   "dataaccess/manual/export",
      //   {
      //     ids: [this.ids],
      //   },
      //   `填报记录.xlsx`
      // );
      this.download(
        "dataaccess/manual/export",
        {
          ...this.queryParams,
        },
        `填报记录.xlsx`
      );
    },
  }


}


</script>
<style rel="stylesheet/scss" lang="scss">
.history_log {
  font-size: 14px;
  color: #606266;
  font-weight: 600;
}

/* 已上传文件区域样式 */
.uploaded-files-section {
  margin-top: 20px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding-left: 4px;
}

/* 复用上传组件的文件列表样式 */
.file-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border: 1px solid #e4e7ed;
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-item:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #f0f7ff 0%, #ffffff 100%);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.file-icon svg {
  width: 24px;
  height: 24px;
}

.file-icon-excel {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.file-icon-zip {
  background: linear-gradient(135deg, #e6a23c 0%, #f0c78a 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.file-icon-text {
  background: linear-gradient(135deg, #409eff 0%, #79bbff 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.file-icon-file {
  background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.3);
}

.file-item:hover .file-icon {
  transform: scale(1.1) rotate(5deg);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #909399;
  margin: 0 0 2px 0;
}

.file-status {
  font-size: 11px;
  color: #67c23a;
  margin: 0;
  font-weight: 500;
}

.file-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #f5f7fa;
}

.action-btn svg {
  width: 18px;
  height: 18px;
}

.download-btn {
  color: #409eff;
}

.download-btn:hover {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.delete-btn {
  color: #f56c6c;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #f56c6c 0%, #f89898 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-item {
    padding: 12px 16px;
  }

  .file-icon {
    width: 36px;
    height: 36px;
  }

  .file-icon svg {
    width: 20px;
    height: 20px;
  }
}

.ac_header {
  width: 98%;
  margin: auto;
  height: 110px;
  background-image: url("../../../assets/images/lsdrsj_bg.png");
  background-size: contain;

  .ach_left {
    float: left;
    height: 100%;

    .title {
      font-family: SourceHanSansSC-Bold;
      font-size: 16px;
      color: #ffffff;
      letter-spacing: 0;
      padding-left: 30px;
      // font-weight: 700;
    }

    .nums {
      font-family: LetsgoDigital-Regular;
      font-size: 36px;
      color: #ffffff;
      letter-spacing: 0;
      text-shadow: 0 2px 5px rgba(2, 0, 70, 0.5);
      font-weight: 700;
      margin-top: 10px;
      padding-left: 30px;
      /* 核心代码开始 */
      background-image: linear-gradient(to bottom, #ffffff, #ccd8f2, #3869cc);
      -webkit-background-clip: text;
      /* 兼容 Safari */
      background-clip: text;
      color: transparent;
      /* 核心代码结束 */
    }
  }

  .ach_right {
    float: right;
  }
}

.position_btn {
  margin-right: 10px;
}

.position_add_btn {
  margin-right: 30px;
  margin-top: 32px;
}
</style>