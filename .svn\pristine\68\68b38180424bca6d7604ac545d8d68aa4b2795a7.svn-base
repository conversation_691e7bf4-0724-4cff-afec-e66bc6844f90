// 历史总览接口文件
import request from '@/utils/request'

 // 获取所有主题接口
 export function selectListBySystemType(data) {
   return request({
     url: '/dataaccess/HistoryAnalysisTopicTrends/selectListBySystemType',
     method: 'get',
     params: data
   })
 }
 // 根据主题查找专题接口
 export function selectThematicListById(data) {
   return request({
     url: '/dataaccess/HistoryAnalysisTopicTrends/selectThematicListById',
     method: 'get',
     params: data
   })
 }
//  查询某个专题的详情信息
export function selectTopicDetailById(data) {
  return request({
    url: '/dataaccess/HistoryAnalysisTopicTrends/selectDataByThenaticId',
    method: 'get',
    params: data
  })
}