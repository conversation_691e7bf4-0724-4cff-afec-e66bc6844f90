<template>
  <div class="infrastructure-visualization">
    <!-- 头部统计信息 -->
    <div class="iv_header">
      <!-- <div class="ivh_left">
        <p class="title">基础建设总数（项）</p>
        <p class="nums">{{ totalInfrastructureCount }}</p>
      </div> -->
      <div class="ivh_right" @click="handleAdd">
        <div class="public_button position_add_btn search_btn">
          <i class="mr10 el-icon-circle-plus-outline"></i>添加坐标
        </div>
      </div>
      <div style="clear: both"></div>
    </div>

    <!-- 优化时间范围选择区域，增加时间范围切换效果 -->
    <div class="time-range-section">
      <div class="time-range-item">
        <div class="range-label"><i class="el-icon-time"></i> 第一时间范围</div>
        <el-date-picker
          v-model="timeRange1"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleTimeRange1Change"
          class="time-picker"
        >
        </el-date-picker>
        <el-tag
          v-if="timeRange1 && timeRange1.length === 2"
          type="success"
          size="small"
          class="data-count-tag"
        >
          {{ filteredRange1Count }} 项
        </el-tag>
      </div>
      <div class="time-range-item">
        <div class="range-label"><i class="el-icon-time"></i> 第二时间范围</div>
        <el-date-picker
          v-model="timeRange2"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleTimeRange2Change"
          class="time-picker"
        >
        </el-date-picker>
        <el-tag
          v-if="timeRange2 && timeRange2.length === 2"
          type="warning"
          size="small"
          class="data-count-tag"
        >
          {{ filteredRange2Count }} 项
        </el-tag>
      </div>
      <div class="time-comparison-switch">
        <el-button
          :type="mapShow ? 'success' : 'danger'"
          size="small"
          @click="mapShow = !mapShow"
          :icon="mapShow ? 'el-icon-sort' : 'el-icon-sort'"
        >
          {{ mapShow ? "地图切换" : "地图切换" }}
        </el-button>
        <!-- <el-button
          :type="showComparison ? 'warning' : 'primary'"
          size="small"
          @click="toggleComparison"
          :icon="showComparison ? 'el-icon-view' : 'el-icon-s-data'"
        >
          {{ showComparison ? "对比显示" : "合并显示" }}
        </el-button> -->
        <el-button
          type="info"
          size="small"
          @click="clearTimeRanges"
          icon="el-icon-refresh-left"
        >
          清空筛选
        </el-button>
      </div>
    </div>

    <!-- 地图区域 -->
    <div class="map-container" v-if="mapShow">
      <!-- 添加地图状态指示器 -->
      <!-- <div class="map-status-indicator">
        <div class="status-item" v-if="currentMarkers.length > 0">
          <span class="status-dot building-dot"></span>
          <span class="status-text">建筑: {{ buildingCount }} 个</span>
        </div>
        <div class="status-item" v-if="currentRoutes.length > 0">
          <span class="status-dot road-dot"></span>
          <span class="status-text">道路: {{ roadCount }} 条</span>
        </div>
        <div class="status-item" v-if="showComparison">
          <span class="status-badge">对比模式</span>
        </div>
      </div> -->

      <!-- <CoolWorldMap 
        height="500px"
        backgroundColor="#0a0f1e"
      /> -->

      <ChinaMapEnhanced
        :road-data="roadData"
        :building-data="buildingData"
        :key="mapKey"
      />
    </div>

    <div class="map-container num2Map" v-else>
      <!-- 添加地图状态指示器 -->
      <!-- <div class="map-status-indicator">
        <div class="status-item" v-if="currentMarkers.length > 0">
          <span class="status-dot building-dot"></span>
          <span class="status-text">建筑: {{ buildingCount }} 个</span>
        </div>
        <div class="status-item" v-if="currentRoutes.length > 0">
          <span class="status-dot road-dot"></span>
          <span class="status-text">道路: {{ roadCount }} 条</span>
        </div>
        <div class="status-item" v-if="showComparison">
          <span class="status-badge">对比模式</span>
        </div>
      </div> -->

      <CoolWorldMap height="500px" backgroundColor="#0a0f1e" />

      <!-- <ChinaMapEnhanced 
        :map-config="mapConfig"
        :markers="currentMarkers"
        :routes="currentRoutes"
      /> -->
    </div>

    <!-- 数据列表 -->
    <div class="data-table-section">
      <div class="table-header">
        <h3><i class="el-icon-s-grid"></i> 基础建设数据列表</h3>
        <!-- 添加快速筛选按钮 -->
        <div class="table-filters">
          <el-radio-group
            v-model="categoryFilter"
            size="small"
            @change="handleCategoryFilter"
          >
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="building">建筑</el-radio-button>
            <el-radio-button label="railway_station">高铁站</el-radio-button>
            <el-radio-button label="port">港口</el-radio-button>
            <el-radio-button label="airport">机场</el-radio-button>
            <!-- <el-radio-button label="railway_station">高铁站</el-radio-button>
            <el-radio-button label="railway_station">高铁站</el-radio-button> -->
          </el-radio-group>
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="infrastructureList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :row-class-name="tableRowClassName"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="80" align="center" />
        <el-table-column
          label="标题"
          prop="title"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span class="title-with-icon">
              <i
                :class="
                  scope.row.category === 'building'
                    ? 'el-icon-office-building'
                    : 'el-icon-map-location'
                "
              ></i>
              {{ scope.row.title }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="坐标" min-width="180" align="center">
          <template slot-scope="scope">
            <div class="coords-display">
              <div class="coord-item">
                <span class="coord-label">起点:</span>
                <span class="coord-value"
                  >{{ scope.row.longitude.toFixed(6) }},
                  {{ scope.row.latitude.toFixed(6) }}</span
                >
              </div>
              <div class="coord-item" v-if="scope.row.category === 'road'">
                <span class="coord-label">终点:</span>
                <span class="coord-value"
                  >{{ scope.row.endLongitude.toFixed(6) }},
                  {{ scope.row.endLatitude.toFixed(6) }}</span
                >
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="分类"
          prop="category"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag
              :type="scope.row.category === 'building' ? 'primary' : 'success'"
              effect="dark"
            >
              <i
                :class="
                  scope.row.category === 'building'
                    ? 'el-icon-office-building'
                    : 'el-icon-map-location'
                "
              ></i>
              {{ scope.row.category === "building" ? "建筑" : "道路" }}
            </el-tag>
          </template>
        </el-table-column> -->
        <el-table-column
          label="内容"
          prop="content"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="基建时间"
          prop="constructionTime"
          width="180"
          align="center"
          sortable
        />
        <!-- 添加删除操作 -->
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              class="edit-btn"
            >
              修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加/修改坐标对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="650px"
      append-to-body
      :show-close="false"
      class="infrastructure-dialog"
    >
      <p class="dialogTitle">
        <i class="el-icon-info"></i>
        添加基础建设的地理位置和详细信息，支持建筑（点）和道路（线）两种类型
      </p>

      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="110px"
        class="dialog_form_style"
      >
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入标题，如：中国尊大厦、京沪高速"
          />
        </el-form-item>

        <el-form-item label="分类" prop="category">
          <el-radio-group
            v-model="form.category"
            @change="handleCategoryChange"
          >
            <el-radio label="building" border>
              <i class="el-icon-office-building"></i> 建筑
            </el-radio>
            <el-radio label="road" border>
              <i class="el-icon-map-location"></i> 道路
            </el-radio>
          </el-radio-group>
          <div class="category-hint">
            <span v-if="form.category === 'building'" class="hint-text">
              <i class="el-icon-info"></i> 建筑将以点标记显示在地图上
            </span>
            <span v-else class="hint-text">
              <i class="el-icon-info"></i>
              道路将以线条显示在地图上，需要提供起点和终点坐标
            </span>
          </div>
        </el-form-item>

        <div class="coordinate-section">
          <div class="section-title">
            <i class="el-icon-location"></i>
            {{ form.category === "road" ? "起点坐标" : "坐标位置" }}
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度" prop="longitude">
                <el-input
                  v-model.number="form.longitude"
                  placeholder="116.404"
                  type="number"
                  step="0.000001"
                >
                  <template slot="append">°E</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度" prop="latitude">
                <el-input
                  v-model.number="form.latitude"
                  placeholder="39.915"
                  type="number"
                  step="0.000001"
                >
                  <template slot="append">°N</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 优化道路终点坐标输入区域 -->
        <transition name="el-zoom-in-top">
          <div class="coordinate-section" v-if="form.category === 'road'">
            <div class="section-title">
              <i class="el-icon-location-outline"></i> 终点坐标
            </div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="经度" prop="endLongitude">
                  <el-input
                    v-model.number="form.endLongitude"
                    placeholder="121.506"
                    type="number"
                    step="0.000001"
                  >
                    <template slot="append">°E</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="终点纬度" prop="endLatitude">
                  <el-input
                    v-model.number="form.endLatitude"
                    placeholder="31.245"
                    type="number"
                    step="0.000001"
                  >
                    <template slot="append">°N</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </transition>

        <el-form-item label="内容描述" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="4"
            placeholder="请输入详细内容，如：高度、规模、特色等信息"
            maxlength="500"
          />
        </el-form-item>

        <el-form-item label="基建时间" prop="constructionTime">
          <el-date-picker
            v-model="form.constructionTime"
            type="datetime"
            placeholder="选择基建完成时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <div class="public_button clear_button fr position_btn" @click="cancel">
          <i class="mr10 el-icon-close"></i>取 消
        </div>
        <div
          class="public_button search_btn fr position_btn"
          @click="submitForm"
        >
          <i class="mr10 el-icon-finished"></i>确 定
        </div>
        <div style="clear: both"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ChinaMapEnhanced from "./WorldMap.vue";
import Pagination from "@/components/Pagination";
import CoolWorldMap from "./CoolWorldMap.vue";

export default {
  name: "InfrastructureVisualization",
  components: {
    ChinaMapEnhanced,
    CoolWorldMap,
    Pagination,
  },
  data() {
    return {
      mapShow: true, // 地图显示状态
      mapConfig: {
        mapColor: "#0a1e3a",
        borderColor: "#1e90ff",
        shadowColor: "#1e90ff",
        shadowBlur: 10,
        setIntervalTime: 5000,
      },
      mapKey: 0,

      timeRange1: [],
      timeRange2: [],
      showComparison: false,
      categoryFilter: "all", // 添加分类筛选

      loading: false,
      infrastructureList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      selectedIds: [],

      dialogVisible: false,
      dialogTitle: "添加坐标",
      form: {
        id: null,
        title: "",
        category: "building",
        longitude: null,
        latitude: null,
        endLongitude: null,
        endLatitude: null,
        content: "",
        constructionTime: "",
      },
      rules: {
        title: [{ required: true, message: "请输入标题", trigger: "blur" }],
        category: [
          { required: true, message: "请选择分类", trigger: "change" },
        ],
        longitude: [
          { required: true, message: "请输入经度", trigger: "blur" },
          { type: "number", message: "经度必须为数字", trigger: "blur" },
        ],
        latitude: [
          { required: true, message: "请输入纬度", trigger: "blur" },
          { type: "number", message: "纬度必须为数字", trigger: "blur" },
        ],
        constructionTime: [
          { required: true, message: "请选择基建时间", trigger: "change" },
        ],
      },
      // 自定义地图样式
      customMapStyle: {
        areaColor: "#16213e",
        borderColor: "#e94560",
        borderWidth: 1.5,
        shadowBlur: 20,
        shadowColor: "rgba(233, 69, 96, 0.6)",
      },

      customEmphasisStyle: {
        areaColor: "#1f4068",
        borderColor: "#ff6b9d",
        borderWidth: 2,
        shadowBlur: 25,
        shadowColor: "rgba(255, 107, 157, 0.8)",
      },

      customScatterStyle: {
        symbolSize: 15,
        color: "#ff6b9d",
        borderColor: "#ffffff",
        borderWidth: 2,
        shadowBlur: 12,
        shadowColor: "rgba(255, 107, 157, 0.9)",
      },
      // 自定义数据
      customData: [
        { name: "Los Angeles", value: [-118.2437, 34.0522, 18] },
        { name: "Mumbai", value: [72.8777, 19.076, 20] },
        { name: "São Paulo", value: [-46.6333, -23.5505, 19] },
        { name: "Moscow", value: [37.6173, 55.7558, 17] },
      ],
      buildingData: [
        {
          name: "乌鲁木齐地窝堡国际机场",
          coords: [87.4833, 43.9022],
          type: "机场",
          status: "运营中",
          capacity: "2300万人次/年",
        },
        {
          name: "成都天府国际机场",
          coords: [104.395, 30.4],
          type: "机场",
          status: "运营中",
          capacity: "6000万人次/年",
        },
        // { name: '北京国际机场', coords: [116.4074, 39.9042], type: '机场', status: '运营中', capacity: '1亿人次/年' },
        // { name: '上海港', coords: [121.4737, 31.2304], type: '港口', status: '运营中', capacity: '4300万标箱/年' },
        {
          name: "迪拜塔",
          coords: [55.2744, 25.1972],
          type: "地标建筑",
          status: "运营中",
          height: "828米",
        },
        {
          name: "伊斯坦布尔机场",
          coords: [28.7519, 41.2753],
          type: "机场",
          status: "运营中",
          capacity: "2亿人次/年",
        },
        {
          name: "新德里地铁",
          coords: [77.209, 28.6139],
          type: "交通枢纽",
          status: "运营中",
          lines: "12条线路",
        },
        {
          name: "德黑兰炼油厂",
          coords: [51.389, 35.6892],
          type: "工业设施",
          status: "运营中",
          capacity: "50万桶/日",
        },
        {
          name: "吉达港",
          coords: [39.1925, 21.4858],
          type: "港口",
          status: "运营中",
          capacity: "1000万标箱/年",
        },
        {
          name: "开罗国际机场",
          coords: [31.4056, 30.1219],
          type: "机场",
          status: "运营中",
          capacity: "3000万人次/年",
        },
        {
          name: "阿拉木图数据中心",
          coords: [76.8512, 43.222],
          type: "数据中心",
          status: "建设中",
          capacity: "10MW",
        },
        {
          name: "喀布尔发电站",
          coords: [69.2075, 34.5553],
          type: "能源设施",
          status: "规划中",
          capacity: "500MW",
        },
      ],
      roadData: [
        // --- 以下为新增数据 ---

        // 中国西部出境铁路
        {
          name: "中吉乌铁路",
          coords: [
            [82.9204, 41.3396],
            [74.7661, 41.2044],
            [69.2163, 41.5128],
          ],
          type: "铁路",
          status: "建设中",
          length: "523公里",
        },
        {
          name: "川藏铁路延伸线",
          coords: [
            [94.9236, 29.6516],
            [97.0376, 29.7059],
            [98.6246, 30.14],
          ],
          type: "铁路",
          status: "建设中",
          length: "800公里",
        },

        // 中国西部出境管道
        {
          name: "中国-中亚天然气管道D线",
          coords: [
            [83.4512, 42.0281],
            [71.7887, 41.3774],
            [64.5219, 39.7837],
            [58.5868, 37.9497],
          ],
          type: "管道",
          status: "运营中",
          length: "3000公里",
        },
        {
          name: "中缅原油管道",
          coords: [
            [97.3973, 24.8064],
            [95.956, 22.1942],
            [94.7325, 19.7633],
          ],
          type: "管道",
          status: "运营中",
          length: "771公里",
        },

        // 中国西部出境公路与光缆
        {
          name: "中巴喀喇昆仑公路",
          coords: [
            [75.9812, 35.1484],
            [74.7661, 34.5227],
            [67.0011, 24.8607],
          ],
          type: "公路",
          status: "运营中",
          length: "1300公里",
        },
        {
          name: "中国-巴基斯坦光缆",
          coords: [
            [77.5946, 39.0742],
            [74.7661, 34.5227],
            [69.3451, 30.3753],
          ],
          type: "光缆",
          status: "运营中",
          length: "2700公里",
        },

        // 南亚区域内线路
        {
          name: "印度-孟加拉国公路",
          coords: [
            [88.3639, 22.5726],
            [89.8332, 24.6232],
          ],
          type: "公路",
          status: "运营中",
          length: "650公里",
        },
        {
          name: "斯里兰卡科伦坡港口连接线",
          coords: [
            [79.8512, 6.9344],
            [80.3176, 7.2906],
          ],
          type: "公路",
          status: "建设中",
          length: "29公里",
        },

        // 西亚及中东区域线路
        {
          name: "伊朗-伊拉克铁路",
          coords: [
            [48.5164, 35.8617],
            [45.7579, 33.2237],
            [44.3661, 33.3152],
          ],
          type: "铁路",
          status: "规划中",
          length: "950公里",
        },
        {
          name: "沙特-约旦-叙利亚公路",
          coords: [
            [46.7219, 24.7136],
            [36.2384, 30.5852],
            [36.7812, 33.5138],
          ],
          type: "公路",
          status: "规划中",
          length: "1800公里",
        },
        {
          name: "土耳其-叙利亚-伊拉克石油管道",
          coords: [
            [32.8597, 39.9334],
            [36.7812, 33.5138],
            [44.3661, 33.3152],
          ],
          type: "管道",
          status: "规划中",
          length: "1500公里",
        },

        // 新增海运线路
        {
          name: "波斯湾-红海海运线",
          coords: [
            [55.1694, 25.2582],
            [45.0355, 12.5737],
          ],
          type: "海运",
          status: "运营中",
          length: "1900公里",
        },
        {
          name: "地中海-埃及亚历山大港",
          coords: [
            [29.9553, 31.2304],
            [31.2001, 30.0444],
          ],
          type: "海运",
          status: "运营中",
          length: "250公里",
        },

        // 新增航空线路 (中国起点均为西部城市)
        {
          name: "乌鲁木齐-德黑兰-伊斯坦布尔航线",
          coords: [
            [87.6271, 43.8255],
            [51.389, 35.6892],
            [28.9784, 41.0082],
          ],
          type: "航空",
          status: "运营中",
          length: "6200公里",
        },
        {
          name: "成都-迪拜-开罗航线",
          coords: [
            [104.0665, 30.5728],
            [55.3188, 25.2797],
            [31.2304, 30.0444],
          ],
          type: "航空",
          status: "运营中",
          length: "7800公里",
        },

        // 海湾地区线路
        {
          name: "沙特-科威特-伊拉克公路",
          coords: [
            [46.7219, 24.7136],
            [47.9744, 29.3759],
            [44.3661, 33.3152],
          ],
          type: "公路",
          status: "运营中",
          length: "1200公里",
        },
        {
          name: "阿联酋-阿曼输油管道",
          coords: [
            [55.1694, 25.2582],
            [58.5922, 23.6101],
          ],
          type: "管道",
          status: "建设中",
          length: "370公里",
        },
        {
          name: "卡塔尔-沙特跨海大桥",
          coords: [
            [51.531, 25.2854],
            [49.8944, 26.2541],
          ],
          type: "公路",
          status: "建设中",
          length: "40公里",
        },
      ],
      mockData: [
        {
          id: 7,
          title: "中吉乌铁路中方起点站",
          category: "railway_station",
          longitude: 82.9204,
          latitude: 41.3396,
          content:
            "中吉乌铁路在中国境内的起点站，位于新疆喀什，是中国连接中亚的新通道。",
          constructionTime: "2023-12-01 10:00:00",
        },
        {
          id: 8,
          title: "川藏铁路拉萨站",
          category: "railway_station",
          longitude: 91.1141,
          latitude: 29.6516,
          content:
            "川藏铁路的终点站，位于西藏拉萨，是世界上海拔最高的铁路枢纽之一。",
          constructionTime: "2026-06-30 15:00:00",
        },
        {
          id: 9,
          title: "中国-中亚天然气管道D线入境站",
          category: "building",
          longitude: 83.4512,
          latitude: 42.0281,
          content:
            "中亚天然气管道D线进入中国的首站，位于新疆伊犁，为中国提供清洁能源。",
          constructionTime: "2019-09-01 09:00:00",
        },
        {
          id: 10,
          title: "中缅原油管道末站",
          category: "port",
          longitude: 94.7325,
          latitude: 19.7633,
          content:
            "中缅原油管道的终点站，位于中国云南瑞丽，连接印度洋石油产区。",
          constructionTime: "2017-04-20 11:30:00",
        },
        {
          id: 11,
          title: "中巴喀喇昆仑公路边境口岸",
          category: "building",
          longitude: 75.9812,
          latitude: 35.1484,
          content:
            "中巴喀喇昆仑公路上的重要边境口岸，促进中国与巴基斯坦的贸易往来。",
          constructionTime: "2009-10-25 14:00:00",
        },
        {
          id: 12,
          title: "中国-巴基斯坦光缆枢纽站",
          category: "building",
          longitude: 77.5946,
          latitude: 39.0742,
          content:
            "连接中国与巴基斯坦的跨境光缆的核心枢纽站，位于新疆乌鲁木齐。",
          constructionTime: "2018-01-18 10:45:00",
        },
        {
          id: 13,
          title: "印度-孟加拉国公路边境检查站",
          category: "building",
          longitude: 88.3639,
          latitude: 22.5726,
          content:
            "印度与孟加拉国边境公路上的检查站，是两国人员和货物往来的重要关口。",
          constructionTime: "2013-08-12 09:30:00",
        },
        {
          id: 14,
          title: "斯里兰卡科伦坡港口城",
          category: "building",
          longitude: 79.8512,
          latitude: 6.9344,
          content:
            "位于科伦坡港口的大型综合发展项目，包括港口、商业区和住宅区。",
          constructionTime: "2022-05-20 16:00:00",
        },
        {
          id: 15,
          title: "伊朗-伊拉克铁路边境站",
          category: "railway_station",
          longitude: 48.5164,
          latitude: 35.8617,
          content: "规划中的伊朗-伊拉克铁路边境站，将加强两国间的铁路连接。",
          constructionTime: "2028-01-01 00:00:00",
        },
        {
          id: 16,
          title: "沙特-约旦-叙利亚公路起点",
          category: "building",
          longitude: 46.7219,
          latitude: 24.7136,
          content:
            "规划中的跨国公路起点，位于沙特阿拉伯，旨在促进中东地区互联互通。",
          constructionTime: "2030-06-30 00:00:00",
        },
        {
          id: 17,
          title: "土耳其-叙利亚-伊拉克石油管道终点站",
          category: "building",
          longitude: 44.3661,
          latitude: 33.3152,
          content:
            "规划中的石油管道终点站，位于伊拉克巴士拉，连接土耳其与波斯湾。",
          constructionTime: "2029-12-31 00:00:00",
        },
        {
          id: 18,
          title: "波斯湾-红海海运线集装箱码头",
          category: "port",
          longitude: 55.1694,
          latitude: 25.2582,
          content:
            "位于阿联酋迪拜的集装箱码头，是波斯湾通往红海的重要海运枢纽。",
          constructionTime: "2005-09-01 12:00:00",
        },
        {
          id: 19,
          title: "埃及亚历山大港",
          category: "port",
          longitude: 29.9553,
          latitude: 31.2304,
          content: "埃及在地中海岸的重要港口，是非洲与欧洲贸易的门户。",
          constructionTime: "1805-01-01 00:00:00",
        },
        {
          id: 20,
          title: "乌鲁木齐地窝堡国际机场",
          category: "airport",
          longitude: 87.6271,
          latitude: 43.8255,
          content:
            "中国西部重要的国际航空枢纽，是连接中国与中亚、西亚的空中桥梁。",
          constructionTime: "1970-01-01 00:00:00",
        },
        {
          id: 21,
          title: "成都双流国际机场",
          category: "airport",
          longitude: 104.0665,
          latitude: 30.5728,
          content:
            "中国中西部地区最繁忙的国际机场，是“一带一路”空中丝绸之路的起点。",
          constructionTime: "1956-01-01 00:00:00",
        },
        {
          id: 22,
          title: "沙特-科威特-伊拉克公路边境口岸",
          category: "building",
          longitude: 46.7219,
          latitude: 24.7136,
          content:
            "位于沙特与科威特边境的公路口岸，是海湾地区重要的陆上交通节点。",
          constructionTime: "2000-05-10 10:00:00",
        },
        {
          id: 23,
          title: "阿联酋-阿曼输油管道终点站",
          category: "building",
          longitude: 58.5922,
          latitude: 23.6101,
          content: "阿联酋至阿曼输油管道的终点站，位于阿曼首都马斯喀特附近。",
          constructionTime: "2024-03-15 14:00:00",
        },
        {
          id: 24,
          title: "卡塔尔-沙特跨海大桥",
          category: "building",
          longitude: 51.531,
          latitude: 25.2854,
          content:
            "连接卡塔尔与沙特阿拉伯的跨海大桥，是海湾地区重要的基础设施。",
          constructionTime: "2027-12-01 00:00:00",
        },
      ],

      // 打印数据以验证
      // console.log(JSON.stringify(mockData, null, 2));
    };
  },
  computed: {
    totalInfrastructureCount() {
      return this.mockData.length;
    },

    currentMarkers() {
      let data = this.mockData.filter((item) => item.category === "building");

      // 如果设置了时间范围，进行筛选
      if (
        this.showComparison &&
        this.timeRange1 &&
        this.timeRange1.length === 2
      ) {
        // 对比模式：只显示第一时间范围
        data = data.filter((item) => {
          const time = new Date(item.constructionTime).getTime();
          return (
            time >= new Date(this.timeRange1[0]).getTime() &&
            time <= new Date(this.timeRange1[1]).getTime()
          );
        });
      } else if (!this.showComparison) {
        // 合并模式：合并两个时间范围
        if (this.timeRange1 && this.timeRange1.length === 2) {
          data = data.filter((item) => {
            const time = new Date(item.constructionTime).getTime();
            return (
              time >= new Date(this.timeRange1[0]).getTime() &&
              time <= new Date(this.timeRange1[1]).getTime()
            );
          });
        }
        if (this.timeRange2 && this.timeRange2.length === 2) {
          const range2Data = this.mockData
            .filter((item) => item.category === "building")
            .filter((item) => {
              const time = new Date(item.constructionTime).getTime();
              return (
                time >= new Date(this.timeRange2[0]).getTime() &&
                time <= new Date(this.timeRange2[1]).getTime()
              );
            });
          // 合并数据并去重
          const mergedData = [...data, ...range2Data];
          const uniqueData = mergedData.filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.id === item.id)
          );
          data = uniqueData;
        }
      }

      return data.map((item) => ({
        name: item.title,
        coords: [item.longitude, item.latitude],
        type: "building",
        value: 100,
        info: item.content,
      }));
    },

    currentRoutes() {
      let data = this.mockData.filter((item) => item.category === "road");

      // 如果设置了时间范围，进行筛选
      if (
        this.showComparison &&
        this.timeRange2 &&
        this.timeRange2.length === 2
      ) {
        // 对比模式：只显示第二时间范围
        data = data.filter((item) => {
          const time = new Date(item.constructionTime).getTime();
          return (
            time >= new Date(this.timeRange2[0]).getTime() &&
            time <= new Date(this.timeRange2[1]).getTime()
          );
        });
      } else if (!this.showComparison) {
        // 合并模式：合并两个时间范围
        if (this.timeRange1 && this.timeRange1.length === 2) {
          data = data.filter((item) => {
            const time = new Date(item.constructionTime).getTime();
            return (
              time >= new Date(this.timeRange1[0]).getTime() &&
              time <= new Date(this.timeRange1[1]).getTime()
            );
          });
        }
        if (this.timeRange2 && this.timeRange2.length === 2) {
          const range2Data = this.mockData
            .filter((item) => item.category === "road")
            .filter((item) => {
              const time = new Date(item.constructionTime).getTime();
              return (
                time >= new Date(this.timeRange2[0]).getTime() &&
                time <= new Date(this.timeRange2[1]).getTime()
              );
            });
          // 合并数据并去重
          const mergedData = [...data, ...range2Data];
          const uniqueData = mergedData.filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.id === item.id)
          );
          data = uniqueData;
        }
      }

      return data.map((item) => ({
        name: item.title,
        from: [item.longitude, item.latitude],
        to: [item.endLongitude, item.endLatitude],
        type: "highway",
        info: item.content,
      }));
    },

    buildingCount() {
      return this.currentMarkers.length;
    },

    roadCount() {
      return this.currentRoutes.length;
    },

    filteredRange1Count() {
      if (!this.timeRange1 || this.timeRange1.length !== 2) return 0;
      return this.mockData.filter((item) => {
        const time = new Date(item.constructionTime).getTime();
        return (
          time >= new Date(this.timeRange1[0]).getTime() &&
          time <= new Date(this.timeRange1[1]).getTime()
        );
      }).length;
    },

    filteredRange2Count() {
      if (!this.timeRange2 || this.timeRange2.length !== 2) return 0;
      return this.mockData.filter((item) => {
        const time = new Date(item.constructionTime).getTime();
        return (
          time >= new Date(this.timeRange2[0]).getTime() &&
          time <= new Date(this.timeRange2[1]).getTime()
        );
      }).length;
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;

      let filteredData = this.mockData;
      if (this.categoryFilter !== "all") {
        filteredData = this.mockData.filter(
          (item) => item.category === this.categoryFilter
        );
      }

      // 模拟分页
      const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
      const end = start + this.queryParams.pageSize;
      this.infrastructureList = filteredData.slice(start, end);
      this.total = filteredData.length;

      setTimeout(() => {
        this.loading = false;
      }, 300);
    },

    handleTimeRange1Change() {
      this.resetDatas()
      this.$message.success(`第一时间范围已更新`);
      if (this.timeRange1 != null) {
        this.roadData = this.roadData.slice(0, 5);
        this.buildingData = this.buildingData.slice(0, 5);
        this.mockData = this.mockData.slice(0, 10);
      }else if (this.timeRange2 != null) {
        this.roadData = this.roadData.slice(-6);
        this.buildingData = this.buildingData.slice(-6);
        this.mockData = this.mockData.slice(-12);
      } else {
        this.resetDatas()
      }
      this.mapKey++;
    },

    handleTimeRange2Change() {
      this.resetDatas()
      console.log("[v0] 第二时间范围改变:", this.timeRange2);
      this.$message.success(
        `第二时间范围已更新`
      );
      if (this.timeRange2 != null) {
        this.roadData = this.roadData.slice(-6);
        this.buildingData = this.buildingData.slice(-6);
        this.mockData = this.mockData.slice(-12);
      }else if (this.timeRange1 != null) {
        this.roadData = this.roadData.slice(0, 5);
        this.buildingData = this.buildingData.slice(0, 5);
        this.mockData = this.mockData.slice(0, 10);
      } else {
        this.resetDatas()
      }
      this.mapKey++;
    },

    toggleComparison() {
      this.showComparison = !this.showComparison;
      if (this.showComparison) {
        this.$message.success(
          "已切换到对比显示模式：第一时间范围显示建筑，第二时间范围显示道路"
        );
      } else {
        this.$message.success(
          "已切换到合并显示模式：两个时间范围的数据合并展示"
        );
      }
    },

    clearTimeRanges() {
      this.timeRange1 = [];
      this.timeRange2 = [];
      this.$message.info("已清空所有时间范围筛选");
    },

    handleCategoryFilter(value) {
      // console.log(value, '1237')
      this.categoryFilter = value;
      
      this.queryParams.pageNum = 1;
      this.getList();
    },

    tableRowClassName({ row }) {
      if (row.category === "building") {
        return "building-row";
      } else if (row.category === "road") {
        return "road-row";
      }
      return "";
    },

    handleSelectionChange(selection) {
      this.selectedIds = selection.map((item) => item.id);
    },

    handleAdd() {
      this.resetForm();
      this.dialogVisible = true;
      this.dialogTitle = "添加坐标";
    },

    handleUpdate(row) {
      this.form = { ...row };
      this.dialogVisible = true;
      this.dialogTitle = "修改坐标";
    },

    handleDelete(row) {
      this.$confirm(`确定要删除"${row.title}"吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const index = this.mockData.findIndex((item) => item.id === row.id);
          if (index !== -1) {
            this.mockData.splice(index, 1);
            this.$message.success("删除成功");
            this.getList();
          }
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    handleCategoryChange(value) {
      if (value === "building") {
        // 切换到建筑时，清空终点坐标
        this.form.endLongitude = null;
        this.form.endLatitude = null;
      }
    },

    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (
            this.form.category === "road" &&
            (!this.form.endLongitude || !this.form.endLatitude)
          ) {
            this.$message.error("道路类型需要填写终点坐标");
            return;
          }

          if (this.form.id) {
            const index = this.mockData.findIndex(
              (item) => item.id === this.form.id
            );
            if (index !== -1) {
              this.mockData[index] = { ...this.form };
              this.$message.success("修改成功");
            }
          } else {
            // 新增
            const newItem = {
              ...this.form,
              id: this.mockData.length + 1,
            };
            this.mockData.push(newItem);
            this.$message.success("添加成功");
          }

          this.dialogVisible = false;
          this.getList();
        }
      });
    },

    cancel() {
      this.dialogVisible = false;
      this.resetForm();
    },

    resetForm() {
      this.form = {
        id: null,
        title: "",
        category: "building",
        longitude: null,
        latitude: null,
        endLongitude: null,
        endLatitude: null,
        content: "",
        constructionTime: "",
      };
      if (this.$refs.form) {
        this.$refs.form.clearValidate();
      }
    },
    resetDatas() {
      this.buildingData = [
        {
          name: "乌鲁木齐地窝堡国际机场",
          coords: [87.4833, 43.9022],
          type: "机场",
          status: "运营中",
          capacity: "2300万人次/年",
        },
        {
          name: "成都天府国际机场",
          coords: [104.395, 30.4],
          type: "机场",
          status: "运营中",
          capacity: "6000万人次/年",
        },
        // { name: '北京国际机场', coords: [116.4074, 39.9042], type: '机场', status: '运营中', capacity: '1亿人次/年' },
        // { name: '上海港', coords: [121.4737, 31.2304], type: '港口', status: '运营中', capacity: '4300万标箱/年' },
        {
          name: "迪拜塔",
          coords: [55.2744, 25.1972],
          type: "地标建筑",
          status: "运营中",
          height: "828米",
        },
        {
          name: "伊斯坦布尔机场",
          coords: [28.7519, 41.2753],
          type: "机场",
          status: "运营中",
          capacity: "2亿人次/年",
        },
        {
          name: "新德里地铁",
          coords: [77.209, 28.6139],
          type: "交通枢纽",
          status: "运营中",
          lines: "12条线路",
        },
        {
          name: "德黑兰炼油厂",
          coords: [51.389, 35.6892],
          type: "工业设施",
          status: "运营中",
          capacity: "50万桶/日",
        },
        {
          name: "吉达港",
          coords: [39.1925, 21.4858],
          type: "港口",
          status: "运营中",
          capacity: "1000万标箱/年",
        },
        {
          name: "开罗国际机场",
          coords: [31.4056, 30.1219],
          type: "机场",
          status: "运营中",
          capacity: "3000万人次/年",
        },
        {
          name: "阿拉木图数据中心",
          coords: [76.8512, 43.222],
          type: "数据中心",
          status: "建设中",
          capacity: "10MW",
        },
        {
          name: "喀布尔发电站",
          coords: [69.2075, 34.5553],
          type: "能源设施",
          status: "规划中",
          capacity: "500MW",
        },
      ];
      this.roadData = [
        // --- 以下为新增数据 ---

        // 中国西部出境铁路
        {
          name: "中吉乌铁路",
          coords: [
            [82.9204, 41.3396],
            [74.7661, 41.2044],
            [69.2163, 41.5128],
          ],
          type: "铁路",
          status: "建设中",
          length: "523公里",
        },
        {
          name: "川藏铁路延伸线",
          coords: [
            [94.9236, 29.6516],
            [97.0376, 29.7059],
            [98.6246, 30.14],
          ],
          type: "铁路",
          status: "建设中",
          length: "800公里",
        },

        // 中国西部出境管道
        {
          name: "中国-中亚天然气管道D线",
          coords: [
            [83.4512, 42.0281],
            [71.7887, 41.3774],
            [64.5219, 39.7837],
            [58.5868, 37.9497],
          ],
          type: "管道",
          status: "运营中",
          length: "3000公里",
        },
        {
          name: "中缅原油管道",
          coords: [
            [97.3973, 24.8064],
            [95.956, 22.1942],
            [94.7325, 19.7633],
          ],
          type: "管道",
          status: "运营中",
          length: "771公里",
        },

        // 中国西部出境公路与光缆
        {
          name: "中巴喀喇昆仑公路",
          coords: [
            [75.9812, 35.1484],
            [74.7661, 34.5227],
            [67.0011, 24.8607],
          ],
          type: "公路",
          status: "运营中",
          length: "1300公里",
        },
        {
          name: "中国-巴基斯坦光缆",
          coords: [
            [77.5946, 39.0742],
            [74.7661, 34.5227],
            [69.3451, 30.3753],
          ],
          type: "光缆",
          status: "运营中",
          length: "2700公里",
        },

        // 南亚区域内线路
        {
          name: "印度-孟加拉国公路",
          coords: [
            [88.3639, 22.5726],
            [89.8332, 24.6232],
          ],
          type: "公路",
          status: "运营中",
          length: "650公里",
        },
        {
          name: "斯里兰卡科伦坡港口连接线",
          coords: [
            [79.8512, 6.9344],
            [80.3176, 7.2906],
          ],
          type: "公路",
          status: "建设中",
          length: "29公里",
        },

        // 西亚及中东区域线路
        {
          name: "伊朗-伊拉克铁路",
          coords: [
            [48.5164, 35.8617],
            [45.7579, 33.2237],
            [44.3661, 33.3152],
          ],
          type: "铁路",
          status: "规划中",
          length: "950公里",
        },
        {
          name: "沙特-约旦-叙利亚公路",
          coords: [
            [46.7219, 24.7136],
            [36.2384, 30.5852],
            [36.7812, 33.5138],
          ],
          type: "公路",
          status: "规划中",
          length: "1800公里",
        },
        {
          name: "土耳其-叙利亚-伊拉克石油管道",
          coords: [
            [32.8597, 39.9334],
            [36.7812, 33.5138],
            [44.3661, 33.3152],
          ],
          type: "管道",
          status: "规划中",
          length: "1500公里",
        },

        // 新增海运线路
        {
          name: "波斯湾-红海海运线",
          coords: [
            [55.1694, 25.2582],
            [45.0355, 12.5737],
          ],
          type: "海运",
          status: "运营中",
          length: "1900公里",
        },
        {
          name: "地中海-埃及亚历山大港",
          coords: [
            [29.9553, 31.2304],
            [31.2001, 30.0444],
          ],
          type: "海运",
          status: "运营中",
          length: "250公里",
        },

        // 新增航空线路 (中国起点均为西部城市)
        {
          name: "乌鲁木齐-德黑兰-伊斯坦布尔航线",
          coords: [
            [87.6271, 43.8255],
            [51.389, 35.6892],
            [28.9784, 41.0082],
          ],
          type: "航空",
          status: "运营中",
          length: "6200公里",
        },
        {
          name: "成都-迪拜-开罗航线",
          coords: [
            [104.0665, 30.5728],
            [55.3188, 25.2797],
            [31.2304, 30.0444],
          ],
          type: "航空",
          status: "运营中",
          length: "7800公里",
        },

        // 海湾地区线路
        {
          name: "沙特-科威特-伊拉克公路",
          coords: [
            [46.7219, 24.7136],
            [47.9744, 29.3759],
            [44.3661, 33.3152],
          ],
          type: "公路",
          status: "运营中",
          length: "1200公里",
        },
        {
          name: "阿联酋-阿曼输油管道",
          coords: [
            [55.1694, 25.2582],
            [58.5922, 23.6101],
          ],
          type: "管道",
          status: "建设中",
          length: "370公里",
        },
        {
          name: "卡塔尔-沙特跨海大桥",
          coords: [
            [51.531, 25.2854],
            [49.8944, 26.2541],
          ],
          type: "公路",
          status: "建设中",
          length: "40公里",
        },
      ];
      this.mockData = [
        {
          id: 7,
          title: "中吉乌铁路中方起点站",
          category: "railway_station",
          longitude: 82.9204,
          latitude: 41.3396,
          content:
            "中吉乌铁路在中国境内的起点站，位于新疆喀什，是中国连接中亚的新通道。",
          constructionTime: "2023-12-01 10:00:00",
        },
        {
          id: 8,
          title: "川藏铁路拉萨站",
          category: "railway_station",
          longitude: 91.1141,
          latitude: 29.6516,
          content:
            "川藏铁路的终点站，位于西藏拉萨，是世界上海拔最高的铁路枢纽之一。",
          constructionTime: "2026-06-30 15:00:00",
        },
        {
          id: 9,
          title: "中国-中亚天然气管道D线入境站",
          category: "building",
          longitude: 83.4512,
          latitude: 42.0281,
          content:
            "中亚天然气管道D线进入中国的首站，位于新疆伊犁，为中国提供清洁能源。",
          constructionTime: "2019-09-01 09:00:00",
        },
        {
          id: 10,
          title: "中缅原油管道末站",
          category: "port",
          longitude: 94.7325,
          latitude: 19.7633,
          content:
            "中缅原油管道的终点站，位于中国云南瑞丽，连接印度洋石油产区。",
          constructionTime: "2017-04-20 11:30:00",
        },
        {
          id: 11,
          title: "中巴喀喇昆仑公路边境口岸",
          category: "building",
          longitude: 75.9812,
          latitude: 35.1484,
          content:
            "中巴喀喇昆仑公路上的重要边境口岸，促进中国与巴基斯坦的贸易往来。",
          constructionTime: "2009-10-25 14:00:00",
        },
        {
          id: 12,
          title: "中国-巴基斯坦光缆枢纽站",
          category: "building",
          longitude: 77.5946,
          latitude: 39.0742,
          content:
            "连接中国与巴基斯坦的跨境光缆的核心枢纽站，位于新疆乌鲁木齐。",
          constructionTime: "2018-01-18 10:45:00",
        },
        {
          id: 13,
          title: "印度-孟加拉国公路边境检查站",
          category: "building",
          longitude: 88.3639,
          latitude: 22.5726,
          content:
            "印度与孟加拉国边境公路上的检查站，是两国人员和货物往来的重要关口。",
          constructionTime: "2013-08-12 09:30:00",
        },
        {
          id: 14,
          title: "斯里兰卡科伦坡港口城",
          category: "building",
          longitude: 79.8512,
          latitude: 6.9344,
          content:
            "位于科伦坡港口的大型综合发展项目，包括港口、商业区和住宅区。",
          constructionTime: "2022-05-20 16:00:00",
        },
        {
          id: 15,
          title: "伊朗-伊拉克铁路边境站",
          category: "railway_station",
          longitude: 48.5164,
          latitude: 35.8617,
          content: "规划中的伊朗-伊拉克铁路边境站，将加强两国间的铁路连接。",
          constructionTime: "2028-01-01 00:00:00",
        },
        {
          id: 16,
          title: "沙特-约旦-叙利亚公路起点",
          category: "building",
          longitude: 46.7219,
          latitude: 24.7136,
          content:
            "规划中的跨国公路起点，位于沙特阿拉伯，旨在促进中东地区互联互通。",
          constructionTime: "2030-06-30 00:00:00",
        },
        {
          id: 17,
          title: "土耳其-叙利亚-伊拉克石油管道终点站",
          category: "building",
          longitude: 44.3661,
          latitude: 33.3152,
          content:
            "规划中的石油管道终点站，位于伊拉克巴士拉，连接土耳其与波斯湾。",
          constructionTime: "2029-12-31 00:00:00",
        },
        {
          id: 18,
          title: "波斯湾-红海海运线集装箱码头",
          category: "port",
          longitude: 55.1694,
          latitude: 25.2582,
          content:
            "位于阿联酋迪拜的集装箱码头，是波斯湾通往红海的重要海运枢纽。",
          constructionTime: "2005-09-01 12:00:00",
        },
        {
          id: 19,
          title: "埃及亚历山大港",
          category: "port",
          longitude: 29.9553,
          latitude: 31.2304,
          content: "埃及在地中海岸的重要港口，是非洲与欧洲贸易的门户。",
          constructionTime: "1805-01-01 00:00:00",
        },
        {
          id: 20,
          title: "乌鲁木齐地窝堡国际机场",
          category: "airport",
          longitude: 87.6271,
          latitude: 43.8255,
          content:
            "中国西部重要的国际航空枢纽，是连接中国与中亚、西亚的空中桥梁。",
          constructionTime: "1970-01-01 00:00:00",
        },
        {
          id: 21,
          title: "成都双流国际机场",
          category: "airport",
          longitude: 104.0665,
          latitude: 30.5728,
          content:
            "中国中西部地区最繁忙的国际机场，是“一带一路”空中丝绸之路的起点。",
          constructionTime: "1956-01-01 00:00:00",
        },
        {
          id: 22,
          title: "沙特-科威特-伊拉克公路边境口岸",
          category: "building",
          longitude: 46.7219,
          latitude: 24.7136,
          content:
            "位于沙特与科威特边境的公路口岸，是海湾地区重要的陆上交通节点。",
          constructionTime: "2000-05-10 10:00:00",
        },
        {
          id: 23,
          title: "阿联酋-阿曼输油管道终点站",
          category: "building",
          longitude: 58.5922,
          latitude: 23.6101,
          content: "阿联酋至阿曼输油管道的终点站，位于阿曼首都马斯喀特附近。",
          constructionTime: "2024-03-15 14:00:00",
        },
        {
          id: 24,
          title: "卡塔尔-沙特跨海大桥",
          category: "building",
          longitude: 51.531,
          latitude: 25.2854,
          content:
            "连接卡塔尔与沙特阿拉伯的跨海大桥，是海湾地区重要的基础设施。",
          constructionTime: "2027-12-01 00:00:00",
        },
      ];
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.infrastructure-visualization {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #030b1f 0%, #0a1e3a 50%, #030b1f 100%);
  padding: 20px;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        circle at 20% 30%,
        rgba(30, 144, 255, 0.1) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 70%,
        rgba(30, 144, 255, 0.08) 0%,
        transparent 50%
      );
    animation: pulse 8s ease-in-out infinite;
    pointer-events: none;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
  }
}

.iv_header {
  width: 98%;
  margin: 0 auto 20px;
  height: 110px;
  background-image: url("../../assets/images/lsdrsj_bg.png");
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;

  .ivh_left {
    float: left;
    height: 100%;

    .title {
      font-family: SourceHanSansSC-Bold;
      font-size: 16px;
      color: #ffffff;
      letter-spacing: 0;
      padding-left: 30px;
      padding-top: 20px;
    }

    .nums {
      font-family: LetsgoDigital-Regular;
      font-size: 36px;
      color: #ffffff;
      letter-spacing: 0;
      text-shadow: 0 2px 5px rgba(2, 0, 70, 0.5);
      font-weight: 700;
      margin-top: 10px;
      padding-left: 30px;
      background-image: linear-gradient(to bottom, #ffffff, #ccd8f2, #3869cc);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      animation: numberGlow 2s ease-in-out infinite;
    }
  }

  .ivh_right {
    float: right;
  }
}

@keyframes numberGlow {
  0%,
  100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.3);
  }
}

.time-range-section {
  width: 98%;
  margin: 0 auto 20px;
  padding: 25px;
  background: rgba(27, 126, 242, 0.12);
  border: 1px solid rgba(30, 144, 255, 0.4);
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 30px;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 15px rgba(30, 144, 255, 0.15);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #1e90ff, transparent);
  }

  .time-range-item {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;

    .range-label {
      font-size: 14px;
      color: #ffffff;
      white-space: nowrap;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 5px;

      i {
        color: #1e90ff;
      }
    }

    .time-picker {
      flex: 1;
      background: transparent;
      border: 1px solid #ffffff1f;
    }

    .data-count-tag {
      flex-shrink: 0;
      font-weight: 600;
    }
  }

  .time-comparison-switch {
    flex-shrink: 0;
    display: flex;
    gap: 10px;
  }
}
.num2Map {
  // width: 1200px;
  height: 500px !important;
}

.map-container {
  width: 1200px;
  height: 1000px;
  margin: 0 auto 30px;
  background: rgba(10, 30, 58, 0.5);
  border: 1px solid rgba(30, 144, 255, 0.4);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(30, 144, 255, 0.25);
  position: relative;
  z-index: 1;

  .map-status-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(10, 30, 58, 0.9);
    border: 1px solid rgba(30, 144, 255, 0.5);
    border-radius: 8px;
    padding: 12px 18px;
    z-index: 10;
    display: flex;
    gap: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);

    .status-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        animation: blink 2s ease-in-out infinite;

        &.building-dot {
          background: #00ffff;
          box-shadow: 0 0 10px #00ffff;
        }

        &.road-dot {
          background: #10b981;
          box-shadow: 0 0 10px #10b981;
        }
      }

      .status-text {
        color: #ffffff;
        font-size: 13px;
        font-weight: 500;
      }

      .status-badge {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: #ffffff;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.4);
      }
    }

    @keyframes blink {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0.4;
      }
    }
  }
}

.data-table-section {
  width: 98%;
  margin: 0 auto;
  background: rgba(27, 126, 242, 0.12);
  border: 1px solid rgba(30, 144, 255, 0.4);
  border-radius: 10px;
  padding: 25px;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 15px rgba(30, 144, 255, 0.15);

  .table-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h3 {
      font-size: 18px;
      color: #ffffff;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #1e90ff;
      }
    }

    .table-filters {
      display: flex;
      gap: 15px;
    }
  }
}

.title-with-icon {
  display: flex;
  align-items: center;
  gap: 6px;

  i {
    color: #1e90ff;
  }
}

.coords-display {
  .coord-item {
    font-size: 12px;
    margin: 2px 0;

    .coord-label {
      color: rgba(255, 255, 255, 0.6);
      margin-right: 4px;
    }

    .coord-value {
      color: #00ffff;
      font-family: monospace;
    }
  }
}

.public_button {
  display: inline-block;
  padding: 8px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;

  &.search_btn {
    background: linear-gradient(135deg, #1e90ff 0%, #0066cc 100%);
    color: #ffffff;
    border: 1px solid #1e90ff;

    &:hover {
      background: linear-gradient(135deg, #0066cc 0%, #004d99 100%);
      box-shadow: 0 4px 15px rgba(30, 144, 255, 0.4);
      transform: translateY(-2px);
    }
  }

  &.clear_button {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.position_add_btn {
  margin-right: 30px;
  margin-top: 32px;
}

.position_btn {
  margin-right: 10px;
  margin-bottom: 10px;
}

.mr10 {
  margin-right: 10px;
}

.fr {
  float: right;
}

.edit-btn {
  color: #1e90ff !important;

  &:hover {
    color: #00a8ff !important;
  }
}

.delete-btn {
  color: #f56c6c !important;

  &:hover {
    color: #ff4444 !important;
  }
}
</style>

<style lang="scss">
.infrastructure-dialog {
  .el-dialog {
    background: linear-gradient(180deg, #0a1e3a 0%, #030b1f 100%);
    border: 1px solid rgba(30, 144, 255, 0.4);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);

    .el-dialog__header {
      border-bottom: 1px solid rgba(30, 144, 255, 0.3);
      padding: 20px;

      .el-dialog__title {
        color: #ffffff;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .el-dialog__body {
      padding: 30px 20px;
    }

    .el-dialog__footer {
      border-top: 1px solid rgba(30, 144, 255, 0.3);
      padding: 15px 20px;
    }
  }

  .dialogTitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin-bottom: 25px;
    line-height: 1.6;
    display: flex;
    align-items: flex-start;
    gap: 8px;

    i {
      color: #1e90ff;
      margin-top: 2px;
    }
  }

  .dialog_form_style {
    .el-form-item__label {
      color: #ffffff;
      font-weight: 500;
    }

    .el-input__inner,
    .el-textarea__inner {
      background: rgba(27, 126, 242, 0.12);
      border: 1px solid rgba(30, 144, 255, 0.3);
      color: #ffffff;

      &::placeholder {
        color: rgba(255, 255, 255, 0.3);
      }

      &:focus {
        border-color: #1e90ff;
        box-shadow: 0 0 10px rgba(30, 144, 255, 0.3);
      }
    }

    .el-input-group__append {
      background: rgba(30, 144, 255, 0.2);
      border: 1px solid rgba(30, 144, 255, 0.3);
      color: #1e90ff;
    }

    .el-radio {
      color: #ffffff;

      &.is-bordered {
        border-color: rgba(30, 144, 255, 0.3);
        background: rgba(27, 126, 242, 0.08);
        padding: 12px 20px;
        margin-right: 15px;
        display: flex;
        float: left;

        &:hover {
          border-color: #1e90ff;
        }

        &.is-checked {
          border-color: #1e90ff;
          background: rgba(30, 144, 255, 0.2);
        }
      }
    }

    .el-radio__label {
      color: #ffffff;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .el-radio__input.is-checked + .el-radio__label {
      color: #00ffff;
    }
  }

  .coordinate-section {
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(30, 144, 255, 0.08);
    border: 1px solid rgba(30, 144, 255, 0.2);
    border-radius: 8px;

    .section-title {
      color: #ffffff;
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #1e90ff;
      }
    }

    .el-form-item {
      margin-bottom: 15px;
    }
  }

  .category-hint {
    margin-top: 10px;

    .hint-text {
      color: rgba(255, 255, 255, 0.6);
      font-size: 13px;
      display: flex;
      align-items: center;
      gap: 6px;

      i {
        color: #1e90ff;
      }
    }
  }
}

.data-table-section {
  .el-table {
    background: transparent;
    color: #ffffff;

    &::before {
      display: none;
    }

    th {
      background: rgba(30, 144, 255, 0.18);
      color: #ffffff;
      border-color: rgba(30, 144, 255, 0.25);
      font-weight: 600;
    }

    tr {
      background: transparent;

      &.building-row {
        background: rgba(0, 255, 255, 0.03);
      }

      &.road-row {
        background: rgba(16, 185, 129, 0.03);
      }

      &:hover > td {
        background: rgba(30, 144, 255, 0.12) !important;
      }
    }

    td {
      border-color: rgba(30, 144, 255, 0.15);
    }

    .cell {
      color: #ffffff;
    }
  }

  .el-table__empty-text {
    color: rgba(255, 255, 255, 0.6);
  }

  .el-radio-group {
    .el-radio-button__inner {
      background: rgba(27, 126, 242, 0.12);
      border-color: rgba(30, 144, 255, 0.3);
      color: #ffffff;

      &:hover {
        color: #00ffff;
      }
    }

    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: linear-gradient(135deg, #1e90ff, #0066cc);
      border-color: #1e90ff;
      box-shadow: 0 2px 10px rgba(30, 144, 255, 0.4);
    }
  }
}

.el-date-editor {
  .el-range-separator {
    color: #ffffff;
  }

  .el-range-input {
    background: transparent;
    color: #ffffff;

    &::placeholder {
      color: rgba(255, 255, 255, 0.4);
    }
  }

  .el-range__icon {
    color: #1e90ff;
  }
}

.el-zoom-in-top-enter-active,
.el-zoom-in-top-leave-active {
  opacity: 1;
  transform: scaleY(1);
  transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1),
    opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
  transform-origin: center top;
}

.el-zoom-in-top-enter,
.el-zoom-in-top-leave-active {
  opacity: 0;
  transform: scaleY(0);
}
</style>
