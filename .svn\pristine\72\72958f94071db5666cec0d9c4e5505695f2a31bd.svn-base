<template>
  <div class="app-container">
    <el-row :gutter="20" class="main-row">
      <el-col :span="6" class="left-panel">
        <el-card shadow="always" class="left-card">
          <div class="history_log">请选择主题</div>
          <el-select v-model="currentTopicId" placeholder="请选择主题" @change="handleTopicChange" style="width:333px">
            <el-option v-for="topic in topicOptions" :key="topic.id" :label="topic.categoryName" :value="topic.id" />
          </el-select>
          <h4>专题列表</h4>
          <el-input v-model="searchTopic" placeholder="搜索专题名称" clearable size="small" prefix-icon="el-icon-search"
            class="search-input" @keyup.enter="fetchThematicList" @input="handleSearchInput" />
          <div class="topic-list-wrapper">
            <div v-for="topic in thematicList" :key="topic.id" class="topic-item"
              :class="{ 'topic-item-active': currentThematicId === topic.id }" @click="handleThematicSelect(topic)">
              <div class="topic-info">
                <div class="topic-name">{{ topic.categoryName }}</div>
                <div class="topic-count">{{ topic.count || 0 }} 条数据</div>
              </div>
            </div>
            <div class="topic-empty" v-if="thematicList.length === 0">
              暂无专题数据
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="18" class="right-panel">
        <div class="page-header">
          <h3 class="page-title">堆下水桩土理货分析</h3>
          <div class="page-tabs">
            <span class="tab-item" @click="to_index">趋势总览</span>
            <span class="tab-item active">可视化时间轴</span>
            <span class="tab-item" @click="to_gjjs">高级检索</span>
            <!-- <span class="tab-item" @click="to_ztbg">专题报告</span> -->
          </div>
        </div>

        <div class="vision_time_line">
          <div class="timeline-container">
            <div class="timeline-header">
              <h3>项目时间轴</h3>
              <div class="timeline-controls">
                <el-button type="primary" icon="el-icon-plus" @click="openAddDialog">添加阶段</el-button>
                <el-button icon="el-icon-refresh" @click="autoGenerateNodes">重新汇聚</el-button>
              </div>

            </div>
            <!-- <div style="margin-left: 848px;">
              <el-button type="primary" icon="el-icon-plus" @click="openAddDialog">展示全部</el-button>
              <el-button icon="el-icon-refresh" @click="autoGenerateNodes">展示主要事件</el-button>
            </div> -->
            <el-radio-group v-model="lineChartType" size="mini">
                      <el-radio-button label="展示全部">展示全部</el-radio-button>
                      <el-radio-button label="展示主要事件">展示主要事件</el-radio-button>
                    </el-radio-group>

            <!-- 新版时间线组件 - 节点居中在线上 -->
            <div class="phase-timeline">
              <div v-for="(phase, phaseIndex) in phaseData" :key="phaseIndex" class="phase-section">
                <div class="phase-header" :style="{ borderLeftColor: phase.color }">
                  <div class="phase-title">{{ phase.name }} ({{ phase.period }})</div>
                  <div v-if="phase.mark" class="stage-indicator">{{ phase.mark }}</div>
                </div>

                <div class="timeline" v-if="phase.events && phase.events.length > 0">
                  <div v-for="(event, eventIndex) in phase.events" :key="eventIndex" class="timeline-item"
                    :class="eventIndex % 2 === 0 ? 'left' : 'right'">
                    <!-- 节点始终在中间线上 -->
                    <div class="timeline-node-container">
                      <div class="timeline-node" :style="{ backgroundColor: phase.color }"></div>
                    </div>
                    <div class="timeline-content" :style="{ borderColor: phase.color }">
                      <el-card class="timeline-card" shadow="hover">
                        <div class="timeline-header-content">
                          <div class="event-date-type">
                            <span class="timeline-date">{{ event.date }}</span>
                            <span class="event-type">{{ event.type }}</span>
                          </div>

                        </div>
                        <h4 class="event-name">{{ event.name }}</h4>
                        <p class="timeline-desc">{{ event.description }}</p>
                      </el-card>
                    </div>
                  </div>
                </div>

                <div v-else class="empty-phase">
                  <i class="el-icon-time empty-icon"></i>
                  <p>暂无事件数据</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 添加时间轴节点对话框 -->
      <el-dialog :title="nodeDialogTitle" :visible.sync="nodeDialogVisible" width="600px" append-to-body>
        <el-form ref="nodeForm" :model="nodeForm" :rules="nodeRules" label-width="100px">
          <el-form-item label="阶段名称" prop="type">
            <el-input v-model="nodeForm.type" placeholder="请输入阶段名称" />
          </el-form-item>
          <el-form-item label="起止日期" prop="dateRange">
            <el-date-picker style="width: 440px;" v-model="nodeForm.dateRange" value-format="yyyy-MM-dd"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="nodeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitNodeForm">确定</el-button>
        </div>
      </el-dialog>
    </el-row>
  </div>
</template>

<script>
import commonUpload from "@/views/components/commonUpload.vue";
import {
  selectListBySystemType, // 获取所有主题
  selectThematicListById, // 根据主题查找专题
  selectTopicDetailById, // 根据主题ID获取专题详情
} from "@/api/historyanalysis/index";
// addjd
// import {
//   addjd,
//   queryjd,
// } from "@/api/historyanalysis/vision-time-line";
export default {
  components: { commonUpload },
  name: "Role",


  data() {
    return {
      lineChartType: "展示全部",

      // 新版时间轴数据 - 按阶段分组
      phaseData: [
        {
          id: 1,
          name: "工程建设前期",
          period: "2023.04-2023.06",
          // mark: "深圳阶段",
          color: "#409EFF",
          events: []
        },
        {
          id: 2,
          name: "工程建设准备",
          period: "2023.07-2023.11",
          // mark: "",
          color: "#67C23A",
          events: []
        },
        {
          id: 3,
          name: "工程建设实施",
          period: "2023.12-2026.07",
          // mark: "",
          color: "#E6A23C",
          events: []
        },
        {
          id: 4,
          name: "工程验收",
          period: "2026.08-2026.11",
          // mark: "",
          color: "#F56C6C",
          events: []
        }
      ],

      // 时间轴对话框相关
      nodeDialogVisible: false,
      nodeDialogTitle: '添加事件发展阶段',
      editingPhaseIndex: -1,
      editingEventIndex: -1,
      nodeForm: {

        type: '',
        dateRange: [],

      },
      nodeRules: {
        dateRange: [{ required: true, message: '请选择起止日期', trigger: 'change' }],
        type: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],

      },

      // 原有数据保持不变
      datain_record_list: [],
      isExist: true,
      topicOptions: [],
      thematicList: [],
      searchTopic: '',
      currentTopicId: null,
      currentThematicId: null,
      currentThematicName: '',
      typeCount: {
        openSource: 0,
        event: 0,
        daily: 0,
        special: 0,
        research: 0
      },
      activeTab: 'openSource',
      drawer: false,
      previewData: [],
      previewTotal: 0,
      ruleForm: {
        ruleConfigs: [
          {
            fieldName: '',
            operator: '',
            valueList: '',
            id: ''
          }
        ],
        ruleSchedule: {
          frequency: '',
          dayOfWeek: '',
          executeHour: '',
          thematicId: '',
          ruleType: 1,
        }
      },
      fieldOptions: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      operatorOptions: [],
      frequencyTypeOptions: [],
      weekdayOptions: [],
      executeHourOptions: [],
      tableData: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      loading: {
        openSource: false,
        event: false,
        daily: false,
        special: false,
        theory: false
      },
      total: {
        openSource: 0,
        event: 0,
        daily: 0,
        special: 0,
        theory: 0
      },




      openDetailDialog: false,
      open_change: false,
      totalSum: 0,
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      roleList: [],
      title: "",
      open: false,
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      dateRange: [],

      menuOptions: [],
      deptOptions: [],
      queryParams_xq: {
        themeId: undefined,
        mainTopicId: undefined,
        mainCategoryId: undefined,
        mainSentimentId: undefined,
        eventName: undefined,
        countryRegion: undefined,
        locltion: undefined,
        occurrenceTime: undefined,
        duration: undefined,
        eventOverview: undefined,
        source: undefined,
        ourPersonnelOrg: undefined,
        opponentPersonnelOrg: undefined,
        fj: [
          {
            fjid: "",
            fjName: "",
          },
        ],
      },
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        eventName: undefined,
        themeId: undefined,
        mainSentimentId: undefined,
        mainTopicId: undefined,
        mainCategoryId: undefined,
        status: undefined,
      },
      changeid: "",

      defaultProps: {
        children: "children",
        label: "label",
      },

    };
  },
  created() {
    // this.what();
    this.fetchTopicOptions();
    this.fetchFieldOptions();
    this.$nextTick(() => {
      // this.initCharts();
      window.addEventListener("resize", this.handleResize);
    });
  },
  mounted() {
    // 初始化示例数据
    this.autoGenerateNodes();
    this.getnodes(1)
  },
  methods: {
    getnodes(c1) {
      let params = {
        flag: c1,
      }
        queryjd(params).then(res => {
          console.log(res)
          this.phaseData = res.data}
        )

    },
    // 时间轴相关方法
    openAddDialog() {
      this.editingPhaseIndex = -1;
      this.editingEventIndex = -1;
      this.nodeDialogTitle = '添加事件发展阶段';
      this.nodeForm = {
        dateRange: [],
        type: '',

      };
      this.nodeDialogVisible = true;
    },

    submitNodeForm() {
      this.$refs.nodeForm.validate((valid) => {
        if (valid) {
          let params = {
            stageName: this.nodeForm.type,

          };
          addjd(
            this.addDateRange(params, this.nodeForm.dateRange)
          )
        }

      });
    },
    // if (valid) {};
    //     const phaseIndex = this.phaseData.findIndex(phase => phase.id === this.nodeForm.phaseId);
    //     if (phaseIndex === -1) return;

    //     const eventData = {
    //       date: this.nodeForm.date,
    //       type: this.nodeForm.type,
    //       name: this.nodeForm.name,
    //       description: this.nodeForm.description
    //     };

    //     if (this.editingPhaseIndex === -1) {
    //       // 添加新节点
    //       this.phaseData[phaseIndex].events.push(eventData);
    //     } else {
    //       // 编辑现有节点
    //       this.phaseData[this.editingPhaseIndex].events.splice(this.editingEventIndex, 1, eventData);
    //     }

    //     // 按日期排序每个阶段的事件
    //     this.phaseData.forEach(phase => {
    //       phase.events.sort((a, b) => new Date(a.date) - new Date(b.date));
    //     });

    //     this.nodeDialogVisible = false;
    //     this.$message.success(this.editingPhaseIndex === -1 ? '添加成功' : '修改成功');
    //   }
    // });
    // },

    // editNode(phaseIndex, eventIndex) {
    //   this.editingPhaseIndex = phaseIndex;
    //   this.editingEventIndex = eventIndex;
    //   this.nodeDialogTitle = '编辑时间轴节点';
    //   const event = this.phaseData[phaseIndex].events[eventIndex];
    //   this.nodeForm = {
    //     phaseId: this.phaseData[phaseIndex].id,
    //     date: event.date,
    //     type: event.type,
    //     name: event.name,
    //     description: event.description
    //   };
    //   this.nodeDialogVisible = true;
    // },

    // deleteNode(phaseIndex, eventIndex) {
    //   this.$confirm('确定要删除这个节点吗？', '提示', {
    //     type: 'warning'
    //   }).then(() => {
    //     this.phaseData[phaseIndex].events.splice(eventIndex, 1);
    //     this.$message.success('删除成功');
    //   });
    // },

    autoGenerateNodes() {
      // 自动生成示例数据
      this.phaseData[0].events = [
        {
          date: '2023.04',
          type: '重新推进',
          name: '项目立项与可行性研究',
          description: '完成项目立项审批，进行可行性研究报告编制与评审，确定项目技术路线和实施方案。'
        },
        {
          date: '2023.05',
          type: '规划设计',
          name: '初步设计与技术方案',
          description: '完成初步设计和技术方案制定，组织专家评审，确定项目关键技术参数和性能指标。'
        },
        {
          date: '2023.06',
          type: '审批准备',
          name: '各项审批材料准备',
          description: '准备环评、用地、规划等各项审批材料，协调相关部门推进审批流程。'
        }
      ];

      this.phaseData[1].events = [
        {
          date: '2023.07',
          type: '招标采购',
          name: '施工与设备采购招标',
          description: '发布施工总承包和设备采购招标公告，组织招标评审，确定中标单位。'
        },
        {
          date: '2023.08',
          type: '合同签订',
          name: '各类合同签订与备案',
          description: '与中标单位签订施工、设备采购等合同，完成合同备案手续。'
        }
      ];

      this.phaseData[2].events = [
        {
          date: '2023.12',
          type: '基础施工',
          name: '桩基工程与基础施工',
          description: '开始桩基工程施工，完成基础开挖、浇筑等基础工程作业。'
        },
        {
          date: '2024.03',
          type: '主体施工',
          name: '主体结构施工',
          description: '开始主体结构施工，按计划推进各分部工程施工，确保工程质量。'
        }
      ];

      this.phaseData[3].events = [
        {
          date: '2026.08',
          type: '预验收',
          name: '工程预验收与问题整改',
          description: '组织预验收，发现并整改存在的问题，为正式验收做好准备。'
        }
      ];
    },

    // 原有方法保持不变
    handleThematicSelect(topic) {
      this.currentThematicId = topic.id;
      this.loadTimelineData(topic.id);
    },

    loadTimelineData(thematicId) {
      // 根据专题ID加载对应的时间轴数据
      console.log('加载专题时间轴数据:', thematicId);
    },

    fetchFieldOptions() {
      // 原有实现
    },

    fetchTopicOptions() {
      const params = { systemType: 1 };
      selectListBySystemType(params)
        .then(response => {
          if (response?.code === 200) {
            this.topicOptions = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名主题'
            }));
            if (this.topicOptions.length > 0) {
              this.currentTopicId = this.topicOptions[0].id;
              this.fetchThematicList();
            }
          } else {
            this.topicOptions = [];
          }
        })
        .catch(error => {
          console.error('获取主题列表失败:', error);
          this.$message.error('获取主题列表失败，请稍后重试');
          this.topicOptions = [];
        });
    },

    handleSearchInput() {
      this.fetchThematicList();
    },

    fetchThematicList() {
      const params = {
        id: this.currentTopicId,
        searchName: this.searchTopic,
        ruleType: ''
      };
      selectThematicListById(params)
        .then(response => {
          if (response?.code === 200) {
            this.thematicList = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名专题',
              count: item.count || 0
            }));
            if (this.thematicList.length > 0) {
              this.currentThematicId = this.thematicList[0].id;
              this.currentThematicName = this.thematicList[0].categoryName;
              this.ruleForm.ruleSchedule.thematicId = this.currentThematicId;
            } else {
              this.currentThematicId = null;
              this.currentThematicName = '';
              this.ruleForm.ruleSchedule.thematicId = '';
              this.typeCount = { openSource: 0, event: 0, daily: 0, special: 0, research: 0 };
            }
          } else {
            this.thematicList = [];
            this.currentThematicId = null;
            this.currentThematicName = '';
            this.ruleForm.ruleSchedule.thematicId = '';
          }
        })
        .catch(error => {
          console.error('获取专题列表失败:', error);
          this.$message.error('获取专题列表失败，请稍后重试');
          this.thematicList = [];
          this.currentThematicId = null;
          this.currentThematicName = '';
          this.ruleForm.ruleSchedule.thematicId = '';
        });
    },

    handleTopicChange() {
      this.fetchThematicList();
    },

    async handleThematicSelect(data) {
      this.currentThematicId = data.id;
      const params = {
        id: this.currentThematicId,
      };
      let response = await selectTopicDetailById(params);
      if (response.code === 200) {
        this.description = response.data.description;
        this.daily = response.data.daily;
        this.event = response.data.event;
        this.openSource = response.data.openSource;
        this.research = response.data.research;
        this.special = response.data.special;
        this.timespan = response.data.timespan;
        this.timespanStartEnd = response.data.timespanStartEnd;
        this.peakTime = response.data.peakTime;
        this.peakTimeCount = response.data.peakTimeCount;
        this.keyPositiveEvents = response.data.keyPositiveEvents;
        this.keyPositiveEventsTime = response.data.keyPositiveEventsTime;
        this.keyPositiveEventsPopularity = response.data.keyPositiveEventsPopularity;
        this.keyNegativeEvents = response.data.keyNegativeEvents;
        this.keyNegativeEventsTime = response.data.keyNegativeEventsTime;
        this.keyNegativeEventsPopularity = response.data.keyNegativeEventsPopularity;
      }
    },

    to_ksh() {
      this.$router.push("/concract/keypage");
    },
    to_gjjs() {
      this.$router.push("/concract/gjjs");
    },
    to_ztbg() {
      this.$router.push("/concract/ztbg");
    },
    to_index() {
      this.$router.push("/history/index");
    },
    // what() {
    //   console.log(this.dict.type.manual_main_sentiment);
    // },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>

<style scoped>
/* 新版时间轴样式 - 左右分布 */
.phase-timeline {
  position: relative;
}

.phase-section {
  margin-bottom: 40px;
}

.phase-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 15px 20px;
  background: #f8fafc;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
}

.phase-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.stage-indicator {
  display: inline-block;
  background: #f0f7ff;
  color: #409EFF;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 15px;
}

.timeline {
  position: relative;
  padding: 20px 0;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e6e9f0;
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.timeline-item.left {
  justify-content: flex-start;
}

.timeline-item.right {
  justify-content: flex-end;
}

.timeline-content {
  width: 45%;
  position: relative;
}


.timeline-item.left .timeline-node {
  right: -8px;
}

.timeline-item.right .timeline-node {
  left: -8px;
}

.timeline-card {
  background: white;
  border-radius: 6px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-top: 3px solid #409EFF;
  transition: all 0.3s ease;
}



.timeline-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.timeline-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.event-date-type {
  display: flex;
  align-items: center;
  gap: 10px;
}


/* 原有样式保持 */
.page-header {
  margin-bottom: 20px;
  /* background: rgba(255, 255, 255, 0.95); */
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 16px 0;
}

.page-tabs {
  display: flex;
  gap: 8px;
}

.tab-item {
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
  background: #f5f7fa;
}

.tab-item:hover {
  background: #e8eaf0;
  color: #409eff;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 500;
}

.vision_time_line {
  padding: 20px;
  /* background:page-header; */
  background: rgba(27,126,242,0.10);
  border-radius: 4px;
  min-height: 500px;
}

.timeline-container {
  max-width: 100%;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.timeline-header h3 {
  margin: 0;
  color: #303133;
  font-size: 20px;
}

.left-card {
  height: 100%;
  background: rgba(27,126,242,0.10);
  border:none;

}

.topic-list-wrapper {
  max-height: 400px;
  overflow-y: auto;
  margin-top: 15px;
}

.topic-item {
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.topic-item:hover {
  border-color: #409EFF;
  background-color: #f5f7fa;
}

.topic-item-active {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.topic-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.topic-name {
  font-weight: 500;
}

.topic-count {
  font-size: 12px;
  color: #909399;
}

.topic-empty {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline::before {
    left: 30px;
  }

  .timeline-item {
    justify-content: flex-start !important;
  }

  .timeline-content {
    width: calc(100% - 60px);
    margin-left: 40px;
  }



  .phase-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

}

/* 节点容器 - 始终居中 */
.timeline-node-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timeline-node {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #409EFF;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-content {
  width: 45%;
  position: relative;
}


.timeline-item.left .timeline-card {
  /* border-left: 3px solid #409EFF; */
  border-top: none;
}

.timeline-item.right .timeline-card {
  border-right: 3px solid #409EFF;
  border-top: none;
}

.timeline-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.timeline-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.event-date-type {
  display: flex;
  align-items: center;
  gap: 10px;
}

.timeline-date {
  font-weight: bold;
  color: #409EFF;
  font-size: 14px;
}

.event-type {
  background: #e6f7ff;
  color: #1890ff;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.timeline-actions {
  display: flex;
  gap: 8px;
}

.event-name {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133;
}

.timeline-desc {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.empty-phase {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  background: #fafafa;
  border-radius: 6px;
  border: 1px dashed #dcdfe6;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
  display: block;
}
.left-panel{
  background: rgba(27,126,242,0.10);
}
</style>