<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:user:add']"
          >新增</el-button
        >
      </el-col>
    </el-row>
    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      title="特殊情形考核添加"
      :visible.sync="open"
      width="1000px"
      append-to-body
    >
      <el-input
        v-model="deptName"
        placeholder="请输入部门名称"
        clearable
        size="small"
        prefix-icon="el-icon-search"
        style="margin-bottom: 20px; width: 200px; margin-right: 10px"
      /><el-button
        type="primary"
        icon="el-icon-search"
        size="mini"
        @click="handleQuery"
        >搜索</el-button
      >
      <div class="content-wrapper">
        <splitpanes
          :horizontal="this.$store.getters.device === 'mobile'"
          class="default-theme"
        >
          <pane size="20">
            <div class="department-list">
              <el-tree
                :data="deptConfig.data"
                :props="deptConfig.props"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="tree"
                node-key="id"
                default-expand-all
                highlight-current
                @node-click="handleNodeClick"
                class="treeh"
              >
                <template #default="{ node, data }">
                  <span class="custom-tree-node">
                    <div v-if="isFirstRootNode(node)">
                      <span class="prefix-img eltree-text"
                        ><img src="./img/tree1.png" alt="" />
                        <span style="margin-left: 10px">{{ node.label }}</span>
                      </span>
                    </div>
                    <span v-else-if="!isFirstRootNode(node)" class="eltree-con">
                      <div style="display: inline-block">
                        <img
                          src="./img/lx.png"
                          alt=""
                          style="width: 8px; height: 8px; margin-right: 20px"
                        />
                      </div>
                      {{ node.label }}</span
                    >
                    <!-- <span v-else-if="!isLastLevelNode(node)" class="eltree-con">{{
                node.label
              }}</span> -->
                  </span>
                </template>
              </el-tree>
            </div>
          </pane>
          <pane size="80">
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
              <el-form-item label="姓名" prop="xm">
                <el-input v-model="form.xm" disabled maxlength="30" />
              </el-form-item>
              <el-form-item label="处室" prop="cs">
                <el-input v-model="form.cs" placeholder="请输入处室" />
              </el-form-item>
              <el-form-item label="职务职称" prop="zwzc">
                <el-input v-model="form.zwzc" placeholder="请输入职务职称" />
              </el-form-item>
              <el-form-item label="廉评分" prop="lpf">
                <el-input-number
                  v-model="form.lpf"
                  controls-position="right"
                  :min="0"
                  :max="100"
                />
              </el-form-item>
              <el-form-item label="评分说明">
                <el-input
                  v-model="form.pfsm"
                  type="textarea"
                  placeholder="请输入评分说明"
                ></el-input>
              </el-form-item>
            </el-form>
          </pane>
        </splitpanes>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="特殊情形考核修改"
      :visible.sync="openxg"
      width="600px"
      append-to-body
    >
      <el-form ref="formxg" :model="formxg" :rules="rules" label-width="80px">
        <el-form-item label="姓名" prop="xm">
          <el-input v-model="formxg.xm" disabled maxlength="30" />
        </el-form-item>
        <el-form-item label="处室" prop="cs">
          <el-input v-model="formxg.cs" disabled placeholder="请输入处室" />
        </el-form-item>
        <el-form-item label="职务职称" prop="zwzc">
          <el-input v-model="formxg.zwzc" disabled placeholder="请输入职务职称" />
        </el-form-item>
        <el-form-item label="廉评分" prop="lpf">
          <el-input-number
            v-model="formxg.lpf"
            controls-position="right"
            :min="0"
            :max="100"
          />
        </el-form-item>
        <el-form-item label="评分说明">
          <el-input
            v-model="formxg.pfsm"
            type="textarea"
            placeholder="请输入评分说明"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormxg">确 定</el-button>
        <el-button @click="cancelxg">取 消</el-button>
      </div>
    </el-dialog>
    <el-table :data="dataList">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" prop="xh" width="55" />
      <el-table-column label="处室" align="center" prop="cs" />
      <el-table-column label="姓名" align="center" prop="xm" />
      <!-- <template slot-scope="scope">
          <span>{{ parseTime(scope.row.date, '{y}-{m}-{d}') }}</span>
        </template> -->
      <el-table-column label="职务职级" align="center" prop="zwzc" />
      <el-table-column label="廉评分" align="center" prop="lpf" />
      <el-table-column label="评分说明" align="center" prop="pfsm" />
      <el-table-column label="评分日期" align="center" prop="pfrq" />
      <el-table-column
        label="操作"
        align="center"
        width="160"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:user:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:user:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="page"
      :limit.sync="pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
export default {
  name: "Audit",
  components: { Splitpanes, Pane },
  data() {
    return {
      page: 1,
      pageSize: 10,
      total: 10,
      open: false,
      openxg: false,
      // 所有部门树选项
      deptOptions: undefined,
      // 默认展开的部门
      defaultDept: undefined,
      // 选中的部门
      deptConfig: {
        data: [
          {
            id: 100,
            label: "黑龙江省自然资源厅",
            disabled: false,
            children: [
              {
                id: 101,
                label: "内设机构",
                disabled: false,
                children: [
                  {
                    id: 102,
                    label: "调查处",
                    disabled: false,
                    children: [
                      {
                        id: 104,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 105,
                        label: "王老师",
                        disabled: false,
                      },
                      {
                        id: 106,
                        label: "李老师",
                        disabled: false,
                      },
                      {
                        id: 107,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 108,
                        label: "王老师",
                        disabled: false,
                      },
                      {
                        id: 109,
                        label: "李老师",
                        disabled: false,
                      },
                    ],
                  },
                  {
                    id: 103,
                    label: "权益处",
                    disabled: false,
                    children: [
                      {
                        id: 110,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 111,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 112,
                        label: "王老师",
                        disabled: false,
                      },
                      {
                        id: 113,
                        label: "李老师",
                        disabled: false,
                      },
                      {
                        id: 114,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 115,
                        label: "王老师",
                        disabled: false,
                      },
                      {
                        id: 116,
                        label: "李老师",
                        disabled: false,
                      },
                      {
                        id: 117,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 118,
                        label: "王老师",
                        disabled: false,
                      },
                      {
                        id: 119,
                        label: "李老师",
                        disabled: false,
                      },
                      {
                        id: 120,
                        label: "张老师",
                        disabled: false,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
        props: {
          children: "children",
          label: "label",
        },
      },
      deptName: "",
      form: {
        xm: "",
        cs: "",
        zwzc: "",
        lpf: "",
        pfsm: "",
      },
      formxg: {
        xm: "",
        cs: "",
        zwzc: "",
        lpf: "",
        pfsm: "",
      },
      rules: {
        xm: [
          { required: true, message: "请输入姓名", trigger: "blur" },
        ],
        cs: [
          { required: true, message: "请输入处室", trigger: "blur" },
        ],
        zwzc: [
          { required: true, message: "请输入职务职称", trigger: "blur" },
        ],
        lpf: [
          { required: true, message: "请输入廉评分", trigger: "blur" },
        ],
      },
      selectedDepartment: "",
      searchName: "",
      departments: [
        { value: "调查处", label: "调查处" },
        { value: "利用处", label: "利用处" },
      ],
      dataList: [
        {
          xh: 1,
          cs: "调查处",
          xm: "张三",
          zwzc: "处长",
          lpf: 90,
          pfsm: "优秀",
          pfrq: "2021-01-01",
        },
        {
          xh: 3,
          cs: "利用处",
          xm: "王五",
          zwzc: "处长",
          lpf: 70,
          pfsm: "合格",
          pfrq: "2021-01-03",
        },
      ],
    };
  },
  computed: {
    filteredAuditList() {
      // 简单的过滤逻辑，可以根据选中的部门和姓名进行过滤
      return this.auditList.filter((item) => {
        return (
          (!this.selectedDepartment || item.bm === this.selectedDepartment) &&
          (!this.searchName || item.name.includes(this.searchName))
        );
      });
    },
  },
  methods: {
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 判断是否是第一个根节点
    isFirstRootNode(node) {
      // 假设 deptOptions 是根节点的数组
      // 检查 node 是否是 deptOptions 中的第一个元素
      return node.level === 1;
    },
    // 判断节点是否为最后一级
    isLastLevelNode(node) {
      return !node.childNodes || node.childNodes.length === 0;
    },
    // 判断节点是否为最后一级
    isLastLevelNode1(node) {
      return !node.children || node.children.length === 0;
    },
    handleNodeClick(data) {
      if (this.isLastLevelNode1(data)) {
        // 只有在最后一级节点时才执行操作
        // console.log('点击了最后一级节点:', node);
        // 这里可以添加其他逻辑
        console.log(data);

        this.form.xm = data.label;
      }
    },
    handleQuery() {
      // 执行查询操作，可以在这里添加更复杂的逻辑
    },
    getList() {},
    handleUpdate(row) {
      this.formxg = JSON.parse(JSON.stringify(row));
      this.openxg = true;
    },
    handleDelete(row) {
      // 这里可以添加删除逻辑
      this.$confirm("此操作将永久删除该记录, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // 这里可以添加删除请求
          this.dataList.splice(this.dataList.indexOf(row), 1);
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleAdd() {
      this.open = true;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮
    cancelxg() {
      this.openxg = false;
      this.resetxg();
    },
    // 表单重置
    reset() {
      this.form = {
        xm: "",
        cs: "",
        zwzc: "",
        lpf: "",
        pfsm: "",
      };
      this.resetForm("form");
    },
    // 表单重置
    resetxg() {
      this.formxg = {
        xm: "",
        cs: "",
        zwzc: "",
        lpf: "",
        pfsm: "",
      };
      this.resetForm("formxg");
    },
    submitForm() {},
    submitFormxg() {},
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  background: #ffffff;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}

.department-select {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  line-height: 20px;
  font-weight: 400;
}

::v-deep .el-table th.el-table__cell > .cell {
  font-family: SourceHanSansSC-Bold;
  font-size: 14px;
  // color: #203F78;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 700;
}

::v-deep .el-table tr {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 400;
}

.search-btn {
  background: #1b5dd8;
  border-radius: 16px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

// .content-wrapper {
//   display: flex;
//   gap: 20px;
// }
// .department-list {
//   /* width: 256px; */
//   padding: 10px 20px;
//   /* background: #f5f7fa; */
//   border-radius: 4px;
//   box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.06);
// }

/* 隐藏Element UI el-tree的向下剪头 */
::v-deep .el-tree .el-tree-node__expand-icon.el-icon-caret-right {
  display: none;
}
.custom-tree-node {
  display: flex;
  align-items: center;
}
.prefix-text {
  /* padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px; */
  font-family: SourceHanSansSC-Regular;
  padding: 2.5px 2px;
  margin-right: 7px;
  font-size: 8px;
  color: #09b72b;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
  background: #f3fff0;
  border: 1px solid rgba(9, 183, 43, 1);
  border-radius: 2px;
}
.prefix-img {
  color: white;
  display: flex;
  align-items: center;
  margin-right: 8px;
}
.eltree-text {
  font-family: SourceHanSansSC-Bold;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
  line-height: 24px;
  font-weight: 500;
  width: 207px;
  padding: 5px;
}
.eltree-con {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  line-height: 24px;
  font-weight: 400;
}
::v-deep .el-tree-node__content {
  margin-top: 10px;
  border-bottom: 1px solid #e6f1fc;
}
/* ::v-deep .el-tree-node:last-child{
  border-bottom: 0;
} */

/* 为 el-tree 的第一级节点添加滚动条 */
::v-deep .el-tree .el-tree-node__children {
  overflow-y: auto;
  max-height: 290px; /* 你可以根据需要设置最大高度 */
}

/* 移除第一级节点的边框，以避免滚动条与节点边框混淆 */
::v-deep .el-tree .el-tree-node__content:first-child {
  border-top: none;
}

/* 为树组件容器设置固定宽度 */

/* 隐藏滚动条的整个容器 */
::v-deep .el-tree .el-tree-node__children {
  -ms-overflow-style: none; /* IE 10+ */
  scrollbar-width: none; /* Firefox */
}

/* 针对Webkit内核浏览器的隐藏滚动条样式 */
::v-deep .el-tree .el-tree-node__children::-webkit-scrollbar {
  display: none;
}
</style>
