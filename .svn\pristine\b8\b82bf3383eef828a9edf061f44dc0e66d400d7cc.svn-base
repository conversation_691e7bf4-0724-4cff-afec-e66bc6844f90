<template>
  <div class="app-container">
    <!-- 主布局：左右分栏 -->
    <el-row :gutter="20" class="main-row">
      <!-- 左侧专题列表 -->
      <el-col :span="6" class="left-panel">
        <el-form shadow="always" class="left-card">
          <div>
            <div class="topic-title1">主题</div>
          </div>
          <div class="select-box">
            <el-select v-model="currentTopicId" placeholder="请选择主题" @change="handleTopicChange" clearable
              style="width: 100%;">
              <el-option v-for="topic in topicOptions" :key="topic.id" :label="topic.categoryName" :value="topic.id" />
            </el-select>
          </div>
        </el-form>

        <el-form shadow="always" class="left-card">
          <div>
            <div class="topic-title1">专题列表</div>
            <div class="topic-count-total">共 {{ thematicCount }} 个专题</div>
          </div>
          <el-input v-model="searchTopic" placeholder="搜索专题名称" clearable size="small" prefix-icon="el-icon-search"
            class="search-input" @keyup.enter="fetchThematicList" @input="handleSearchInput" />
          <div class="topic-list-wrapper">
            <div v-for="topic in thematicList" :key="topic.id" class="topic-item"
              :class="{ 'topic-item-active': currentThematicId === topic.id }" @click="handleThematicSelect(topic)">
              <div class="topic-info">
                <div class="topic-name-wrap">
                  <img src="../imgs_erb/icon_zsq.png" class="topic-tag-img">
                  <span class="topic-name">{{ topic.categoryName }}</span>
                </div>
                <div class="topic-title2">{{ topic.count || 0 }} <span class="topic-unit">条数据</span></div>
              </div>
              <el-button class="topic-setting" @click.stop="handleThematicSetting(topic)">
                <i class="el-icon-setting"></i>
              </el-button>
            </div>
            <div class="topic-empty" v-if="thematicList.length === 0">
              暂无专题数据
            </div>
          </div>
        </el-form>
      </el-col>

      <!-- 右侧内容区 -->
      <el-col :span="18" class="right-panel">
        <div shadow="always" class="right-card">
          <!-- 专题标题与说明 -->
          <div class="topic-header" style="text-align: left;">
            <h3 class="topic-title">{{ currentThematicName || '' }}</h3>
            <p class="topic-desc" style="text-align: left;">
              {{ isSettingVisible
              ? '设置自动汇聚规则后，系统将自动匹配符合条件的数据并添加到专题中，新数据会定期更新。'
              : '根据数据汇聚规则，形成面向不同专题的数据资产'
              }}
            </p>
          </div>

          <!-- 状态1：规则配置页 -->
          <div v-if="isSettingVisible" style="text-align: left;">
            <el-tabs v-model="activeTab" type="form" class="content-tabs" @tab-click="handleRuleTabChange">
              <!-- 动态生成Tab页 -->
              <el-tab-pane v-for="tab in tabList" :key="tab.id" :label="`${tab.categoryName} (${tab.count || 0})`"
                :name="tab.id" class="rule-tab"></el-tab-pane>

              <!-- 共用的规则配置模板 -->
              <div class="rule-config">
                <div class="config-section">
                  <h4 style="color: #ffffff;">内容条件 <i class="el-icon-info"></i></h4>
                  <div class="condition-group">
                    <el-form v-for="(condition, index) in currentRule.ruleConfigs" :key="index" class="condition-row">
                      <el-select v-model="condition.fieldName" placeholder="字段" class="field-select"
                        @change="handleFieldChange(condition, index)">
                        <el-option v-for="item in fieldOptions[activeTab]" :key="item.fieldName"
                          :label="item.displayName" :value="item.fieldName"
                          :data-field-type="item.fieldType"></el-option>
                      </el-select>
                      <el-select v-model="condition.operator" placeholder="操作符" class="operator-select">
                        <el-option v-for="item in operatorOptions" :key="item.value" :label="item.label"
                          :value="item.value"></el-option>
                      </el-select>
                      <!-- 关键词输入框改为多标签形式 -->
                      <div class="keyword-input-container">
                        <div v-for="(keyword, kIndex) in condition.keywords" :key="kIndex" class="keyword-tag">
                          {{ keyword }}
                          <span class="tag-remove" @click="removeKeyword(condition, kIndex)">×</span>
                        </div>
                        <el-input v-model="condition.currentKeyword" placeholder="输入关键词，回车添加" class="keyword-input"
                          @keyup.enter.native="addKeyword(condition, condition.currentKeyword)"
                          @blur="handleInputBlur(condition, condition.currentKeyword)"></el-input>
                      </div>
                      <div
                        class="public_button clear_button fr position_btn"
                        v-if="currentRule.ruleConfigs.length > 1"
                        @click="removeCondition(index)"
                      >
                        <i class="mr10 el-icon-close"></i>删除
                      </div>

                      <div
                        v-if="index === currentRule.ruleConfigs.length - 1"
                        @click="addCondition"
                        class="public_button search_btn fr position_btn"
                      >
                        <i class="mr10 el-icon-circle-plus-outline"></i>添加
                      </div>
                      <div style="clear: both"></div>
                    </el-form>
                  </div>
                </div>

                <el-form class="config-section" style="text-align: left;">
                  <h4 style="color: #ffffff;">执行频率</h4>
                  <el-select v-model="currentRule.ruleSchedule.frequency" placeholder="执行频率" class="frequency-select"
                    @change="handleFrequencyTypeChange">
                    <el-option v-for="item in frequencyTypeOptions" :key="item.dictValue" :label="item.dictLabel"
                      :value="item.dictValue"></el-option>
                  </el-select>
                  <el-select v-model="currentRule.ruleSchedule.dayOfWeek" placeholder="周几" class="weekday-select"
                    v-if="currentRule.ruleSchedule.frequency === '2'" :disabled="currentRule.ruleSchedule.frequency !== '2'">
                    <el-option v-for="item in weekdayOptions" :key="item.dictValue" :label="item.dictLabel"
                      :value="item.dictValue"></el-option>
                  </el-select>
                  <el-select v-model="currentRule.ruleSchedule.executeHour" placeholder="时间" class="time-select">
                    <el-option v-for="item in executeHourOptions" :key="item.dictValue" :label="item.dictLabel"
                      :value="item.dictValue"></el-option>
                  </el-select>
                </el-form>

                <div class="config-actions">
                  <div class="public_button clear_button fl position_btn" @click="resetCurrentRule">
                    <i class="mr10 el-icon-refresh"></i>重置规则
                  </div>
                  <div @click="testCurrentRule" class="public_button search_btn fl position_btn">
                    <i class="mr10 el-icon-search"></i>测试规则
                  </div>
                </div>
              </div>
            </el-tabs>

            <!-- 底部取消和保存按钮 -->
            <div class="rule-footer">
              <div class="public_button clear_button fl position_btn" @click="cancelRules">
                <i class="mr10 el-icon-close"></i>取消
              </div>
              <div @click="saveOrUpdateRules" class="public_button search_btn fl position_btn">
                <i class="mr10 el-icon-finished"></i>保存/修改
              </div>
            </div>
          </div>

          <!-- 状态2：列表页（默认显示） -->
          <div v-else>
            <el-tabs v-model="activeTab" type="form" @tab-click="handleTabChange" class="content-tabs">
              <!-- 动态生成Tab页 -->
              <el-tab-pane v-for="tab in tabList" :key="tab.id" :label="`${tab.categoryName} (${tab.count || 0})`"
                :name="tab.id">
                <div style="text-align: left;">
                  <div v-if="(tableData[tab.id] || []).length === 0" class="no-data-img">
                    <img src="../imgs_erb/img_zwsj.png" alt="无数据" style="width: 520px; height: 360px;">
                    <p
                      style="color: #A9C7EA;margin-top: -90px;margin-bottom: 228px;margin-left: 70px; display: flex; justify-content: center; align-items: center;">
                      暂无数据,请先
                      <span class="config-rule-link"
                        @click="handleThematicSetting({id: currentThematicId, categoryName: currentThematicName})"
                        style="cursor: pointer; color: #409EFF; margin-left: 5px;">配置规则</span>
                    </p>
                  </div>
                  <div v-else>
                    <el-table v-loading="loading[tab.id]" :data="tableData[tab.id] || []"
                      @selection-change="handleSelectChange(tab.id)" border stripe>
                      <el-table-column label="序号" type="index" width="60" />
                      <el-table-column prop="eventName" label="事件名称" min-width="200" />
                      <el-table-column prop="createByName" label="填报人" width="120" />
                    </el-table>
                    <pagination v-if="total[tab.id] > 0" :total="total[tab.id]" :page.sync="pageInfo[tab.id].pageNum"
                      :limit.sync="pageInfo[tab.id].pageSize" @pagination="handlePageChange(tab.id)" class="pagination"
                      style="justify-content: flex-start;" />
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 右侧数据预览抽屉 -->
    <el-drawer title="数据预览" :visible.sync="drawer" direction="rtl" size="25%" custom-class="data-preview-drawer"
      :close-on-click-modal="false">
      <div class="preview-match-tip">根据当前规则，匹配到 {{ previewTotal || 0 }} 条数据</div>
      <div class="preview-data-list">
        <div class="preview-data-item" v-for="(item, index) in previewData" :key="index">
          <div class="item-title">{{ index + 1 }}.{{ item.name }}</div>
          <div class="item-content">{{ item.content }}</div>
          <div class="item-meta">
            <span class="meta-source">来源：{{ item.source }}</span>
            <span class="meta-time">发布时间：{{ item.createTime }}</span>
          </div>
          <div class="item-tags">
            <el-tag :type="item.sentiment === '负面' ? 'danger' : 'info'" size="mini">{{ item.sentiment }}</el-tag>
            <el-tag type="primary" size="mini">{{ item.matcherLabel }}</el-tag>
          </div>
        </div>
      </div>
      <div class="preview-footer">
        <el-button @click="drawer = false">关闭</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  selectListBySystemType,
  selectThematicListById,
  fields,
  operators,
  data_manage_frequency,
  data_manage_dayofweek,
  data_manage_executehour,
  selectDataByThenaticId,
  selectTabData,
  saveRules,
  getRuleByThematic,
  updateRules,
  execute,
  selectCountById
} from "@/api/data/zcbuild";

// 格式化日期工具函数
function formatDate(time, format) {
  if (!time) return '';
  const date = new Date(time);
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'H+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    'S': date.getMilliseconds()
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
    }
  }
  return format;
}

export default {
  filters: {
    formatDate(time) {
      return formatDate(time, 'yyyy-MM-dd HH:mm')
    }
  },
  data() {
    return {
      // 首次加载标记
      isFirstLoad: true,
      // 左侧主题和专题列表
      topicOptions: [],
      thematicList: [],
      searchTopic: '',
      currentTopicId: null,
      currentThematicId: null,
      currentThematicName: '',
      thematicCount: 0,
      // 动态Tab列表
      tabList: [],

      // 规则配置页状态：默认false（显示列表页）
      isSettingVisible: false,
      activeTab: '',

      // 右侧预览抽屉
      drawer: false,
      previewData: [],
      previewTotal: 0,

      // 规则数据存储
      thematicRuleMap: {}, 

      // 下拉框选项
      fieldOptions: {},
      operatorOptions: [],
      frequencyTypeOptions: [],
      weekdayOptions: [],
      executeHourOptions: [],

      // 列表数据
      tableData: {},
      loading: {},
      total: {},
      pageInfo: {},
      selectedIds: {},
    };
  },
  computed: {
    currentRule() {
      if (!this.thematicRuleMap[this.currentThematicId]) {
        this.$set(this.thematicRuleMap, this.currentThematicId, {});
      }
      if (!this.thematicRuleMap[this.currentThematicId][this.activeTab]) {
        this.initRuleForm(this.currentThematicId, this.activeTab);
      }
      return this.thematicRuleMap[this.currentThematicId][this.activeTab];
    },
    hasSelected() {
      return this.selectedIds[this.activeTab]?.length > 0 || false
    }
  },
  created() {
    this.fetchFrequencyTypeOptions();
    this.fetchExecuteHourOptions();
    this.fetchTopicOptions();
  },
  methods: {
    // 添加关键词
    addKeyword(condition, value) {
      if (value && value.trim() && !condition.keywords.includes(value.trim())) {
        condition.keywords.push(value.trim());
        condition.currentKeyword = '';
      }
    },
    
    // 移除关键词
    removeKeyword(condition, index) {
      condition.keywords.splice(index, 1);
    },
    
    // 输入框失焦处理
    handleInputBlur(condition, value) {
      if (value && value.trim() && !condition.keywords.includes(value.trim())) {
        condition.keywords.push(value.trim());
        condition.currentKeyword = '';
      }
    },

    // 获取主题下拉选项
    fetchTopicOptions() {
      const params = { systemType: 1 };
      selectListBySystemType(params)
        .then(response => {
          if (response?.code === 200) {
            this.topicOptions = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名主题'
            }));
            if (this.topicOptions.length > 0) {
              this.currentTopicId = this.topicOptions[0].id;
              this.fetchThematicList();
            }
          } else {
            this.topicOptions = [];
          }
        })
        .catch(error => {
          console.error('获取主题列表失败:', error);
          this.$message.error('获取主题列表失败，请稍后重试');
          this.topicOptions = [];
        });
    },

    // 获取专题数量
    fetchThematicCount() {
      if (!this.currentTopicId) return;
      selectCountById(this.currentTopicId)
        .then(response => {
          if (response?.code === 200) {
            this.thematicCount = response.data || 0;
          } else {
            this.thematicCount = 0;
            this.$message.error('获取专题数量失败：' + (response?.msg || '未知错误'));
          }
        })
        .catch(error => {
          console.error('获取专题数量失败:', error);
          this.thematicCount = 0;
          this.$message.error('获取专题数量失败，请稍后重试');
        });
    },

    // 搜索专题
    handleSearchInput() {
      this.fetchThematicList();
    },

    // 获取专题列表
    fetchThematicList() {
      const params = {
        id: this.currentTopicId,
        searchName: this.searchTopic,
        ruleType: ''
      };
      selectThematicListById(params)
        .then(response => {
          if (response?.code === 200) {
            this.thematicList = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名专题',
              count: item.count || 0
            }));
            if (this.thematicList.length > 0) {
              this.currentThematicId = this.thematicList[0].id;
              this.currentThematicName = this.thematicList[0].categoryName;
              this.fetchTabList();
            } else {
              this.currentThematicId = null;
              this.currentThematicName = '';
              this.tabList = [];
            }
          } else {
            this.thematicList = [];
            this.currentThematicId = null;
            this.currentThematicName = '';
            this.tabList = [];
          }
          this.fetchThematicCount();
        })
        .catch(error => {
          console.error('获取专题列表失败:', error);
          this.$message.error('获取专题列表失败，请稍后重试');
          this.thematicList = [];
          this.thematicCount = 0;
        });
    },

    // 初始化规则表单
    initRuleForm(thematicId, tabId) {
      if (!this.thematicRuleMap[thematicId]) {
        this.$set(this.thematicRuleMap, thematicId, {});
      }
      this.$set(this.thematicRuleMap[thematicId], tabId, {
        ruleConfigs: [
          { fieldName: '', operator: '', keywords: [], currentKeyword: '', id: '' }
        ],
        ruleSchedule: {
          frequency: '',
          dayOfWeek: '',
          executeHour: '',
          thematicId: thematicId,
          ruleType: tabId,
          ruleId: ''
        }
      });
    },

    // 获取Tab列表（核心逻辑：仅首次加载强制列表页）
    fetchTabList() {
      if (!this.currentThematicId) return;
      const params = { thematicId: this.currentThematicId };
      selectTabData(params)
        .then(response => {
          if (response?.code === 200) {
            this.tabList = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名类别',
              count: item.count || 0
            }));
            
            // 若Tab列表为空且当前在配置页，强制关闭
            if (this.tabList.length === 0 && this.isSettingVisible) {
              this.isSettingVisible = false;
              this.$message.warning('该专题暂无可用类别，已关闭规则配置页');
              return;
            }
            
            this.tabList.forEach(tab => {
              if (!this.thematicRuleMap[this.currentThematicId]?.[tab.id]) {
                this.initRuleForm(this.currentThematicId, tab.id);
              }
            });
            
            if (this.tabList.length > 0) {
              this.activeTab = this.tabList[0].id;
              
              // 仅首次加载时强制显示列表页并加载数据
              if (this.isFirstLoad) {
                this.isSettingVisible = false;
                this.loadData(this.activeTab);
                this.isFirstLoad = false; // 标记为非首次加载
              } else {
                // 非首次加载时根据当前状态处理
                if (this.isSettingVisible) {
                  this.fetchFieldOptions(this.activeTab);
                  this.fetchExistingRules(this.currentThematicId, this.activeTab);
                } else {
                  this.loadData(this.activeTab);
                }
              }
            } else {
              this.activeTab = '';
            }
          } else {
            this.tabList = [];
            this.activeTab = '';
          }
        })
        .catch(error => {
          console.error('获取Tab列表失败:', error);
          this.$message.error('获取Tab列表失败，请稍后重试');
          this.tabList = [];
        });
    },

    // 主题切换
    handleTopicChange() {
      this.thematicRuleMap = {};
      this.isFirstLoad = false; // 切换主题后不再视为首次加载
      this.fetchThematicList();
      this.fetchThematicCount();
    },

    // 加载列表数据
    loadData(tabId) {
      if (!tabId) return;
      
      const tab = this.tabList.find(item => item.id === tabId);
      if (!tab) return;
      
      if (!this.pageInfo[tabId]) {
        this.$set(this.pageInfo, tabId, { pageNum: 1, pageSize: 10 });
      }

      const params = {
        thematicId: this.currentThematicId,
        ruleType: tabId,
        pageNum: this.pageInfo[tabId].pageNum,
        pageSize: this.pageInfo[tabId].pageSize
      };
      this.$set(this.loading, tabId, true);
      selectDataByThenaticId(params)
        .then(response => {
          this.$set(this.loading, tabId, false);
          if (response?.code === 200) {
            this.$set(this.tableData, tabId, response.rows || []);
            this.$set(this.total, tabId, response.total || 0);
          } else {
            this.$set(this.tableData, tabId, []);
            this.$set(this.total, tabId, 0);
          }
        })
        .catch(error => {
          console.error(`加载${tabId}数据失败:`, error);
          this.$message.error(`加载数据失败，请稍后重试`);
          this.$set(this.loading, tabId, false);
        });
    },

    // 分页切换
    handlePageChange(tabId) {
      this.loadData(tabId);
    },

    // 专题选择
    handleThematicSelect(data) {
      this.currentThematicId = data.id;
      this.currentThematicName = data.categoryName;
      this.isSettingVisible = false; // 选择专题时显示列表页
      this.fetchTabList();
    },

    // 进入规则配置页（核心限制：Tab为空时不允许打开）
    handleThematicSetting(topic) {
      // 先验证该专题是否有可用Tab
      const params = { thematicId: topic.id };
      selectTabData(params)
        .then(response => {
          if (response?.code === 200) {
            const tabData = response.data || [];
            // 若Tab列表为空，提示无法配置
            if (tabData.length === 0) {
              this.$message.warning('该专题暂无可用类别，无法配置规则');
              return;
            }
            // Tab列表不为空时，正常打开配置页
            this.currentThematicId = topic.id;
            this.currentThematicName = topic.categoryName;
            this.isSettingVisible = true;
            this.fetchTabList();
          } else {
            this.$message.error('获取类别信息失败：' + (response?.msg || '未知错误'));
          }
        })
        .catch(error => {
          console.error('验证Tab列表失败:', error);
          this.$message.error('验证专题信息失败，请稍后重试');
        });
    },

    // 获取已有规则
    fetchExistingRules(thematicId, tabId) {
      const params = {
        thematicId: thematicId,
        ruleType: tabId
      };
      getRuleByThematic(params)
        .then(response => {
          if (response?.code === 200) {
            const ruleData = response.data || {};
            const ruleConfigs = ruleData.ruleConfig?.length 
              ? ruleData.ruleConfig.map(item => ({
                  fieldName: item.fieldName || '',
                  operator: item.operator || '',
                  keywords: (item.valueList || '').split(',').filter(Boolean),
                  currentKeyword: '',
                  id: item.id || ''
                }))
              : [{ fieldName: '', operator: '', keywords: [], currentKeyword: '' }];

            const ruleSchedule = {
              frequency: String(ruleData.ruleSchedule?.frequency || ''),
              dayOfWeek: String(ruleData.ruleSchedule?.dayOfWeek || ''),
              executeHour: String(ruleData.ruleSchedule?.executeHour || ''),
              ruleId: ruleData.ruleSchedule?.ruleId || ''
            };

            if (!this.thematicRuleMap[thematicId]) {
              this.$set(this.thematicRuleMap, thematicId, {});
            }
            this.$set(this.thematicRuleMap[thematicId], tabId, {
              ruleConfigs,
              ruleSchedule: {
                ...this.thematicRuleMap[thematicId][tabId].ruleSchedule,
                ...ruleSchedule
              }
            });

            if (ruleConfigs[0].fieldName && this.fieldOptions[tabId]) {
              const firstField = this.fieldOptions[tabId].find(
                item => item.fieldName === ruleConfigs[0].fieldName
              );
              if (firstField) {
                this.fetchOperatorOptions(firstField.fieldType);
              }
            }
          } else {
            this.$message.error('查询规则失败：' + (response?.msg || '未知错误'));
            this.resetCurrentRule();
          }
        })
        .catch(error => {
          console.error('查询已有规则失败:', error);
          this.$message.error('查询规则失败，请稍后重试');
          this.resetCurrentRule();
        });
    },

    // 获取字段列表
    fetchFieldOptions(tabId) {
      fields({ type: tabId })
        .then(response => {
          if (response?.code === 200) {
            this.$set(this.fieldOptions, tabId, (response.data || []).map(item => ({
              fieldName: item.fieldName,
              displayName: item.displayName || '未命名字段',
              fieldType: item.fieldType
            })));
          } else {
            this.$set(this.fieldOptions, tabId, []);
          }
        })
        .catch(error => {
          console.error(`获取字段列表失败 (Tab: ${tabId}):`, error);
          this.$message.error(`获取字段列表失败，请稍后重试`);
        });
    },

    // 获取执行频率选项
    fetchFrequencyTypeOptions() {
      data_manage_frequency()
        .then(response => {
          this.frequencyTypeOptions = response.data || [];
        })
        .catch(error => {
          console.error('获取执行频率类型失败:', error);
          this.$message.error('获取执行频率类型失败，请稍后重试');
        });
    },

    // 获取执行时间选项
    fetchExecuteHourOptions() {
      data_manage_executehour()
        .then(response => {
          this.executeHourOptions = (response.data || []).map(item => ({
            dictLabel: item.dictLabel || '未设置',
            dictValue: item.dictValue
          }));
        })
        .catch(error => {
          console.error('获取执行时间失败:', error);
          this.$message.error('获取执行时间失败，请稍后重试');
        });
    },

    // 字段变化处理
    handleFieldChange(condition) {
      const fieldItem = this.fieldOptions[this.activeTab]?.find(item => item.fieldName === condition.fieldName);
      if (fieldItem) {
        this.fetchOperatorOptions(fieldItem.fieldType);
      } else {
        this.operatorOptions = [];
      }
    },

    // 获取操作符选项
    fetchOperatorOptions(fieldType) {
      if (!fieldType) {
        this.operatorOptions = [];
        return;
      }
      operators({ fieldType })
        .then(response => {
          this.operatorOptions = (response.data || []).map(item => ({
            label: item.label || '未命名操作符',
            value: item.value
          }));
        })
        .catch(error => {
          console.error('获取操作符失败:', error);
          this.$message.error('获取操作符失败，请稍后重试');
        });
    },

    // 执行频率变化处理
    handleFrequencyTypeChange() {
      if (this.currentRule.ruleSchedule.frequency === '2') {
        data_manage_dayofweek()
          .then(response => {
            this.weekdayOptions = response.data || [];
          })
          .catch(error => {
            console.error('获取周几列表失败:', error);
            this.$message.error('获取周几列表失败，请稍后重试');
          });
      } else {
        this.weekdayOptions = [];
        this.currentRule.ruleSchedule.dayOfWeek = '';
      }
    },

    // 规则页Tab切换
    handleRuleTabChange(tab) {
      const tabId = tab.name;
      this.activeTab = tabId;
      
      if (!this.thematicRuleMap[this.currentThematicId]) {
        this.$set(this.thematicRuleMap, this.currentThematicId, {});
      }
      
      if (!this.thematicRuleMap[this.currentThematicId][tabId]) {
        this.initRuleForm(this.currentThematicId, tabId);
      }
      
      this.currentRule.ruleSchedule.thematicId = this.currentThematicId;
      this.currentRule.ruleSchedule.ruleType = tabId;
      
      this.fetchFieldOptions(tabId);
      this.fetchExistingRules(this.currentThematicId, tabId);
    },

    // 列表页Tab切换
    handleTabChange(tab) {
      this.activeTab = tab.name;
      this.loadData(tab.name);
    },

    // 添加条件行
    addCondition() {
      this.currentRule.ruleConfigs.push({ 
        fieldName: '', 
        operator: '', 
        keywords: [], 
        currentKeyword: '', 
        id: '' 
      });
    },

    // 删除条件行
    removeCondition(index) {
      if (this.currentRule.ruleConfigs.length <= 1) return;
      this.currentRule.ruleConfigs.splice(index, 1);
    },

    // 保存/修改规则
    saveOrUpdateRules() {
      const thematicId = this.currentThematicId;
      const tabId = this.activeTab;
      const currentRule = this.currentRule;
      
      if (!thematicId) {
        this.$message.error('请选择专题');
        return;
      }
      if (currentRule.ruleConfigs.length === 0) {
        this.$message.error('请至少配置一条规则');
        return;
      }
      if (!currentRule.ruleSchedule.frequency || !currentRule.ruleSchedule.executeHour) {
        this.$message.error('请完善执行频率设置');
        return;
      }

      const ruleConfigs = currentRule.ruleConfigs.map(condition => ({
        ...condition,
        valueList: condition.keywords.join(',')
      }));

      const requestData = {
        ruleConfigs,
        ruleSchedule: currentRule.ruleSchedule
      };

      const api = currentRule.ruleSchedule.ruleId 
        ? updateRules 
        : saveRules;

      api(requestData)
        .then(response => {
          if (response?.code === 200) {
            this.$message.success(currentRule.ruleSchedule.ruleId ? '规则修改成功' : '规则保存成功');
            this.isSettingVisible = false;
            this.fetchTabList();
          } else {
            this.$message.error('操作失败：' + (response?.msg || '未知错误'));
          }
        })
        .catch(error => {
          console.error('保存/修改规则失败:', error);
          this.$message.error('操作失败，请稍后重试');
        });
    },

    // 重置规则
    resetCurrentRule() {
      this.initRuleForm(this.currentThematicId, this.activeTab);
      this.operatorOptions = [];
      this.weekdayOptions = [];
    },

    // 测试规则
    testCurrentRule() {
      const tabId = this.activeTab;
      const currentRule = this.currentRule;
      
      const ruleConfigs = currentRule.ruleConfigs.map(condition => ({
        ...condition,
        valueList: condition.keywords.join(',')
      }));

      const testParams = {
        ruleConfigs,
        thematicId: currentRule.ruleSchedule.thematicId,
        ruleType: tabId,
        pageNum: 1,
        pageSize: 10
      };

      execute(testParams)
        .then(response => {
          if (response?.code === 200) {
            this.previewData = response.data.list || [];
            this.previewTotal = response.data.total || 0;
            this.drawer = true;
          } else {
            this.$message.error('规则测试失败：' + (response?.msg || '未知错误'));
          }
        })
        .catch(error => {
          console.error('规则测试失败:', error);
          this.$message.error('规则测试失败，请稍后重试');
        });
    },

    // 取消规则配置
    cancelRules() {
      this.isSettingVisible = false;
      this.resetCurrentRule();
      this.loadData(this.activeTab);
    },

    // 选择项变化
    handleSelectChange(tabId, selection) {
      this.$set(this.selectedIds, tabId, (selection || []).map(item => item.id));
    },
  }
}
</script>

<style scoped>
.main-row {
  height: 100%;
  min-height: calc(100vh - 60px);
  padding: 15px;
}

.left-panel{
  height: 100%;
  width: 18%;
  background: rgba(27,126,242,0.10);
  margin-right: 1%;
}

.right-panel {
  height: 100%;
  width: 81%;
  background: rgba(27,126,242,0.10);
}

.left-card,
.right-card {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: transparent !important;
  box-shadow: none !important;
}

.topic-title {
  /* font-family: SourceHanSansSC-Bold; */
  font-size: 24px;
  letter-spacing: 0;
  font-weight: 700;
  background: linear-gradient(to bottom, #FFFFFF, #0091FF);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 0;
}

.topic-title1 {
  /* font-family: SourceHanSansSC-Bold; */
  font-size: 20px;
  letter-spacing: 0;
  font-weight: 700;
  background: linear-gradient(to bottom, #FFFFFF, #0091FF);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 15px;
  margin-top: 25px;
}

.topic-title2 {
  font-family: "LetsgoDigital-Regular", sans-serif;
  font-size: 20px;
  letter-spacing: 0;
  font-weight: 700;
  background: linear-gradient(to bottom, #FFFFFF, #0091FF);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  /* margin-bottom: 15px; */
  margin-top: 5px;
}

.topic-desc {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #A9C7EA;
  letter-spacing: 0;
  font-weight: 400;
}

.search-input {
  margin-bottom: 10px;
}

.topic-list-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 8px 5px;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  margin-bottom: 6px;
  /* background: rgba(27,126,242,0.10); */
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(27,126,242,0.1);
}

.topic-item-active {
  border: 1px solid rgba(27,126,242,0.5);
  background: rgba(27,126,242,0.10);
}

.topic-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.topic-name {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.topic-unit {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #A9C7EA;
  letter-spacing: 0;
  font-weight: 400;
}

.topic-setting {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  color: #909399;
  cursor: pointer;
  transition: color 0.2s;
  padding: 0;
}

.topic-setting:hover {
  color: #409eff;
}

.topic-empty {
  text-align: center;
  padding: 30px 0;
  color: #ffffff;
  font-size: 14px;
  background-color: transparent;
  border-radius: 4px;
  margin: 0 5px;
}

.pagination {
  margin-top: 15px;
  text-align: right;
  color: #dcdfe6 !important;
}

.rule-config {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.rule-tab{
  background-image: linear-gradient(179deg, rgba(27,126,242,0.00) 0%, rgba(27,126,242,0.20) 100%);
}

::v-deep.el-tabs__item.is-active {
  color: #dcdfe6 !important;
}

.config-section {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #eeeeee21;
}

.config-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #303133;
  display: flex;
  align-items: center;
}

.config-section h4 i {
  margin-left: 5px;
  font-size: 14px;
  color: #909399;
}

.condition-group {
  margin-bottom: 15px;
}

.condition-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.field-select,
.operator-select,
.frequency-select,
.weekday-select,
.time-select {
  width: 140px;
  margin-right: 10px;
  margin-bottom: 5px;
}

.keyword-input-container {
  flex: 1;
  min-width: 0;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0;
  min-height: 36px;
  align-items: center;
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  min-height: 42px;
  padding: 5px;
  margin-right: 10px;
}
.keyword-input-container ::v-deep .el-input__inner {
  background-color: transparent !important;
  border: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
  height: 36px !important;
  line-height: 36px !important;
}

.keyword-tag {
  background-color: #00FFA0;
  border-radius: 4px;
  padding: 2px 10px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  margin: 0 2px;
  color: #fff;
  background: rgba(0, 255, 160, 0.20);
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #00FFA0;
  letter-spacing: 0;
  font-weight: 400;
}

.keyword-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 100px;
  height: 36px;
  padding: 0 10px;
}

.keyword-input::-webkit-input-placeholder {
  color: #c0c4cc;
}

.tag-remove {
  cursor: pointer;
  color: #909399;
  font-size: 12px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.tag-remove:hover {
  background-color: #ccc;
  color: #fff;
}

.rule-footer {
  padding: 15px;
  border-top: 1px solid #eeeeee2b;
  display: flex;
  justify-content: center;
  gap: 10px;
}

::v-deep .data-preview-drawer {
  width: 25% !important;
}

::v-deep .data-preview-drawer .el-drawer__body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100% - 56px);
}

.preview-match-tip {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.preview-data-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-data-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #508df6;
  line-height: 1.4;
}

.item-content {
  font-size: 13px;
  color: #666666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  font-size: 12px;
  color: #999999;
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
}

.item-tags {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

.preview-footer {
  margin-top: 16px;
  text-align: right;
}

::v-deep .data-preview-drawer .el-drawer__header {
  padding: 14px 20px;
  border-bottom: 1px solid #eee;
}

.topic-count-total {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #A9C7EA;
  letter-spacing: 0;
  font-weight: 400;
  margin-bottom: 15px;
  margin-top: 10px;
}

.el-input__inner {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  height: 50px !important;
  line-height: 50px !important;
  color: #ffffff !important;
}

.el-form-item__label {
  height: 50px !important;
  line-height: 50px !important;
}

::v-deep .content-tabs .el-table {
  background: inherit !important;
  border: inherit !important;
}

::v-deep .content-tabs .el-table tr,
::v-deep .content-tabs .el-table th,
::v-deep .content-tabs .el-table td {
  background: inherit !important;
  border-color: inherit !important;
  color: inherit !important;
}

::v-deep .content-tabs .el-pagination {
  justify-content: flex-start !important;
  color: inherit !important;
}

::v-deep .content-tabs .el-pagination .el-pager li,
::v-deep .content-tabs .el-pagination .btn-prev,
::v-deep .content-tabs .el-pagination .btn-next {
  background-color: inherit !important;
  color: inherit !important;
}

::v-deep .content-tabs .el-table .cell {
  font-family: inherit !important;
  font-size: inherit !important;
  color: #ffffff !important;
}

.topic-tag-img {
  width: 4px;
  height: 20px;
  margin-right: 8px;
  vertical-align: middle;
  margin-left: -12px;
}

.topic-name-wrap {
  display: flex;
  align-items: center;
}

.no-data-img {
  text-align: center;
  padding: 40px 0;
}

.public_button {
  height: 50px;
  line-height: 35px;
}
</style>