<template>
  <!-- 趋势总览 -->
  <div class="app-container">
    <!-- 左侧列表 -->
    <el-row :gutter="20" class="main-row">
      <el-col :span="6" class="left-panel">
        <el-card shadow="always" class="left-card">
          <div class="history_log">请选择主题</div>
          <el-select v-model="currentTopicId" placeholder="请选择主题" @change="handleTopicChange" style="width:333px"
            class="blue">
            <el-option v-for="topic in topicOptions" :key="topic.id" :label="topic.categoryName" :value="topic.id" />
          </el-select>
          <h4 class="history_log">专题列表</h4>
          <el-input v-model="searchTopic" placeholder="搜索专题名称" clearable size="small" prefix-icon="el-icon-search"
            class="blue" @keyup.enter="fetchThematicList" @input="handleSearchInput" />
          <div class="topic-list-wrapper">
            <div v-for="topic in thematicList" :key="topic.id" class="topic-item"
              :class="{ 'topic-item-active': currentThematicId === topic.id }" @click="handleThematicSelect(topic)">
              <div class="topic-info">
                <div class="topic-name">{{ topic.categoryName }}</div>
                <div class="topic-count">{{ topic.count || 0 }} 条数据</div>
              </div>
            </div>
            <div class="topic-empty" v-if="thematicList.length === 0">
              暂无专题数据
            </div>
          </div>
        </el-card>
      </el-col>
      <!-- 右侧内容区 -->
      <el-col :span="18" class="right-panel">
        <div class="right-container">
          <!-- 添加渐变背景和标签导航 -->
          <div class="page-header">
            <h3 class="page-title">{{ topicOptions[0].categoryName }}</h3>
            <div class="page-tabs">
              <span class="tab-item active">趋势总览</span>
              <span class="tab-item" @click="to_ksh">可视化时间轴</span>
              <!-- <span class="tab-item" @click="to_gjjs">高级检索</span> -->
              <!-- <span class="tab-item" @click="to_ztbg">专题报告</span> -->
            </div>
          </div>
          <!-- 搜索区域 -->
          <el-card class="search-card" shadow="never">
            <el-form :inline="true" :model="queryParams" class="search-form">
              <el-form-item label="分析周期:">
                <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                  range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
              </el-form-item>
              <el-form-item label="时间粒度:">
                <el-select v-model="queryParams.period" placeholder="请选择" style="width: 120px">
                  <el-option label="月" value="1" />
                  <el-option label="季度" value="2" />
                  <el-option label="年" value="3" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <div class="public_button clear_button fl position_btn" @click="resetQuery">
                  <i class="mr10 el-icon-refresh"></i>重置
                </div>
                <div @click="handleQuery" class="public_button search_btn fl position_btn">
                  <i class="mr10 el-icon-search"></i>查询
                </div>
              </el-form-item>

            </el-form>
          </el-card>
          <!-- 专题概述 -->
          <div class="description-box">
            <div>
              专题概述：{{ description }}
            </div>
          </div>
          <!-- 美化统计卡片，添加渐变背景和图标 -->
          <el-row :gutter="16" class="stats-row">
            <el-col :xs="12" :sm="8" :md="6" :lg="6">
              <div class="stat-card stat-card-blue">
                <div class="stat-content">
                  <div class="stat-label">时间跨度</div>
                  <div class="stat-value">{{ timespan }}</div>
                  <div class="stat-value">{{ timespanStartEnd }}</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="8" :md="6" :lg="6">
              <div class="stat-card stat-card-green">

                <div class="stat-content">
                  <div class="stat-label">峰值时间</div>
                  <div class="stat-value">{{ peakTime }}</div>
                  <div class="stat-value">{{ peakTimeCount }}</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="8" :md="6" :lg="6">
              <div class="stat-card stat-card-orange">

                <div class="stat-content">
                  <div class="stat-label">主要正面事件</div>
                  <div class="stat-value">{{ keyPositiveEvents }}</div>
                  <div class="stat-value">{{ keyPositiveEventsTime }}</div>

                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="8" :md="6" :lg="6">
              <div class="stat-card stat-card-red">

                <div class="stat-content">
                  <div class="stat-label">主要负面事件</div>
                  <div class="stat-value">{{ keyNegativeEvents }}</div>
                  <div class="stat-value">{{ keyNegativeEventsTime }}</div>

                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 动态生成的统计块 -->
          <el-row :gutter="24" class="stats-row">
            <el-col :xs="12" :sm="8" :md="6" :lg="5" v-for="(category, index) in categoryList" :key="index">
              <div class="stat-card stat-card-blue">
                <div class="stat-content">
                  <div class="stat-label">{{ category.categoryName }}</div>
                  <div class="stat-value">{{ category.count }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
          <!-- 图表区域 -->
          <el-row :gutter="16" class="chart-row" v-if="isExist">
            <!--事件发展趋势 -->
            <!-- 这个图由后端回的字段决定展不展示，所以先隐藏，默认隐藏 -->
            <el-col :span="24">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-data-line"></i>
                    事件发展趋势
                  </span>
                  <div class="chart-actions">
                    <el-button @click="handelqz" type="text"
                      style="font-size: 16px;color: #FFFFFF;letter-spacing: 0;font-weight: 400;">权重设置</el-button>
                    <div class="custom-radio-group">
                      <div @click="lineChartType = '趋势图'"
                        :class="['custom-radio-button', { 'is-active': lineChartType === '趋势图' }]">
                        趋势图
                      </div>
                      <div @click="lineChartType = '散点图'"
                        :class="['custom-radio-button', { 'is-active': lineChartType === '散点图' }]">

                        散点图
                      </div>
                    </div>
                  </div>
                </div>


                <!-- <div ref="lineChart" class="chart-container"></div>
                <div ref="sdChart" class="chart-container"></div> -->
                <!-- 趋势图 -->
                <div ref="lineChart" class="chart-container" v-show="lineChartType === '趋势图'"></div>

                <!-- 散点图 -->
                <div ref="sdChart" class="chart-container" v-show="lineChartType === '散点图'"></div>

              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="16" class="chart-row">

            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-s-data"></i>
                    情感趋势
                  </span>
                </div>
                <!-- 三折线图 -->
                <div ref="areaChart" class="chart-container"></div>
              </el-card>
            </el-col>


            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-pie-chart"></i>
                    专题相关实体
                  </span>
                </div>
                <!-- 词云图 -->
                <div ref="pieChart" class="chart-container"></div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="16" class="chart-row">

            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-s-data"></i>
                    事件类型分布
                  </span>
                </div>
                <!-- 饼图 -->
                <div ref="trendChart" class="chart-container"></div>
              </el-card>
            </el-col>


            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-pie-chart"></i>
                    专题声量趋势
                  </span>
                </div>
                <!-- 单条面积图 -->
                <div ref="slChart" class="chart-container"></div>

              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="16" class="chart-row">

            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-s-data"></i>
                    热点词云
                  </span>
                </div>
                <!-- 词云图 -->
                <div ref="rdpieChart" class="chart-container"></div>
              </el-card>
            </el-col>

            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-pie-chart"></i>
                    专题地域分布
                  </span>
                </div>
                <!-- 地域分布图 -->
                <div ref="dtChart" class="chart-container"></div>
              </el-card>
            </el-col>
          </el-row>
          <!-- 事件权重配置 -->
          <el-drawer title="事件权重配置" :visible.sync="drawer" size="25%" custom-class="custom-drawer">
            <div class="drawer-content">
              <p style="margin-bottom: 10px">
                调整不同类型事件的权重系数，将影响趋势图的分析结果。
              </p>
              <el-row :gutter="24">
                <el-col :span="24" v-for="(item, index) in sliderData" :key="index">
                  <div class="slider-block">
                    <span class="slider-label">{{ item.categoryName }}</span>
                    <el-slider v-model="item.weightInformation" :min="0.5" :max="3.0" :step="0.1"></el-slider>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="24" style="margin-top:20px">
                <el-col :span="12" style="text-align: center">
                  <el-button @click="resetSlider()" type="primary" plain>重置默认</el-button>
                </el-col>
                <el-col :span="12" style="text-align: center">
                  <el-button @click="apply()" type="success" plain>应用配置</el-button>
                </el-col>
              </el-row>
            </div>
          </el-drawer>
        </div>
      </el-col>
    </el-row>


  </div>
</template>

<script>
import * as echarts from "echarts";
import {
  selectListBySystemType, // 获取所有主题
  selectThematicListById, // 根据主题查找专题
  selectTopicDetailById, // 根据主题ID获取专题详情
  // 更改权重
  updateWeight,
  // 默认权重
  resetById,

} from "@/api/historyanalysis/index";
import 'echarts-wordcloud';
export default {
  name: "PileAnalysis",
  data() {
    return {
      thematicList: [],

      // 权重数组 
      sliderData: [
        { categoryName: '类型1', weightInformation: 1.0, id: '' },
        { categoryName: '类型2', weightInformation: 1.0, id: '' }
        // 更多类型
      ],
      series: [],
      ar_data: [],
      originalEvenQuantity: [],
      // 权重样式
      weightedEventQuantity: [],
      sldata: [],
      data_x: [],
      data_zm: [],
      data_zl: [],
      data_fm: [],
      thematicRelatedEntities: [],
      hotTopicWordCloud: [],
      categoryList: [],
      value1: "",
      // 是否展示折线图
      isExist: true,
      // 左侧主题和专题列表
      topicOptions: [],
      topicOptions: [{
        "id": "",  //专题id
        "categoryName": "暂无",   //专题名称
        "description": "暂无",  //专题描述
        "daily": 0,            // 舆情日报
        "event": 0,                   // 事件信息
        "openSource": 0,              // 开源咨询
        "research": 0,                // 理论研究成果
        "special": 0,                // 舆情专报
        "timespan": "暂无",            //时间跨度
        "timespanStartEnd": "暂无",  //时间跨度开始到结束
        "peakTime": "暂无",             //峰值时间
        "peakTimeCount": "暂无",   //峰值月份事件数量
        "keyPositiveEvents": "暂无",   //主要正面事件名称
        "keyPositiveEventsTime": "暂无",//主要正面事件发生事件
        "keyPositiveEventsPopularity": 0,   //主要正面事件热度
        "keyNegativeEvents": "暂无",   //主要负面事件名称
        "keyNegativeEventsTime": "暂无",   //主要负面事件发生事件
        "keyNegativeEventsPopularity": 0,  //主要负面事件热度
        // }

      }],
      searchTopic: '',
      currentTopicId: null,
      currentThematicId: null,
      currentThematicName: '', // 新增：存储当前专题名称
      typeCount: {
        openSource: 0,
        event: 0,
        daily: 0,
        special: 0,
        research: 0
      },

      // 规则配置页状态
      // isSettingVisible: false,
      activeTab: 'openSource',

      // 右侧预览抽屉
      drawer: false,
      previewData: [],
      previewTotal: 0,

      // 规则数据（严格匹配接口层级：ruleSchedule内包含thematicId和ruleType）
      ruleForm: {
        ruleConfigs: [
          {
            fieldName: '',
            operator: '',
            valueList: '',
            id: '' // 用于修改接口的规则ID
          }
        ],
        ruleSchedule: {
          frequency: '',
          dayOfWeek: '',
          executeHour: '',
          thematicId: '',
          ruleType: 1,
        }
      },

      // 下拉框选项
      fieldOptions: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      operatorOptions: [],
      frequencyTypeOptions: [], // 执行频率选项（每天/每周）
      weekdayOptions: [], // 周几选项
      executeHourOptions: [], // 执行时间选项

      // 右侧列表数据
      tableData: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      loading: {
        openSource: false,
        event: false,
        daily: false,
        special: false,
        theory: false
      },
      total: 0,
      // total: {
      //   openSource: 0,
      //   event: 0,
      //   daily: 0,
      //   special: 0,
      //   theory: 0
      // },

      selectedIds: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      // };
      // ***********************************
      // 专题描述字段
      description: '',
      daily: '',                  // 舆情日报
      event: '',                   // 事件信息
      openSource: '',              // 开源咨询
      research: '',                // 理论研究成果
      special: '',                 // 舆情专报
      timespan: '',            //时间跨度
      timespanStartEnd: '',  //时间跨度开始到结束
      peakTime: '',             //峰值时间
      peakTimeCount: '',   //峰值月份事件数量
      keyPositiveEvents: '',  //主要正面事件名称
      keyPositiveEventsTime: '',   //主要正面事件发生事件
      keyPositiveEventsPopularity: '',   //主要正面事件热度
      keyNegativeEvents: '',   //主要负面事件名称
      keyNegativeEventsTime: '',   //主要负面事件发生事件
      keyNegativeEventsPopularity: '',   //主要负面事件热度
      // },
      // **********
      dateRange: [],
      // 右侧弹出层
      drawer: false,
      queryParams: {
        startDate: "2025-04-17",
        endDate: "2025-05-15",
        period: "1",
      },
      // 这里改成变量可切换的两个值
      lineChartType: "趋势图",
      // 事件发展趋势图
      lineChart: null,
      //散点图
      sdChart: null,
      // 情感趋势图
      areaChart: null,
      // 专题相关实体词云图
      pieChart: null,
      // 事件类型分布图
      trendChart: null,
      // 专题声量分布图
      slChart: null,
      // 热点词云图
      rdpieChart: null,
      // 地域分布图
      dtChart: null,
    };
  },
  mounted() {
    this.fetchTopicOptions();
    this.fetchFieldOptions();
    this.$nextTick(() => {
      this.initCharts();
      window.addEventListener("resize", this.handleResize);
    });
    // this.handleThematicSelect(thematicList[0]);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    if (this.lineChart) this.lineChart.dispose();

    if (this.sdChart) this.sdChart.dispose();

    if (this.areaChart) this.areaChart.dispose();
    if (this.pieChart) this.pieChart.dispose();
    if (this.trendChart) this.trendChart.dispose();
    if (this.slChart) this.slChart.dispose();
    if (this.dtChart) this.dtChart.dispose();
    if (this.rdpieChart) this.rdpieChart.dispose();

  },
  created() {
    // 初始化时加载基础数据
    this.fetchTopicOptions();
    this.fetchFieldOptions();
    // this.handleThematicSelect(thematicList[0]);

  },
  methods: {
    // 更改成默认值1.0
    async resetSlider() {
      const params = {
        value: 1.0,
        id: this.currentThematicId,
      }
      let response = await resetById(params);
      if (response.code === 200) {
        // 将所有置成1.0
        this.sliderData.forEach(item => {
          item.weightInformation = 1.0;
        });
        this.$message.success("重置默认权重成功");
      } else {
        this.$message.error("重置默认权重失败");
      }
    },
    // updateWeight
    async apply() {
      const params = {
        classificationSystemList: this.sliderData
      }
      // };
      let response = await updateWeight(params);
      if (response.code === 200) {
        this.$message.success("修改权重成功");

      } else {
        this.$message.error("修改权重失败");
      }
    },
    // 获取主题下拉选项
    fetchTopicOptions() {
      const params = { systemType: 1 };
      selectListBySystemType(params)
        .then(response => {
          if (response?.code === 200) {
            this.topicOptions = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名主题'
            }));
            if (this.topicOptions.length > 0) {
              this.currentTopicId = this.topicOptions[0].id;
              this.fetchThematicList();
            }
          } else {
            this.topicOptions = [];
          }
        })
        .catch(error => {
          console.error('获取主题列表失败:', error);
          this.$message.error('获取主题列表失败，请稍后重试');
          this.topicOptions = [];
        });
    },

    // 输入框搜索专题
    handleSearchInput() {
      this.fetchThematicList();
    },

    // 获取专题列表
    fetchThematicList() {
      const params = {
        id: this.currentTopicId,
        searchName: this.searchTopic,
        ruleType: ''
      };
      selectThematicListById(params)
        .then(response => {
          if (response?.code === 200) {
            this.thematicList = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名专题',
              count: item.count || 0
            }));
            if (this.thematicList.length > 0) {
              this.currentThematicId = this.thematicList[0].id;
              this.currentThematicName = this.thematicList[0].categoryName; // 新增：同步专题名称
              this.ruleForm.ruleSchedule.thematicId = this.currentThematicId;
              // 自动选择第一个专题并加载数据
              this.handleThematicSelect(this.thematicList[0]);
            } else {
              this.currentThematicId = null;
              this.currentThematicName = ''; // 新增：清空专题名称
              this.ruleForm.ruleSchedule.thematicId = '';
              this.typeCount = { openSource: 0, event: 0, daily: 0, special: 0, research: 0 };
            }
          } else {
            this.thematicList = [];
            this.currentThematicId = null;
            this.currentThematicName = ''; // 新增：清空专题名称
            this.ruleForm.ruleSchedule.thematicId = '';
          }
        })
        .catch(error => {
          console.error('获取专题列表失败:', error);
          this.$message.error('获取专题列表失败，请稍后重试');
          this.thematicList = [];
          this.currentThematicId = null;
          this.currentThematicName = ''; // 新增：清空专题名称
          this.ruleForm.ruleSchedule.thematicId = '';
        });
    },
    // 主题选择变化
    handleTopicChange() {
      this.fetchThematicList();
    },

    // 专题选择
    // $$$$$$
    async handleThematicSelect(data) {
      this.currentThematicId = data.id;
      const params = {
        id: this.currentThematicId,
        // searchName: this.searchTopic,
        // ruleType: ''
      };
      let response = await selectTopicDetailById(params);
      if (response.code === 200) {
        this.description = response.data.description;
        this.daily = response.data.daily;
        this.event = response.data.event;
        this.openSource = response.data.openSource;
        this.research = response.data.research;
        this.special = response.data.special;
        this.timespan = response.data.timespan;
        this.timespanStartEnd = response.data.timespanStartEnd;
        this.peakTime = response.data.peakTime;
        this.peakTimeCount = response.data.peakTimeCount;
        this.keyPositiveEvents = response.data.keyPositiveEvents;
        this.keyPositiveEventsTime = response.data.keyPositiveEventsTime;
        this.keyPositiveEventsPopularity = response.data.keyPositiveEventsPopularity;
        this.keyNegativeEvents = response.data.keyNegativeEvents;
        this.keyNegativeEventsTime = response.data.keyNegativeEventsTime;
        this.keyNegativeEventsPopularity = response.data.keyNegativeEventsPopularity;
        this.categoryList = response.data.categoryList;
        this.sliderData = response.data.categoryList;
        this.isExist = response.data.isExist;
        this.thematicRelatedEntities = response.data.thematicRelatedEntities;
        this.data_x = response.data.abscissa;
        this.data_zm = response.data.positiveEmotionalTrend;
        this.data_zl = response.data.neutralEmotionalTrend;
        this.data_fm = response.data.negativeEmotionalTrend;
        this.hotTopicWordCloud = response.data.hotTopicWordCloud;
        this.originalEvenQuantity = response.data.originalEvenQuantity;
        this.weightedEventQuantity = response.data.weightedEventQuantity;
        this.sldata = response.data.thematicVolumeTrend;
        this.ar_data = response.data.var_data;
        this.series = response.data.series;


        this.$message.success("获取专题详情成功");
      } else {

        this.$message.error("获取专题详情失败");

      }

      this.initCharts();
    },
    // 获取字段下拉框数据
    fetchFieldOptions() {
      const tabTypes = {
        openSource: 2,
        event: 1,
        daily: 1,
        special: 1,
        theory: 1
      };

    },

    fetchOperatorOptions(fieldType) {
      if (!fieldType) {
        this.operatorOptions = [];
        return;
      }
      const params = { fieldType };
      operators(params)
        .then(response => {
          if (response?.code === 200) {
            this.operatorOptions = (response.data || []).map(item => ({
              label: item.label || '未命名操作符',
              value: item.value
            }));
          } else {
            this.operatorOptions = [];
          }
        })
        .catch(error => {
          console.error('获取操作符列表失败:', error);
          this.$message.error('获取操作符列表失败，请稍后重试');
          this.operatorOptions = [];
        });
    },




    // *******************************************************************
    to_ksh() {
      this.$router.push("/history/line");
    },
    to_gjjs() {
      this.$router.push("/concract/gjjs");

    },
    to_ztbg() {
      this.$router.push("/concract/ztbg");

    },
    to_index() {
      this.$router.push("/concract/document");

    },
    // /concract/ztbg
    // /concract/document
    handelqz() {
      // drawerfalse,
      this.drawer = true;
    },
    // **********************
    async handleQuery() {
      //  this.currentThematicId = data.id;
      const params = {
        id: this.currentThematicId,
        period: this.queryParams.period,
        startTime: this.queryParams.startDate,
        endTime: this.queryParams.endDate,
        // searchName: this.searchTopic,
        // ruleType: ''
      };
      let response = await selectTopicDetailById(params);
      if (response.code === 200) {
        this.description = response.data.description;
        this.daily = response.data.daily;
        this.event = response.data.event;
        this.openSource = response.data.openSource;
        this.research = response.data.research;
        this.special = response.data.special;
        this.timespan = response.data.timespan;
        this.timespanStartEnd = response.data.timespanStartEnd;
        this.peakTime = response.data.peakTime;
        this.peakTimeCount = response.data.peakTimeCount;
        this.keyPositiveEvents = response.data.keyPositiveEvents;
        this.keyPositiveEventsTime = response.data.keyPositiveEventsTime;
        this.keyPositiveEventsPopularity = response.data.keyPositiveEventsPopularity;
        this.keyNegativeEvents = response.data.keyNegativeEvents;
        this.keyNegativeEventsTime = response.data.keyNegativeEventsTime;
        this.keyNegativeEventsPopularity = response.data.keyNegativeEventsPopularity;
        this.categoryList = response.data.categoryList;
        this.sliderData = response.data.categoryList;
        this.isExist = response.data.isExist;
        this.thematicRelatedEntities = response.data.thematicRelatedEntities;
        this.data_x = response.data.abscissa;
        this.data_zm = response.data.positiveEmotionalTrend;
        this.data_zl = response.data.neutralEmotionalTrend;
        this.data_fm = response.data.negativeEmotionalTrend;
        this.hotTopicWordCloud = response.data.hotTopicWordCloud;
        this.originalEvenQuantity = response.data.originalEvenQuantity;
        this.weightedEventQuantity = response.data.weightedEventQuantity;
        this.sldata = response.data.thematicVolumeTrend;
        this.ar_data = response.data.var_data;
        this.series = response.data.series;

        this.$message.success("获取专题详情成功");
        // this.ruleForm.ruleSchedule.thematicId = data.id;
      } else {

        this.$message.error("获取专题详情失败");

      }
      // this.$message.success("查询成功");
      this.initCharts();
      this.resetQuery();
    },
    resetQuery() {
      this.queryParams = {
        startDate: "2025-04-17",
        endDate: "2025-05-15",
        period: "1",
      };
    },
    handleResize() {
      if (this.lineChart) this.lineChart.resize();
      if (this.sdChart) this.sdChart.resize();

      if (this.areaChart) this.areaChart.resize();
      if (this.pieChart) this.pieChart.resize();
      if (this.trendChart) this.trendChart.resize();
      if (this.slChart) this.slChart.resize();
      if (this.dtChart) this.dtChart.resize();
      if (this.rdpieChart) this.rdpieChart.resize();

    },
    initCharts() {
      this.initLineChart();
      this.initsdChart();
      this.initAreaChart();
      this.initPieChart();
      this.initTrendChart();
      this.initslChart();
      this.initdtChart();
      this.initrdpieChart();

    },
    initLineChart() {
      if (this.lineChart) this.lineChart.dispose();
      this.lineChart = echarts.init(this.$refs.lineChart);

      const option = {
        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#ddd",
          borderWidth: 1,
          textStyle: { color: "#333" },
        },
        legend: {

          top: 10,
          right: 20,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "15%",
          containLabel: true,
        },

        xAxis: {
          axisLabel: { color: "#fff" },
          type: "category",
          boundaryGap: false,
          // 实际上在后台获取data
          data: this.data_x,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#1C82C5',
            }
          },

        },
        yAxis: {
          type: "value",
          name: "（件）",
          nameTextStyle: {
            color: "#fff",
            fontSize: 13,
            // fontWeight: "bold",
            padding: [0, 0, 0, 30],
          },
          axisLine: {
            show: true,

            lineStyle: {
              color: '#1C82C5',
            }
          },
          // axisTick: { show: false },
          axisLabel: { color: "#fff" },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(28, 130, 197, .3)",  // 网格分割线颜色（半透明蓝色）
              type: "dashed",                   // 分割线类型为虚线
            }
          },
        },
        series: [
          {
            name: '原始事件',
            type: 'line',
            stack: 'Total',
            data: this.originalEvenQuantity,
            label: {
              show: true,
              color: '#ffffff',
              // 添加以下样式配置
              textStyle: {
                color: '#ffffff'  // 在这里设置字体颜色
              }
            }
          },
          {
            name: '加权事件',
            type: 'line',
            stack: 'Total',
            data: this.weightedEventQuantity,
            label: {
              show: true,
              color: '#ffffff',
              // 添加以下样式配置
              textStyle: {
                color: '#ffffff'  // 在这里设置字体颜色
              }
            }
          },
        ]
      };

      this.lineChart.setOption(option);
    },

    initsdChart() {
      if (this.sdChart) this.sdChart.dispose();
      this.sdChart = echarts.init(this.$refs.sdChart);
      const option = {
        legend: {
          bottom: 10,
          left: 'center',
          itemWidth: 9,
          itemHeight: 9,
          // data: ['2018溢价率', '2019溢价率']
        },
        xAxis: {
          axisLine: { //  改变x轴颜色

            lineStyle: {
              color: '#1C82C5',
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: { //  改变x轴字体颜色和大小
            textStyle: {
              color: "#fff",
              fontSize: 12
            },
          },
          splitLine: {
            show: false,

          },
        },
        yAxis: {
          name: '（件）',
          nameTextStyle: {
            color: '#fff',
            padding: [0, 25, 0, 0]
          },
          axisLine: { //  改变y轴颜色
            lineStyle: {
              color: '#1C82C5'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: { //  改变y轴字体颜色和大小
            //formatter: '{value} m³ ', //  给y轴添加单位
            textStyle: {
              color: "#fff",
              fontSize: 12
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(28, 130, 197, .3)",  // 网格分割线颜色（半透明蓝色）
              type: "dashed",                   // 分割线类型为虚线
            }
          },
        },
        series:
          // this.series,
          [{
            name: '2018溢价率',
            type: 'scatter',
            itemStyle: {
              color: "#3E9FFF",
            },
            symbolSize: 12,
            data: [
              [10.0, 8.04],
              [8.0, 6.95],
              [13.0, 7.58],
              [9.0, 8.81],
              [11.0, 8.33],
              [14.0, 9.96],
              [6.0, 7.24],
              [4.0, 4.26],
              [12.0, 10.84],
              [7.0, 4.82],
              [5.0, 5.68]
            ],
          }, {
            name: '2019溢价率',
            type: 'scatter',
            itemStyle: {
              color: "#F7B500",
            },
            symbolSize: 12,
            data: [
              [9.0, 5.04],
              [11.0, 7.95],
              [12.0, 8.58],
              [5.0, 11.81],
              [7.0, 12.33],
              [11.0, 7.96],
              [7.0, 9.24],
              [6.0, 8.26],
              [10.0, 11.84],
              [7.0, 3.82],
              [6.0, 4.68]
            ],
          }]



      };
      this.sdChart.setOption(option);
    },

    initAreaChart() {
      if (this.areaChart) this.areaChart.dispose();
      this.areaChart = echarts.init(this.$refs.areaChart);
      const option = {
        animation: true,
        animationDuration: 1800,
        animationEasing: "cubicOut",
        animationDelay: function (idx) {
          return idx * 100;
        },
        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#e4e4e4",
          borderWidth: 1,
          padding: 0,
          zIndex: 1000,
          textStyle: {
            color: "#333",
            fontSize: 14,
          },
          extraCssText:
            "box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); border-radius: 6px;",
          formatter: function (params) {
            var result =
              '<div style="padding:8px;background-color:rgba(255,255,255,0.95);border-radius:6px;box-shadow:0 3px 10px rgba(0,0,0,0.15);min-width:180px;">' +
              '<div style="font-size:16px;font-weight:bold;color:#333;margin-bottom:8px;text-align:center;padding-bottom:6px;">' +
              params[0].name +
              "</div>" +
              '<div style="padding:4px 0;">';
            params.forEach(function (item, index) {
              result +=
                '<div style="display:flex;align-items:center;margin:6px 0;">' +
                '<div style="width:12px;height:12px;border-radius:50%;background-color:' +
                item.color +
                ';margin-right:8px;box-shadow:0 1px 3px rgba(0,0,0,0.2);"></div>' +
                '<div style="color:' +
                (index === 0 ? "#409eff" : "#67c234") +
                ';font-weight:bold;margin-right:10px;flex:1;">' +
                item.seriesName +
                "</div>" +
                '<div style="font-weight:bold;color:#333;font-size:15px;">' +
                item.value +
                "件</div>" +
                "</div>";
            });
            result += "</div></div>";
            return result;
          },
        },
        legend: {
          data: [
            {
              name: "中立",
              icon: "rect",
              textStyle: {
                color: "#377dff",
                fontSize: 14,
                padding: [0, 0, 0, 5],
                fontWeight: "bold",
              },
            },
            {
              name: "正面",
              icon: "rect",
              textStyle: {
                color: "#5fd193",
                fontSize: 14,
                padding: [0, 0, 0, 5],
                fontWeight: "bold",
              },
            },
            {
              name: "负面",
              icon: "rect",
              textStyle: {
                color: "#ff0000",
                fontSize: 14,
                padding: [0, 0, 0, 5],
                fontWeight: "bold",
              },
            },
          ],
          width: "100%",
          left: "center",
          top: "bottom",
          textStyle: {
            color: "#000",
            padding: [0, 0, 0, 5],
          },
          itemStyle: {
            borderWidth: 0,
          },
          itemWidth: 24,
          itemHeight: 12,
          itemGap: 35,
          backgroundColor: "rgba(255, 255, 255, 0.8)",
          borderRadius: 6,
          borderColor: "#f0f0f0",
          borderWidth: 1,
          padding: 10,
        },
        grid: {
          left: "5%",
          right: "5%",
          bottom: "22%",
          top: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: true,
          data: this.data_x,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#1C82C5",
            },
          },
          axisTick: {
            show: false,
            // lineStyle: {
            //   color: "#e0e6ed",
            // },
          },
          axisLabel: {
            color: "#556677",
            fontSize: 13,
            margin: 15,
          },
        },
        yAxis: {
          type: "value",
          name: "（件）",
          nameTextStyle: {
            color: "#fff",
            fontSize: 13,
            // fontWeight: "bold",
            padding: [0, 0, 0, 30],
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#1C82C5",
            },
          },
          axisTick: {
            show: false,
            // lineStyle: {
            //   color: "#e0e6ed",
            // },
          },
          axisLabel: {
            formatter: "{value}件",
            textStyle: {
              color: "#556677",
              fontSize: 13,
            },
            margin: 15,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(28, 130, 197, .3)",  // 网格分割线颜色（半透明蓝色）
              type: "dashed",                   // 分割线类型为虚线
            }
          },
        },
        series: [
          {
            name: "中立",
            type: "line",
            symbolSize: 8,
            symbol: "circle",
            smooth: false,
            color: "#377dff",
            yAxisIndex: 0,
            showSymbol: true,
            showAllSymbol: true,
            lineStyle: {
              width: 4,
              color: "#377dff",
              shadowColor: "rgba(55, 125, 255, 0.3)",
              shadowBlur: 10,
              shadowOffsetY: 5,
            },
            areaStyle: {
              color: "rgba(55, 125, 255, 0.1)",
            },
            emphasis: {
              focus: "series",
              itemStyle: {
                borderWidth: 3,
                borderColor: "rgba(55, 125, 255, 0.5)",
                shadowColor: "rgba(0, 0, 0, 0.3)",
                shadowBlur: 10,
                scale: true,
              },
              lineStyle: {
                width: 5,
              },
            },
            data: this.data_zl,
          },
          {
            name: "正面",
            type: "line",
            smooth: false,
            symbolSize: 8,
            symbol: "circle",
            color: "#5fd193",
            yAxisIndex: 0,
            showSymbol: true,
            showAllSymbol: true,
            lineStyle: {
              width: 4,
              color: "#5fd193",
              shadowColor: "rgba(95, 209, 147, 0.3)",
              shadowBlur: 10,
              shadowOffsetY: 5,
            },
            areaStyle: {
              color: "rgba(95, 209, 147, 0.1)",
            },
            emphasis: {
              focus: "series",
              itemStyle: {
                borderWidth: 3,
                borderColor: "rgba(95, 209, 147, 0.5)",
                shadowColor: "rgba(0, 0, 0, 0.3)",
                shadowBlur: 10,
                scale: true,
              },
              lineStyle: {
                width: 5,
              },
            },
            data: this.data_zm,
          },
          {
            name: "负面",
            type: "line",
            smooth: false,
            symbolSize: 8,
            symbol: "circle",
            color: "#ff0000",
            yAxisIndex: 0,
            showSymbol: true,
            showAllSymbol: true,
            lineStyle: {
              width: 4,
              color: "#ff0000",
              shadowColor: "rgba(255, 171, 0, 0.3)",
              shadowBlur: 10,
              shadowOffsetY: 5,
            },
            areaStyle: {
              color: "rgba(255, 0, 0, 0.1)",
            },
            emphasis: {
              focus: "series",
              itemStyle: {
                borderWidth: 3,
                borderColor: "rgba(255, 171, 0, 0.5)",
                shadowColor: "rgba(0, 0, 0, 0.3)",
                shadowBlur: 10,
                scale: true,
              },
              lineStyle: {
                width: 5,
              },
            },
            data: this.data_fm,
          },
        ],
      };
      this.areaChart.setOption(option);
    },
    initPieChart() {
      if (this.pieChart) this.pieChart.dispose();
      this.pieChart = echarts.init(this.$refs.pieChart);
      const datas = this.thematicRelatedEntities;
      const option = {
        tooltip: {
          show: true,
          position: 'top',
          textStyle: {
            fontSize: 30
          }
        },
        series: [{
          type: "wordCloud",
          // 网格大小，各项之间间距
          gridSize: 30,
          // 形状 circle 圆，cardioid  心， diamond 菱形，
          // triangle-forward 、triangle 三角，star五角星
          shape: 'circle',
          // 字体大小范围
          sizeRange: [20, 50],
          // 文字旋转角度范围
          rotationRange: [0, 90],
          // 旋转步值
          rotationStep: 90,
          // 自定义图形
          // maskImage: maskImage,
          left: 'center',
          top: 'center',
          right: null,
          bottom: null,
          // 画布宽
          width: '90%',
          // 画布高
          height: '80%',
          // 是否渲染超出画布的文字
          drawOutOfBound: true,
          textStyle: {
            normal: {
              color: function () {
                return 'rgb(' + [
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55)
                ].join(',') + ')';
              }
            },
            emphasis: {
              shadowBlur: 10,
              shadowColor: '#2ac'
            }
          },

          data: datas,

        }]
      };

      this.pieChart.setOption(option);
    },
    initTrendChart() {
      if (this.trendChart) this.trendChart.dispose();
      this.trendChart = echarts.init(this.$refs.trendChart);
      // 事件类型分布图
      let normaldata = [
        {
          name: '化肥企业',
          value: 38
        },
        {
          name: '农药企业',
          value: 30
        },
        {
          name: '种子企业',
          value: 22
        },
        {
          name: '农机销售',
          value: 10
        }
      ];

      // 计算总和（只计算实际数据）
      let sum = this.ar_data.reduce((total, item) => total + item.value, 0);
      // total
      // 预先计算每个数据项的正确百分比
      const percentages = this.ar_data.map(item => ({
        ...item,
        percent: (item.value / sum * 100).toFixed(1)
      }));

      // 创建用于显示的数据，包含空项用于间距
      let valdata = [];
      percentages.forEach((item, index) => {
        valdata.push({
          value: item.value,
          name: item.name,
          // 存储正确的百分比
          _percent: item.percent
        });
        // 配置一个空值用于间距
        valdata.push({
          name: '',
          value: 1, // 设置为0，不影响百分比计算
          itemStyle: { color: 'transparent' },
          _percent: 0 // 标记为0百分比
        });
      });

      // 颜色配置
      let colorList = [
        '#E77655',
        "#1FFFD7",
        "#108CFF",
        "#DAAAFF",
        'rgba(208, 255, 255, 1)',
        'rgba(255, 235, 32, 1)',
        'rgba(22, 160, 255, 1)',
        'rgba(29, 255, 213, 1)',

      ];

      const colorList1 = [];
      const colorList2 = [];
      colorList.forEach((item) => {
        colorList1.push(item)
        colorList1.push('transparent')
        colorList2.push(item.replace(/,\s*\d+(\.\d+)?\)/, ", 0.3)"))
        colorList2.push('transparent')
      });
      const option = {
        title: {
          text: "{b|总事件数}\n{a|" + sum + "}",
          left: "49%",
          top: "40%",
          itemGap: 10,
          textStyle: {
            rich: {
              b: {
                color: "#fff",
                fontSize: 24,
                padding: 10
              },
              a: {
                color: "#fff",
                fontSize: 32,
                fontWeight: 700
              }
            }
          },
          textAlign: "center"
        },
        series: [
          {
            type: 'pie',
            zlevel: 3,
            radius: ['39%', '48%'],
            center: ['50%', '50%'],
            itemStyle: {
              color: function (params) {
                return colorList2[params.dataIndex % colorList2.length];
              }
            },
            label: { show: false },
            data: valdata
          },
          {
            type: 'pie',
            zlevel: 1,
            silent: true,
            radius: ['52%', '83%'],
            center: ['50%', '50%'],
            itemStyle: {
              color: function (params) {
                return colorList1[params.dataIndex % colorList1.length];
              }
            },
            label: {
              padding: [0, -80],
              alignTo: 'labelLine',
              formatter: (params) => {
                // 只对实际数据显示标签
                if (params.data.name) {
                  // 使用预先计算好的正确百分比
                  return `{dot|}   {d|${params.data._percent}%}\n\n{c|${params.name} }`;
                }
                return '';
              },
              rich: {
                c: {
                  color: 'rgba(255, 255, 255, 1)',
                  fontSize: 14,
                  lineHeight: 20,
                },
                d: {
                  fontSize: 14,
                  color: 'rgba(255, 255, 255, .7)'
                },
                dot: {
                  backgroundColor: 'auto',
                  width: 0,
                  height: 0,
                  borderRadius: 3,
                  fontSize: 16,
                  padding: [3, -3, 3, -3]
                }
              }
            },
            labelLine: {
              length: 50,
              length2: 70,
              show: function (params) {
                return params.data.name !== '';
              }
            },
            data: valdata
          },
          {
            type: 'pie',
            radius: ['86%', '86.3%'],
            center: ['50%', '50%'],
            hoverAnimation: false,
            itemStyle: {
              color: 'rgba(201, 254, 240, 0.15)'
            },
            label: { show: false },
            data: [{ value: 1 }]
          },
        ]
      };
      this.trendChart.setOption(option);
    },
    initslChart() {
      if (this.slChart) this.slChart.dispose();
      this.slChart = echarts.init(this.$refs.slChart);
      const option = {

        legend: {
          top: "4%",
          right: "2%",
          itemWidth: 18,
          itemHeight: 10,
          itemGap: 30,
          textStyle: {
            fontSize: 12,
            color: "#fff",
            padding: [0, 0, 0, 10],
          },
        },
        grid: {
          top: "18%",
          left: "4%",
          right: "3%",
          bottom: "2%",
          containLabel: true,
        },


        xAxis: [
          {
            type: "category",
            // 对数据进行采样，只取5个点
            data: this.data_x.length > 5 ? this.getSampledData(this.data_x, 5) : this.data_x,
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              lineStyle: {
                color: "#1C82C5",
              },
            },
            axisLabel: {
              interval: 0,
              margin: 10,
              color: "#05D5FF",
              textStyle: {
                fontSize: 14,
                color: "#fff",
              },
            },
          },
        ],
        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#e4e4e4",
          borderWidth: 1,
          padding: 0,
          zIndex: 1000,
          textStyle: {
            color: "#333",
            fontSize: 14,
          },
          extraCssText:
            "box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); border-radius: 6px;",
          formatter: function (params) {
            var result =
              '<div style="padding:8px;background-color:rgba(255,255,255,0.95);border-radius:6px;box-shadow:0 3px 10px rgba(0,0,0,0.15);min-width:180px;">' +
              '<div style="font-size:16px;font-weight:bold;color:#333;margin-bottom:8px;text-align:center;padding-bottom:6px;">' +
              // params[0].name +
              "</div>" +
              '<div style="padding:4px 0;">';
            params.forEach(function (item, index) {
              result +=
                '<div style="display:flex;align-items:center;margin:6px 0;">' +
                '<div style="width:12px;height:12px;border-radius:50%;background-color:' +
                item.color +
                ';margin-right:8px;box-shadow:0 1px 3px rgba(0,0,0,0.2);"></div>' +
                '<div style="color:' +
                (index === 0 ? "#409eff" : "#67c234") +
                ';font-weight:bold;margin-right:10px;flex:1;">' +
                // item.seriesName +
                "</div>" +
                '<div style="font-weight:bold;color:#333;font-size:15px;">' +
                item.value +
                "件</div>" +
                "</div>";
            });
            result += "</div></div>";
            return result;
          },
        },
        yAxis: [
          {
            type: "value",
            name: "(件)",
            // 设置纵坐标刻度间隔为1
            interval: 1,
            min: 0, // 建议设置最小值，确保从0开始
            // 移除 splitNumber，使用 interval 控制刻度间隔
            nameTextStyle: {
              color: "#fff",
              fontSize: 13,
              align: "center",
              padding: [0, 0, 0, 30],
            },
            axisLabel: {
              formatter: "{value}件",
              textStyle: {
                color: "#fff",
                fontSize: 13,
              },
              margin: 15,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#1C82C5",
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(28, 130, 197, .3)",  // 网格分割线颜色（半透明蓝色）
                type: "dashed",                   // 分割线类型为虚线
              }
            },
          },
          { type: 'value', show: true }
        ],
        series: [
          {
            type: "line",
            showSymbol: false,
            symbolSize: 8,
            lineStyle: {
              normal: {
                color: "#2073fe",
                smooth: true,
              },
            },
            itemStyle: {
              color: "#40acff",
              borderColor: "#40acff",
              borderWidth: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 1,
                    color: 'transparent'
                  },
                  {
                    offset: 0,
                    color: '#051b47'
                  }
                ]
              }
            },
            data: this.sldata,
          },
        ],
      };
      this.slChart.setOption(option);
    },
    getSampledData(data, count) {
      if (data.length <= count) return data;

      const sampled = [];
      const step = Math.floor(data.length / count);

      for (let i = 0; i < count; i++) {
        const index = Math.min(i * step, data.length - 1);
        sampled.push(data[index]);
      }

      return sampled;
    },
    initdtChart() {
      if (this.dtChart) this.dtChart.dispose();
      this.dtChart = echarts.init(this.$refs.dtChart);
      const option = {
      }
      this.dtChart.setOption(option);

    },


    initrdpieChart() {
      if (this.rdpieChart) this.rdpieChart.dispose();
      this.rdpieChart = echarts.init(this.$refs.rdpieChart);
      const datas = this.hotTopicWordCloud;
      const option = {
        tooltip: {
          show: true,
          position: 'top',
          textStyle: {
            fontSize: 30
          }
        },
        series: [{
          type: "wordCloud",
          // 网格大小，各项之间间距
          gridSize: 30,
          // 形状 circle 圆，cardioid  心， diamond 菱形，
          // triangle-forward 、triangle 三角，star五角星
          shape: 'circle',
          // 字体大小范围
          sizeRange: [20, 50],
          // 文字旋转角度范围
          rotationRange: [0, 90],
          // 旋转步值
          rotationStep: 90,
          // 自定义图形
          // maskImage: maskImage,
          left: 'center',
          top: 'center',
          right: null,
          bottom: null,
          // 画布宽
          width: '90%',
          // 画布高
          height: '80%',
          // 是否渲染超出画布的文字
          drawOutOfBound: false,
          textStyle: {
            normal: {
              color: function () {
                return 'rgb(' + [
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55)
                ].join(',') + ')';
              }
            },
            emphasis: {
              shadowBlur: 10,
              shadowColor: '#2ac'
            }
          },

          data: datas,

        }]
      };

      this.rdpieChart.setOption(option);


    },

  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  min-height: 100vh;
}

/* 美化页面标题，添加渐变效果和标签导航 */
.page-header {
  margin-bottom: 20px;
  /* background: rgba(255, 255, 255, 0.95); */
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.page-title {
  /* font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 16px 0; */
  font-family: SourceHanSansSC-Bold;
  font-size: 24px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 700;
  /* margin: 0 0 16px 0; */
}

.page-tabs {
  display: flex;
  gap: 8px;
  height: 50px;
  /* width:132px; */
}

.tab-item {

  font-family: SourceHanSansSC-Regular;
  font-size: 18px;
  color: #A9C7EA;
  letter-spacing: 0;
  font-weight: 400;
  /* align-items: center; */
  /* text-align: center; */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 20px;
  /* 根据需要调整内边距 */
  cursor: pointer;
}

.tab-item:hover {
  background-image: linear-gradient(179deg, rgba(27, 126, 242, 0.00) 0%, rgba(27, 126, 242, 0.20) 100%);
  border-bottom: #66CCFF 2px solid;

}

.tab-item.active {
  background-image: linear-gradient(179deg, rgba(27, 126, 242, 0.00) 0%, rgba(27, 126, 242, 0.20) 100%);
  border-bottom: #66CCFF 2px solid;
}

/* 添加描述文本区域样式 */
.description-box {
  background: rgba(27, 126, 242, 0.10);
  color: white;

  padding: 16px 24px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #667eea;
}




.search-card {
  margin-bottom: 16px;
  border: none;
  background: rgba(27, 126, 242, 0.10);

}

.stats-row {
  margin-bottom: 16px;
}

/* 重新设计统计卡片，使用渐变背景和图标 */
.stat-card {
  background: rgba(27, 126, 242, 0.10);
  padding: 16px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  min-width: 180px;
}

.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  font-size: 28px;
  color: white;
  transition: all 0.3s;
}

.stat-card:hover .stat-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
}

.stat-card-blue .stat-icon-wrapper {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card-green .stat-icon-wrapper {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stat-card-orange .stat-icon-wrapper {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card-red .stat-icon-wrapper {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-card-purple .stat-icon-wrapper {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card-cyan .stat-icon-wrapper {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  /* font-size: 20px;
  font-weight: 700;
  color: #303133;
  line-height: 1.2; */
  font-family: "LetsgoDigital-Regular", sans-serif;
  font-size: 28px;
  color: #FFFFFF;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 28px;
  background: linear-gradient(to bottom,
      rgba(255, 255, 255, 1) 0%,
      rgba(204, 216, 242, 1) 50%,
      rgba(56, 105, 204, 1) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.stat-value .unit {
  font-size: 16px;
  font-weight: 500;
  margin-left: 4px;
  color: #909399;
}

.stat-date {
  font-size: 12px;
  color: #c0c4cc;
  margin-top: 8px;
}

.chart-row {
  margin-bottom: 16px;
}

.chart-card {
  border-radius: 12px;
  margin-bottom: 16px;
  background: rgba(27, 126, 242, 0.10);

  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
}

.chart-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: none;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: SourceHanSansSC-Regular;
  font-size: 18px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.chart-title i {
  /* color: #fff; */
  font-family: SourceHanSansSC-Regular;
  font-size: 18px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.chart-actions {
  display: flex;
  gap: 10px;
}

.chart-container {
  width: 100%;
  height: 400px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-tabs {
    flex-wrap: wrap;
  }

  .chart-container {
    height: 300px;
  }

  .stat-value {
    font-size: 28px;
  }

  .stat-icon-wrapper {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }
}

/* 其他样式 */

.main-row {
  height: 100%;
  min-height: calc(100vh - 60px);
  padding: 15px;
}

.left-panel,
.right-panel {
  height: 100%;
}

.left-card,
.right-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* .search-input {
  margin-bottom: 15px;
} */

.topic-list-wrapper {
  max-height: 400px;
  overflow-y: auto;
  margin-top: 15px;
}


.topic-item {
  padding: 12px;
  /* border: 1px solid #ebeef5; */
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.topic-item:hover {
  /* border-color: #409EFF;
  background-color: #f5f7fa; */
}

.topic-item-active {
  /* border-color: #409EFF;
  background-color: #ecf5ff; */
}

.topic-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.topic-name {
  /* font-weight: 500; */
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.topic-count {
  font-family: LetsgoDigital-Regular;
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 700;
}

.topic-setting {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  color: #909399;
  cursor: pointer;
  transition: color 0.2s;
  padding: 0;
}

.topic-setting:hover {
  color: #409eff;
}

.topic-empty {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.content-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 5px 0;
}

.pagination {
  margin-top: 15px;
  text-align: right;
}

::v-deep .el-tabs__content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

::v-deep .el-tab-pane {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 预览抽屉样式 */
::v-deep .data-preview-drawer {
  width: 25% !important;
}

::v-deep .data-preview-drawer .el-drawer__body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100% - 56px);
}

::v-deep .data-preview-drawer .el-drawer__header {
  padding: 14px 20px;
  border-bottom: 1px solid #eee;
}

.history_log {
  font-family: YouSheBiaoTiHei;
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  /* text-shadow: 0 2px 4px #1B1A85; */
  font-weight: 400;
  /*  */
  font-family: SourceHanSansSC-Bold;
  font-size: 20px;
  letter-spacing: 0;
  font-weight: 700;
  background: -webkit-gradient(linear, left top, left bottom, from(#FFFFFF), to(#0091FF));
  background: linear-gradient(to bottom, #FFFFFF, #0091FF);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 5px;
  margin-top: 25px
}

.el-card {
  border: none;
}

.blue {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  height: 50px !important;
  line-height: 50px !important;
  color: #ffffff !important;
  border: none !important;

}

::v-deep .el-input__inner {

  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  height: 50px !important;
  line-height: 50px !important;
  color: #ffffff !important;
}

.left-panel {
  /* background: rgba(27, 126, 242, 0.10); */
  height: 1000px;
}

.left-card {
  height: 100%;
  background: rgba(27, 126, 242, 0.10);
  border: none;
}



::v-deep.el-radio-button__inner {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 0px 10px 10px 0px;
}

::v-deep.el-radio-button__inner:hover {
  background-image: linear-gradient(180deg, #78B8FF 0%, #002BA9 100%);
  border-radius: 10px 0px 0px 10px;
}

::v-deep.el-radio-button__inner.is-active {
  background-image: linear-gradient(180deg, #78B8FF 0%, #002BA9 100%);
  border-radius: 10px 0px 0px 10px;
}

/* <style scoped> */
.chart-header {
  display: flex;
  justify-content: space-between;
  /* 将标题和按钮组分开 */
  align-items: center;
  /* 垂直居中对齐 */
}

.chart-actions {
  display: flex;
  align-items: center;
  /* 垂直居中对齐 */
}

.custom-radio-group {
  display: inline-flex;
  justify-content: flex-end;
  /* 将子元素推到右侧 */
}

.custom-radio-button {
  padding: 8px 16px;
  /* 根据需要调整内边距 */
  border: 1px solid #dcdcdc;
  /* 设置边框颜色 */

  background-color: #fff;
  /* 设置背景颜色 */
  /* color: #606266; */
  /* 设置文本颜色 */
  cursor: pointer;
  /* 设置鼠标指针样式 */

  /*  */
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  color: #fff;

  /* border-radius: 0px 10px 10px 0px;   */
}

.custom-radio-button.is-active {
  background-color: #409eff;
  /* 设置激活时的背景颜色 */
  color: #fff;
  /* 设置激活时的文本颜色 */
  border-color: #409eff;
  /* 设置激活时的边框颜色 */
  background-image: linear-gradient(180deg, #78B8FF 0%, #002BA9 100%);
  /* border-radius: 10px 0px 0px 10px; */
}

.mr10 {
  margin-right: 10px;
  /* 设置图标右边距 */
}

.block {
  margin-left: 15px;
  margin-top: 10px;
}

.custom-drawer {
  /* background-color: #52b31f !important; */
}

.drawer-content {
  padding: 20px;
}

.slider-block {
  margin-bottom: 20px;
}

.slider-label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
}

.el-slider {
  width: 100%;
}

::v-deep .el-card__header {
  border: none;
}
</style>