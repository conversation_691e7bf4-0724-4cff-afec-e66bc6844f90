<template>
  <!-- 关联分析界面 -->
  <div class="app-container">
    <!-- 头部历史记录+创建任务按钮  start -->
    <div class="ac_header">
      <!-- <div class="ach_left">
        <p class="title">关联分析任务（条）</p>
        <p class="nums">{{ total }}</p>
      </div> -->
      <div class="ach_right" @click="handleAdd">
        <div class="public_button position_add_btn search_btn">
          <i class="mr10 el-icon-circle-plus-outline"></i>新建任务
        </div>
      </div>
      <div style="clear: both"></div>
      <!-- 清除浮动 -->
    </div>
    <!-- 头部历史记录+创建任务按钮  end -->

    <!-- 搜索栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      class="search_form"
      v-show="showSearch"
    >
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务信息"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <div
          class="public_button clear_button fl position_btn"
          @click="resetQuery"
        >
          <i class="mr10 el-icon-refresh"></i>重置
        </div>

        <div
          @click="handleQuery"
          class="public_button search_btn fl position_btn"
        >
          <i class="mr10 el-icon-search"></i>应用筛选
        </div>
        <!-- <div
          @click="handleExport"
          v-hasPermi="['data:datain:batchExport']"
          class="public_button export_button fl position_btn"
        >
          <i class="mr10 el-icon-upload2"></i>批量导出
        </div> -->
        <div
          @click="handleDelete"
          v-hasPermi="['data:datain:batchDelete']"
          class="public_button delete_button fl position_btn"
        >
          <i class="mr10 el-icon-delete"></i>批量删除
        </div>
      </el-form-item>
    </el-form>

    <!-- 列表页 -->
    <el-table
      v-loading="loading"
      :data="taskList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column
        label="涉及专题"
        prop="themes"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ formatThemes(scope.row.themes) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建人"
        prop="createByName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="创建时间"
        align="center"
        sortable
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分析进度" prop="progress" width="120">
        <template slot-scope="scope">
          <el-progress
            :percentage="scope.row.progress || 0"
            :status="scope.row.progress === 100 ? 'success' : ''"
          ></el-progress>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新建任务弹框 -->
    <el-dialog
      title="新建关联分析任务"
      :visible.sync="createDialogVisible"
      width="900px"
      append-to-body
      :show-close="false"
    >
      <el-form
        ref="createForm"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
        class="dialog_form_style"
      >
        <div
          v-for="(theme, index) in createForm.themes"
          :key="index"
          class="theme-item"
        >
          <div class="theme-item-header">
            <span class="theme-item-title">专题 {{ index + 1 }}</span>
            <el-button
              v-if="createForm.themes.length > 1"
              type="text"
              icon="el-icon-delete"
              @click="removeTheme(index)"
              class="remove-theme-btn"
              >删除</el-button
            >
          </div>

          <el-form-item
            :label="'选择专题'"
            :prop="`themes.${index}.thematicId`"
            :rules="{
              required: true,
              message: '请选择专题',
              trigger: 'change',
            }"
          >
            <el-select
              v-model="theme.thematicId"
              placeholder="请选择专题"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="item in thematicOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="是否过滤">
            <el-radio-group v-model="theme.isFilter">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <template v-if="theme.isFilter">
            <el-form-item label="过滤方式">
              <el-select
                v-model="theme.filterType"
                placeholder="请选择过滤方式"
                style="width: 100%"
              >
                <el-option label="关键词过滤" value="keyword" />
                <el-option label="标签过滤" value="tag" />
              </el-select>
            </el-form-item>

            <el-form-item v-if="theme.filterType === 'tag'" label="过滤条件">
              <el-select
                v-model="theme.filterTags"
                multiple
                placeholder="请选择标签（可多选）"
                style="width: 100%"
              >
                <el-option
                  v-for="tag in tagOptions"
                  :key="tag.id"
                  :label="tag.name"
                  :value="tag.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              v-if="theme.filterType === 'keyword'"
              label="过滤条件"
            >
              <el-input
                v-model="theme.filterKeywords"
                type="textarea"
                :rows="3"
                placeholder="请输入关键词，多个关键词用逗号分隔"
              />
            </el-form-item>
          </template>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <div
          class="public_button clear_button fr position_btn"
          @click="cancelCreate"
        >
          <i class="mr10 el-icon-close"></i>取 消
        </div>

        <div
          @click="submitCreate"
          class="public_button search_btn fr position_btn"
        >
          <i class="mr10 el-icon-finished"></i>确 定
        </div>
        <div
          @click="addTheme"
          class="public_button export_button fr position_btn"
        >
          <i class="mr10 el-icon-circle-plus"></i>添加专题
        </div>
        <div style="clear: both"></div>
      </div>
    </el-dialog>

    <!-- 查看分析结果弹框 -->
    <el-dialog
      title="分析结果查看"
      :visible.sync="viewDialogVisible"
      width="1400px"
      append-to-body
      :show-close="false"
      class="view-dialog"
    >
      <p class="dialogTitle">对主题、专题关联数据进行多维度的统计和分析</p>
      <div class="dialogViewBody">
        <!-- 任务基础信息 -->
        <div class="task-info-section">
          <h3 class="section-title">任务基础信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="分析的专题">
              {{ currentTask.themes || "暂无" }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ currentTask.createTime || "暂无" }}
            </el-descriptions-item>
            <el-descriptions-item label="涉及舆情数量">
              {{ currentTask.opinionCount || 0 }} 条
            </el-descriptions-item>
            <el-descriptions-item label="关联度评分">
              {{ currentTask.score || 0 }} 分
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 分析结果 -->
        <div class="analysis-results">
          <h3 class="section-title">分析结果</h3>

          <!-- 第一块：逻辑关系图 -->
          <div class="chart-section">
            <h4 class="chart-title">逻辑关系图</h4>
            <div class="chart-container">
              <TopologyGraph :graph-data="familyGraphData" :option="customOption"/>
            </div>
          </div>

          <!-- 第二块：涉事主体关系图 -->
          <div class="chart-section">
            <h4 class="chart-title">涉事主体关系</h4>
            <div class="chart-container">
              <RelationCharts :graph-data="educationGraphData"/>
            </div>
          </div>

          <!-- 第三块：时间线比较 -->
          <div class="chart-section">
            <h4 class="chart-title">时间线比较</h4>
            <!-- <el-tabs v-model="timelineTab" @tab-click="handleTimelineTab">
              <el-tab-pane label="重点" name="key"></el-tab-pane>
              <el-tab-pane label="全部" name="all"></el-tab-pane>
            </el-tabs> -->
            <div class="chart-container">
              <TimeLine :timeline-data="policyData" :option="customOption2"/>
            </div>
          </div>

          <!-- 第四块：饼图 -->
          <div class="chart-section">
            <h4 class="chart-title">专题分布</h4>
            <!-- <div ref="pieChart" class="chart-container"></div> -->
            <div class="chart-container">
              <OldDeviceChart />
            </div>
          </div>

          <!-- 第五块：词云图 -->
          <div class="chart-section">
            <h4 class="chart-title">词云图</h4>
            <div ref="wordCloudChart" class="chart-container"></div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <div
          class="public_button clear_button fr position_btn"
          @click="viewDialogVisible = false"
        >
          <i class="mr10 el-icon-close"></i>关 闭
        </div>
        <div style="clear: both"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from "echarts";
import "echarts-wordcloud";
import OldDeviceChart from '@/views/components/OldDeviceChart.vue'
import TopologyGraph from '@/views/components/TopologyGraph.vue'
import RelationCharts from '@/views/components/RelationCharts.vue'
import TimeLine from '@/views/components/TimeLine.vue'
// import TimeLine from '../../components/TimeLine.vue';
// import { parseTime } from "@/utils";
// 你的原始数据
const rawList = [
    {
        policy_content: '规定对处方药和非处方药分类管理。',
        policy_country: '中国',
        policy_time: '1999-06-11',
        policy_title: '《处方药与非处方药分类管理办法》（试行)',
    },
    {
        policy_content: '规定药品生产、经营企业、医疗机构应当对其生产、经营、使用的药品质量负责。',
        policy_country: '中国',
        policy_time: '2007-01-31',
        policy_title: '《药品流通监督管理方法》（国家食品药品监督管理局令第26号',
    },
    {
        policy_content:
            '《意见》指出通过生物医药园的建设，研究开发具有自主知识产权的新药及其他医药产品；孵化新型医药企业，为从事生物技术与医药领域技术孵化、中试的机构和企业提供一流、全方位、专业化的保障服务和条件支撑；发展以生物医药为主体，中药、化药为补充，医药贸易为纽带的新型医药产业链。',
        policy_country: '中国',
        policy_time: '2007-06-22',
        policy_title: '《关于共同建设国家生物医药国际创新园的意见》',
    },
    {
        policy_content: '进一步明确了药品生产、经营企业的管理规范行为',
        policy_country: '中国',
        policy_time: '2016-02-06',
        policy_title: '《中华人民共和国药品管理法实施条例》（2016年修订版）',
    },
    {
        policy_content: '将化药新药分为创新药和改良型新药，将原来新药定义的“中国新”变成了“全球性”',
        policy_country: '中国',
        policy_time: '2016-03-09',
        policy_title: '《关于发布化学药品注册分类改革工作方案的公告》',
    },
    {
        policy_content: '《意见》明确提出了优化服务环境、提升服务能力、营造生物医药产业创新发展的34条具体措施和办法。',
        policy_country: '中国',
        policy_time: '2016-03-18',
        policy_title: '《优化服务环境助推生物医药产业创新发展的意见》。',
    },
    {
        policy_content:
            '推进生物药、化学药新品种、优质中药、高性能医疗器械、新型辅料包材和制药设备六大重点领域发展，加快各领域新技术的开发和应用，促进产品、技术、质量升级',
        policy_country: '中国',
        policy_time: '2016-10-26',
        policy_title: '《医药工业发展规划指南》',
    },
    {
        policy_content:
            '《规划》确定了加快生物产业方面的发展任务。提出构建生物医药新体系、提升生物医学工程发展水平，到2020年，生物产业规模达到8—10万亿元，形成一批具有较强国际竞争力的新型生物技术企业和生物经济集群。',
        policy_country: '中国',
        policy_time: '2016-11-29',
        policy_title: '《“十三五”国家战略性新兴产业发展规划》',
    },
    {
        policy_content:
            '《意见》在生产方面强调严格药品上市审评审批，加快推进已上市仿制药质量和疗效一致性评价，对通过一致性评价的药品给予政策支持等。流通方面推动药品流通企业转型升级，推行药品购销“两票制”，使中间环节加价透明化等。',
        policy_country: '中国',
        policy_time: '2017-02-09',
        policy_title: '《关于进一步改革完善药品生产流通使用政策的若干建议》',
    },
    // ... 此处省略其他数据 ...
];
export default {
  name: "AssociationAnalysisIndex",
  components: { OldDeviceChart, TopologyGraph, RelationCharts, TimeLine },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 任务列表
      taskList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: undefined,
        handlingStrategies: "",
        kssj: undefined,
        jssj: undefined,
      },
      // 新建任务弹框
      createDialogVisible: false,
      createForm: {
        themes: [
          {
            thematicId: undefined,
            isFilter: false,
            filterType: "keyword",
            filterTags: [],
            filterKeywords: "",
          },
        ],
      },
      createRules: {},
      // 专题选项（测试数据）
      thematicOptions: [
        { id: 1, name: "专题A：农业政策分析" },
        { id: 2, name: "专题B：市场动态监测" },
        { id: 3, name: "专题C：技术发展趋势" },
        { id: 4, name: "专题D：行业风险评估" },
        { id: 5, name: "专题E：政策影响评估" },
      ],
      // 标签选项（测试数据）
      tagOptions: [
        { id: 1, name: "重要" },
        { id: 2, name: "紧急" },
        { id: 3, name: "关注" },
        { id: 4, name: "预警" },
        { id: 5, name: "热点" },
      ],
      // 查看弹框
      viewDialogVisible: false,
      currentTask: {},
      timelineTab: "key",
      pieChart: null,
      wordCloudChart: null,
      // 符合封装后数据格式的图数据
      familyGraphData: {
        categories: [
          { name: '亲人' },
          { name: '租户', symbol: 'rect' }
        ],
        nodes: [
          { id: 'li-fugui', name: '李富贵', category: '亲人', data: { age: 50, job: '企业家' } },
          { id: 'wang-guihua', name: '王桂花', category: '亲人', data: { age: 48, job: '教师' } },
          { id: 'li-sisi', name: '李思思', category: '亲人', symbolSize: 60, data: { age: 22, job: '学生' } },
          { id: 'house-self', name: '自住房屋', symbol: 'circle', data: { address: '北京市朝阳区' } },
          { id: 'car', name: '车子', symbol: 'circle', symbolSize: 50, data: { brand: '宝马' } },
          { id: 'house-rent', name: '租房', symbol: 'circle', data: { address: '上海市浦东新区' } },
          { id: 'huang-tao', name: '黄涛', category: '租户', data: { age: 30 } },
          { id: 'yu-hai', name: '于海', category: '租户', data: { age: 28 } },
          { id: 'yu-hai-1', name: '于海1', category: '租户', data: { age: 25 } },
          { id: 'zhang-bo', name: '张柏', category: '租户', data: { age: 35 } },
          // { 
          //   id: 'fu-mengjie', 
          //   name: '付梦杰', 
          //   category: '租户', 
          //   symbol: "image://https://echarts.apache.org/examples/data/asset/img/pear.png", // 使用网络图片
          //   symbolSize: 80,
          //   data: { age: 27 }
          // }
        ],
        links: [
          { source: 'li-fugui', target: 'wang-guihua', value: '夫妻', lineStyle: { color: '#ff6b6b' } },
          { source: 'li-fugui', target: 'li-sisi', value: '父女' },
          { source: 'wang-guihua', target: 'li-sisi', value: '母女' },
          { source: 'li-fugui', target: 'house-self', value: '自住' },
          { source: 'li-fugui', target: 'car', value: '车主' },
          { source: 'li-fugui', target: 'house-rent', value: '房东' },
          { source: 'house-rent', target: 'huang-tao', value: '租赁' },
          { source: 'house-rent', target: 'yu-hai', value: '租赁' },
          { source: 'house-rent', target: 'yu-hai-1', value: '租赁' },
          { source: 'house-rent', target: 'zhang-bo', value: '租赁' },
          // { source: 'house-self', target: 'fu-mengjie', value: '租赁1', data: { rent: '5000/月' } }
        ]
      },
      // 自定义ECharts配置，会覆盖组件的默认配置
      customOption: {
        // 例如，修改力导向图的参数
        series: [{
            force: {
                repulsion: 1200, // 增大排斥力
                edgeLength: 80   // 增长边长度
            },
            // 修改节点样式
            itemStyle: {
                normal: {
                    color: '#003366'
                }
            }
        }]
      },
// 符合封装后数据格式的图数据
      educationGraphData: {
        categories: [
          { name: '同集团公司', color: '#c4895c' },
          { name: '同城市公司', color: '#6987b8' },
          { name: '控股', color: '#f9a5c7' },
          // { name: '在线教育机构', color: '#f2a498' },
          // { name: '互联网企业', color: '#91c7ae' },
          // { name: '学生', color: '#fcd684' },
        ],
        nodes: [
          {
            id: 'root',
            name: '内蒙古第一机械集团有限公司',
            category: '同集团公司',
            // symbolSize: [300, 60], // 根节点可以大一些
          },
          {
            id: 'teacher',
            name: '西北工业集团有限公司',
            category: '同城市公司',
          },
          {
            id: 'parent',
            name: '西北工业集团有限公司',
            category: '同城市公司',
          },
          {
            id: 'institution',
            name: '西安东方集团有限公司',
            category: '控股',
          },
          {
            id: 'company',
            name: '哈尔滨思和信息股份有限公司',
            category: '控股',
          },
          {
            id: 'student',
            name: '哈工大软件',
            category: '同城市公司',
          },
        ],
        links: [
          { source: 'root', target: 'teacher', category: '同集团公司' },
          { source: 'root', target: 'parent', category: '同城市公司' },
          { source: 'root', target: 'institution', category: '同城市公司' },
          { source: 'root', target: 'company', category: '控股' },
          { source: 'root', target: 'student', category: '控股' },
        ],
      },
      // 将原始数据转换成组件需要的格式
      policyData: rawList.map(item => ({
        date: item.policy_time,
        title: item.policy_title,
        country: item.policy_country,
        content: item.policy_content,
        // 你可以为特定条目添加自定义label
        // 例如，让所有2016年的条目都显示在左侧
        // label: item.policy_time.startsWith('2016') ? { position: 'left', align: 'left' } : undefined
      })),

      // 自定义ECharts配置，例如修改颜色
      customOption2: {
          color: ['#FF0000'], // 修改线和点的颜色
          series: [{
              lineStyle: {
                  type: 'dashed' // 将线改成虚线
              }
          }]
      }
    };
  },
  created() {
    this.getList();
  },
  beforeDestroy() {
    // 销毁图表实例
    this.disposeCharts();
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查询任务列表 */
    getList() {
      this.loading = true;
      // 模拟数据
      setTimeout(() => {
        this.taskList = [
          {
            id: 1,
            themes: [
              { id: 1, name: "专题A：农业政策分析" },
              { id: 2, name: "专题B：市场动态监测" },
            ],
            createByName: "张三",
            createTime: new Date().getTime(),
            progress: 100,
            opinionCount: 1250,
            score: 85,
          },
          {
            id: 2,
            themes: [
              { id: 3, name: "专题C：技术发展趋势" },
              { id: 4, name: "专题D：行业风险评估" },
            ],
            createByName: "李四",
            createTime: new Date().getTime() - 86400000,
            progress: 75,
            opinionCount: 890,
            score: 78,
          },
          {
            id: 3,
            themes: [{ id: 5, name: "专题E：政策影响评估" }],
            createByName: "王五",
            createTime: new Date().getTime() - 172800000,
            progress: 50,
            opinionCount: 560,
            score: 0,
          },
        ];
        this.total = this.taskList.length;
        this.loading = false;
      }, 500);
    },
    // 格式化专题显示
    formatThemes(themes) {
      if (!themes || !Array.isArray(themes)) return "暂无";
      return themes.map((t) => t.name).join("、");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.createForm = {
        themes: [
          {
            thematicId: undefined,
            isFilter: false,
            filterType: "keyword",
            filterTags: [],
            filterKeywords: "",
          },
        ],
      };
      this.createDialogVisible = true;
    },
    // 添加专题
    addTheme() {
      this.createForm.themes.push({
        thematicId: undefined,
        isFilter: false,
        filterType: "keyword",
        filterTags: [],
        filterKeywords: "",
      });
    },
    // 删除专题
    removeTheme(index) {
      this.createForm.themes.splice(index, 1);
    },
    // 取消创建
    cancelCreate() {
      this.createDialogVisible = false;
      this.createForm = {
        themes: [
          {
            thematicId: undefined,
            isFilter: false,
            filterType: "keyword",
            filterTags: [],
            filterKeywords: "",
          },
        ],
      };
    },
    // 提交创建
    submitCreate() {
      this.$refs["createForm"].validate((valid) => {
        if (valid) {
          // 验证每个专题是否选择了专题
          let isValid = true;
          this.createForm.themes.forEach((theme, index) => {
            if (!theme.thematicId) {
              isValid = false;
              this.$message.warning(`请选择专题 ${index + 1}`);
            }
          });

          if (!isValid) {
            return;
          }

          // 这里调用API创建任务
          this.$modal.msgSuccess("任务创建成功");
          this.createDialogVisible = false;
          this.getList();
        }
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.currentTask = { ...row };
      this.viewDialogVisible = true;
      this.$nextTick(() => {
        this.initCharts();
      });
    },
    // 初始化图表
    initCharts() {
      this.initPieChart();
      this.initWordCloudChart();
    },
    // 时间轴tab切换
    handleTimelineTab() {
      this.updateTimelineChart();
    },
    // 初始化饼图
    initPieChart() {
      if (!this.$refs.pieChart) return;
      this.pieChart = echarts.init(this.$refs.pieChart);
      const option = {
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
        },
        legend: {
          orient: "vertical",
          left: "left",
          textStyle: {
            color: "#fff",
          },
        },
        series: [
          {
            name: "专题分布",
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "30",
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 335, name: "专题A" },
              { value: 310, name: "专题B" },
              { value: 234, name: "专题C" },
              { value: 135, name: "专题D" },
              { value: 154, name: "专题E" },
            ],
          },
        ],
      };
      this.pieChart.setOption(option);
    },
    // 初始化词云图
    initWordCloudChart() {
      if (!this.$refs.wordCloudChart) return;
      this.wordCloudChart = echarts.init(this.$refs.wordCloudChart);
      const data = [
        { name: "农业", value: 1000 },
        { name: "政策", value: 800 },
        { name: "市场", value: 600 },
        { name: "技术", value: 500 },
        { name: "发展", value: 400 },
        { name: "风险", value: 350 },
        { name: "评估", value: 300 },
        { name: "监测", value: 250 },
        { name: "分析", value: 200 },
        { name: "趋势", value: 180 },
        { name: "影响", value: 150 },
        { name: "行业", value: 120 },
      ];

      const option = {
        tooltip: {
          show: true,
        },
        series: [
          {
            type: "wordCloud",
            gridSize: 2,
            sizeRange: [12, 50],
            rotationRange: [-90, 90],
            shape: "pentagon",
            width: "100%",
            height: "100%",
            drawOutOfBound: true,
            textStyle: {
              fontFamily: "sans-serif",
              fontWeight: "bold",
              color: function () {
                return (
                  "rgb(" +
                  [
                    Math.round(Math.random() * 255),
                    Math.round(Math.random() * 255),
                    Math.round(Math.random() * 255),
                  ].join(",") +
                  ")"
                );
              },
            },
            emphasis: {
              focus: "self",
              textStyle: {
                shadowBlur: 10,
                shadowColor: "#333",
              },
            },
            data: data,
          },
        ],
      };
      this.wordCloudChart.setOption(option);
    },
    // 销毁图表
    disposeCharts() {
      if (this.pieChart) {
        this.pieChart.dispose();
        this.pieChart = null;
      }
      if (this.wordCloudChart) {
        this.wordCloudChart.dispose();
        this.wordCloudChart = null;
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (row.id) {
        this.handleDeleteOne(row);
      } else {
        // 批量删除
        this.handleDeleteBatch();
      }
    },
    // 单条删除
    handleDeleteOne(row) {
      this.$modal
        .confirm(
          '是否确认删除任务"' + this.formatThemes(row.themes) + '"的数据项？'
        )
        .then(() => {
          this.taskList = this.taskList.filter((item) => item.id !== row.id);
          this.total = this.taskList.length;
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // 批量删除
    handleDeleteBatch() {
      const ids = this.ids;
      if (ids.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }
      this.$modal
        .confirm("是否确认删除" + ids.length + "条数据项？")
        .then(() => {
          this.taskList = this.taskList.filter(
            (item) => !ids.includes(item.id)
          );
          this.total = this.taskList.length;
          this.ids = [];
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    // parseTime,
  },
  watch: {
    viewDialogVisible(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.initCharts();
          window.addEventListener("resize", this.handleResize);
        });
      } else {
        this.disposeCharts();
        window.removeEventListener("resize", this.handleResize);
      }
    },
  },
  mounted() {
    this.handleResize = () => {
      if (this.pieChart) this.pieChart.resize();
      if (this.wordCloudChart) this.wordCloudChart.resize();
    };
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.ac_header {
  width: 98%;
  margin: auto;
  height: 110px;
  background-image: url("../../../assets/images/lsdrsj_bg.png");
  background-size: contain;
  .ach_left {
    float: left;
    height: 100%;
    .title {
      font-family: SourceHanSansSC-Bold;
      font-size: 16px;
      color: #ffffff;
      letter-spacing: 0;
      padding-left: 30px;
    }
    .nums {
      font-family: LetsgoDigital-Regular;
      font-size: 36px;
      color: #ffffff;
      letter-spacing: 0;
      text-shadow: 0 2px 5px rgba(2, 0, 70, 0.5);
      font-weight: 700;
      margin-top: 10px;
      padding-left: 30px;
      background-image: linear-gradient(to bottom, #ffffff, #ccd8f2, #3869cc);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }
  .ach_right {
    float: right;
  }
}

.position_btn {
  margin-right: 10px;
}
.position_add_btn {
  margin-right: 30px;
  margin-top: 32px;
}

.theme-item {
  border: 1px solid rgba(17, 80, 154, 1);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  background: rgba(27, 126, 242, 0.1);
}

.theme-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.theme-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}

.remove-theme-btn {
  color: #f56c6c;
}

.view-dialog {
  .task-info-section {
    margin-bottom: 30px;
  }

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(17, 80, 154, 1);
  }

  .analysis-results {
    .chart-section {
      margin-bottom: 30px;
      // padding: 20px;
      // background: rgba(27, 126, 242, 0.05);
      border-radius: 8px;
      // border: 1px solid rgba(17, 80, 154, 0.3);
      width: 50%;
      float: left;
      height: 500px;
      background-image: url('../../../assets/images/left-kuang03.png');
      background-repeat: no-repeat;
      background-size: 98% 100%;
    }

    .chart-title {
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      margin-bottom: 15px;
      height: 40px;
      margin: 0;
      line-height: 42px;
      margin-left: 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.50);
    // font-weight: 700;
    }

    .chart-container {
      width: 98%;
      height: calc(100% - 40px);
      min-height: calc(100% - 40px);
    }
  }
}

.dialog_form_style {
  .el-form-item__label {
    color: #fff;
  }
}
</style>
