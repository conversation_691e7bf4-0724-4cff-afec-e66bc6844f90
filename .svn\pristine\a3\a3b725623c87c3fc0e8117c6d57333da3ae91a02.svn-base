<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入姓名"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处室">
        <el-select
          v-model="queryParams.selectedDepartment"
          placeholder="请选择"
        >
          <el-option
            v-for="item in departments"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <div class="table-container">
      <el-table
        :data="filteredAuditList"
        stripe
        :row-class-name="tableRowClassName"
      >
        <el-table-column
          class="table-column"
          label="排名"
          align="center"
          prop="xh"
        />
        <el-table-column
          class="table-column"
          label="部门"
          align="center"
          prop="bm"
        />
        <el-table-column
          class="table-column"
          label="姓名"
          align="center"
          prop="name"
        />
        <el-table-column
          class="table-column"
          label="职务职称"
          align="center"
          prop="jobTitle"
        />
        <el-table-column
          class="table-column"
          label="测评日期"
          align="center"
          prop="date"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.date }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="考核等级"
          align="center"
          prop="assessmentLevel"
        />
        <el-table-column label="排名占比" align="center">
          <template slot-scope="scope">
            <div class="progress-wrapper">
              <el-progress
                :percentage="calculatePercentage(scope.row.xh)"
                :format="format"
                :stroke-width="15"
                :color="getProgressColor(scope.row.xh)"
              >
              </el-progress>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="排名" align="center" prop="total" /> -->
      </el-table>
    </div>

    <!-- 分页 -->
    <!-- <div class="pagination-container">
      <el-pagination background layout="prev, pager, next" :total="2">
      </el-pagination>
    </div> -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: "Audit",
  data() {
    return {
      selectedDepartment: "",
      searchName: "",
      departments: [
        { value: "调查处", label: "调查处" },
        { value: "利用处", label: "利用处" },
      ],
      queryParams: {
        userName: "",
        selectedDepartment: "",
      },
      auditList: [
        {
          xh: 1,
          bm: "调查处",
          name: "张老师",
          jobTitle: "处长",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 1,
        },
        {
          xh: 2,
          bm: "利用处",
          name: "李老师",
          jobTitle: "处长",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 2,
        },
        {
          xh: 3,
          bm: "信息处",
          name: "王老师",
          jobTitle: "副处长",
          date: "2024年",
          assessmentLevel: "良好",
          total: 3,
        },
        {
          xh: 4,
          bm: "研究处",
          name: "赵老师",
          jobTitle: "助理",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 4,
        },
        {
          xh: 5,
          bm: "教育处",
          name: "孙老师",
          jobTitle: "处长",
          date: "2024年",
          assessmentLevel: "良好",
          total: 5,
        },
        {
          xh: 6,
          bm: "技术处",
          name: "周老师",
          jobTitle: "工程师",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 6,
        },
        {
          xh: 7,
          bm: "培训处",
          name: "吴老师",
          jobTitle: "讲师",
          date: "2024年",
          assessmentLevel: "良好",
          total: 7,
        },
        {
          xh: 8,
          bm: "合作处",
          name: "郑老师",
          jobTitle: "协调员",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 8,
        },
        {
          xh: 9,
          bm: "发展处",
          name: "钱老师",
          jobTitle: "顾问",
          date: "2024年",
          assessmentLevel: "良好",
          total: 9,
        },
        {
          xh: 10,
          bm: "创新处",
          name: "徐老师",
          jobTitle: "研究员",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 10,
        },
        {
          xh: 11,
          bm: "宣传处",
          name: "杨老师",
          jobTitle: "撰稿人",
          date: "2024年",
          assessmentLevel: "良好",
          total: 11,
        },
        {
          xh: 12,
          bm: "安全处",
          name: "朱老师",
          jobTitle: "主管",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 12,
        },
        {
          xh: 13,
          bm: "项目处",
          name: "宋老师",
          jobTitle: "项目经理",
          date: "2024年",
          assessmentLevel: "良好",
          total: 13,
        },
        {
          xh: 14,
          bm: "财务处",
          name: "唐老师",
          jobTitle: "会计",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 14,
        },
        {
          xh: 15,
          bm: "管理处",
          name: "霍老师",
          jobTitle: "行政助理",
          date: "2024年",
          assessmentLevel: "良好",
          total: 15,
        },
        {
          xh: 16,
          bm: "服务处",
          name: "程老师",
          jobTitle: "服务专员",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 16,
        },
      ],
      // 总条数
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bm: null,
        name: null,
        jobTitle: null,
        date: null,
        assessmentLevel: null,
        total: null,
        morals: null,
        ability: null,
        diligent: null,
        achievement: null,
        integrity: null,
        status: null,
      },
    };
  },
  computed: {
    filteredAuditList() {
      // 简单的过滤逻辑，可以根据选中的部门和姓名进行过滤
      return this.auditList.filter((item) => {
        return (
          (!this.selectedDepartment || item.bm === this.selectedDepartment) &&
          (!this.searchName || item.name.includes(this.searchName))
        );
      });
    },
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.total = this.auditList.length; // 更新总条数
    },
    handleQuery() {
      // 执行查询操作，可以在这里添加更复杂的逻辑
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex === 1) {
        return "warning-row";
      } else if (rowIndex === 3) {
        return "success-row";
      }
      return "";
    },
    calculatePercentage(rank) {
      const totalPeople = this.auditList.length; // 总人数，您可以根据实际情况修改
      const percentage = Math.round((1 - rank / totalPeople) * 100);
      return percentage;
    },
    format(percentage) {
      return `${percentage}%`;
    },
    getProgressColor(rank) {
      // 根据排名返回不同的颜色
      if (rank === 1) return "#67C23A";
      if (rank === 2) return "#409EFF";
      if (rank === 3) return "#F56C6C";
      return "#909399";
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #ffffff;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}

.department-select {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  line-height: 20px;
  font-weight: 400;
}

::v-deep .el-table th.el-table__cell > .cell {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #203f78;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 700;
}

::v-deep .el-table tr {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 400;
}

.search-btn {
  background: #1b5dd8;
  border-radius: 16px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // right: 0;
  // height: 50px;
  // z-index: 1000;
}

.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}

.progress-wrapper {
  padding: 0 20px;

  ::v-deep .el-progress-bar__outer {
    background-color: #f0f2f5;
  }

  ::v-deep .el-progress-bar__inner {
    transition: all 0.3s ease;
  }
}
</style>