<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes
        :horizontal="this.$store.getters.device === 'mobile'"
        class="default-theme"
      >
        <!--部门数据-->
        <pane size="16" class="treeDiv">
          <!-- <p>数据资源分类体系</p> -->
          <!-- <el-button class="addClass" type="success" size="mini">成功按钮</el-button> -->
          <el-col>
            <el-button class="addClass" size="small" type="success" @click="handleAdd">新增分类</el-button>
            <div class="head-container">
              <el-input
                v-model="deptName"
                placeholder="搜索分类名称"
                clearable
                size="small"
                prefix-icon="el-icon-search"
                style="margin-bottom: 20px"
              />
            </div>
            <div class="head-container">
              <el-tree
                :data="deptOptions"
                :props="defaultProps"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="tree"
                node-key="id"
                default-expand-all
                highlight-current
                @node-click="handleNodeClick"
              >
                <span class="custom-tree-node" slot-scope="{ data }">
                  <span>
                    <!-- {{data.level}} -->
                    <el-tag effect="dark" v-if="data.level === 1" type="" size="mini"
                      >主题</el-tag
                    >
                    <el-tag effect="dark" v-if="data.level === 2" type="info" size="mini"
                      >专题</el-tag
                    >
                    <el-tag
                      effect="dark"
                      v-if="data.level === 3"
                      type="warning"
                      size="mini"
                      >类别</el-tag
                    >
                    {{ data.label }}
                  </span>
                </span>
              </el-tree>
            </div>
          </el-col>
        </pane>
        <!--用户数据-->
        <pane size="84">
          <el-col>
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              v-show="showSearch"
              label-width="68px"
            >
              <el-form-item label="分类名称" prop="categoryName">
                <el-input
                  v-model="queryParams.categoryName"
                  placeholder="请输入分类名称"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="所属体系" prop="systemId">
                <el-select
                  v-model="queryParams.systemId"
                  placeholder="所属体系"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="system in systemOptions"
                    :key="system.value"
                    :label="system.label"
                    :value="system.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="queryParams.status"
                  placeholder="状态"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="success"
                  plain
                  icon="el-icon-edit"
                  size="mini"
                  :disabled="single"
                  @click="handleUpdate"
                  v-hasPermi="['data:datain:batchExport']"
                  >批量导出</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  plain
                  icon="el-icon-delete"
                  size="mini"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['data:datain:batchDelete']"
                  >批量删除</el-button
                >
              </el-col>
              <right-toolbar
                :showSearch.sync="showSearch"
                @queryTable="getList"
                :columns="columns"
              ></right-toolbar>
            </el-row>

            <el-table
              v-loading="loading"
              :data="categoryList"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" type="index" width="60" align="center" />
              <el-table-column label="分类名称" prop="categoryName" sortable />
              <el-table-column label="编码" prop="categoryCode" />
              <el-table-column label="所属体系" prop="systemName" />
              <el-table-column label="父节点" prop="parentName" />
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <dict-tag
                    :options="dict.type.sys_normal_disable"
                    :value="scope.row.status"
                  />
                </template>
              </el-table-column>
              <el-table-column label="创建人" prop="createBy" :show-overflow-tooltip="true" />
              <el-table-column
                label="创建时间"
                align="center"
                sortable
                prop="createTime"
                width="180"
              >
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['data:category:edit']"
                    >编辑</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['data:category:remove']"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改分类配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <p class="dialogTitle">数据资源分类体系管理</p>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类名称" prop="categoryName">
              <el-input
                v-model="form.categoryName"
                placeholder="请输入分类名称"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="编码" prop="categoryCode">
              <el-input
                v-model="form.categoryCode"
                placeholder="系统自动生成"
                :disabled="true"
                maxlength="50"
              >
                <el-button slot="append" @click="generateCode">生成编码</el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属体系" prop="systemId">
              <el-select v-model="form.systemId" placeholder="请选择所属体系" style="width: 100%">
                <el-option
                  v-for="system in systemOptions"
                  :key="system.value"
                  :label="system.label"
                  :value="system.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="父节点" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="categoryTreeOptions"
                :show-count="true"
                placeholder="请选择父节点"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="分类描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                placeholder="请输入分类描述"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="同级排序" prop="orderNum">
              <el-input-number
                v-model="form.orderNum"
                :min="0"
                :max="999"
                placeholder="排序号"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import {
  deptTreeSelect,
} from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";

export default {
  name: "DataManage",
  dicts: ["sys_normal_disable"],
  components: { Treeselect, Splitpanes, Pane },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分类表格数据
      categoryList: [],
      // 弹出层标题
      title: "",
      // 所有部门树选项
      deptOptions: undefined,
      // 过滤掉已禁用部门树选项
      enabledDeptOptions: undefined,
      // 分类树选项
      categoryTreeOptions: [],
      // 体系选项
      systemOptions: [
        { value: '1', label: '边境基建体系' },
        { value: '2', label: '边防斗争体系' },
        { value: '3', label: '高层动态体系' },
        { value: '4', label: '武器装备体系' }
      ],
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryName: undefined,
        systemId: undefined,
        status: undefined,
        deptId: undefined,
      },
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `分类名称`, visible: true },
        { key: 2, label: `编码`, visible: true },
        { key: 3, label: `所属体系`, visible: true },
        { key: 4, label: `父节点`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建人`, visible: true },
        { key: 7, label: `创建时间`, visible: true },
      ],
      // 表单校验
      rules: {
        categoryName: [
          { required: true, message: "分类名称不能为空", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "分类名称长度必须介于 2 和 50 之间",
            trigger: "blur",
          },
        ],
        categoryCode: [
          { required: true, message: "编码不能为空", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "编码长度必须介于 2 和 50 之间",
            trigger: "blur",
          },
        ],
        systemId: [
          { required: true, message: "所属体系不能为空", trigger: "change" },
        ],
        parentId: [
          { required: true, message: "父节点不能为空", trigger: "change" },
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getList();
    this.getDeptTree();
  },
  methods: {
    /** 查询分类列表 */
    getList() {
      this.loading = true;
      // 模拟数据，实际应该调用分类管理API
      setTimeout(() => {
        this.categoryList = [
          {
            id: 1,
            categoryName: "事件信息",
            categoryCode: "SJXX001",
            systemName: "边境基建体系",
            parentName: "东段建设",
            status: "0",
            createBy: "admin",
            createTime: "2024-01-01 10:00:00"
          },
          {
            id: 2,
            categoryName: "舆情日报",
            categoryCode: "YQRB002",
            systemName: "边境基建体系",
            parentName: "东段建设",
            status: "0",
            createBy: "admin",
            createTime: "2024-01-02 10:00:00"
          }
        ];
        this.total = this.categoryList.length;
        this.loading = false;
      }, 500);
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then((response) => {
        console.log(response, "661-----------------");
        response = {
          msg: "操作成功",
          code: 200,
          data: [
            {
              id: 1,
              label: "边境基建",
              disabled: false,
              level: 1,
              children: [
                {
                  id: 2,
                  label: "东段建设",
                  disabled: false,
                  level: 2,
                  children: [
                    {
                      id: 3,
                      label: "事件信息",
                      level: 3,
                      disabled: false,
                    },
                    {
                      id: 4,
                      label: "舆情日报",
                      level: 3,
                      disabled: false,
                    },
                    {
                      id: 5,
                      label: "开源资讯",
                      level: 3,
                      disabled: false,
                    },
                    {
                      id: 6,
                      label: "X类别",
                      level: 3,
                      disabled: false,
                    },
                  ],
                },
                {
                  id: 7,
                  label: "西段建设",
                  level: 2,
                  disabled: false,
                },
                {
                  id: 8,
                  label: "中段建设",
                  level: 2,
                  disabled: false,
                },
              ],
            },
            {
              id: 9,
              label: "边防斗争",
              level: 1,
              disabled: false,
            },
            {
              id: 10,
              label: "高层动态",
              level: 1,
              disabled: false,
            },
            {
              id: 11,
              label: "武器装备",
              level: 1,
              disabled: false,
            },
            {
              id: 12,
              label: "XX主题",
              level: 1,
              disabled: false,
            },
          ],
        };
        this.deptOptions = response.data;
        this.enabledDeptOptions = this.filterDisabledDept(
          JSON.parse(JSON.stringify(response.data))
        );
      });
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        categoryId: undefined,
        categoryName: undefined,
        categoryCode: undefined,
        systemId: undefined,
        parentId: undefined,
        description: undefined,
        orderNum: 0,
        status: "0",
      };
      this.resetForm("form");
    },
    // 生成编码
    generateCode() {
      const timestamp = Date.now();
      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      this.form.categoryCode = `CAT${timestamp}${random}`;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.deptId = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getCategoryTree();
      this.open = true;
      this.title = "添加分类";
      this.generateCode();
    },
    /** 获取分类树 */
    getCategoryTree() {
      // 模拟分类树数据，实际应该调用API
      this.categoryTreeOptions = [
        {
          id: 1,
          label: "边境基建",
          children: [
            {
              id: 2,
              label: "东段建设",
              children: []
            },
            {
              id: 3,
              label: "西段建设",
              children: []
            }
          ]
        },
        {
          id: 4,
          label: "边防斗争",
          children: []
        }
      ];
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getCategoryTree();
      // 模拟获取分类详情，实际应该调用API
      this.form = {
        categoryId: row.id,
        categoryName: row.categoryName,
        categoryCode: row.categoryCode,
        systemId: "1", // 根据实际数据设置
        parentId: 2, // 根据实际数据设置
        description: "分类描述信息",
        orderNum: 1,
        status: row.status
      };
      this.open = true;
      this.title = "修改分类";
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.categoryId != undefined) {
            // 模拟修改分类API调用
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          } else {
            // 模拟新增分类API调用
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除分类名称为"' + row.categoryName + '"的数据项？')
        .then(() => {
          // 模拟删除分类API调用
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/user/export",
        {
          ...this.queryParams,
        },
        `user_${new Date().getTime()}.xlsx`
      );
    },

  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.treeDiv {
  position: relative;
}
.addClass {
  width: 100%;
  margin-bottom: 10px;
}
</style>
