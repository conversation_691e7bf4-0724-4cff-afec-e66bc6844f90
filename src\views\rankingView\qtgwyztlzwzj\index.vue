<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" label-width="68px">
      <el-form-item label="处室">
        <el-select v-model="selectedDepartment" placeholder="请选择">
          <el-option
            v-for="item in departments"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="姓名">
        <el-input
          v-model="searchName"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-button type="primary" size="small" @click="handleQuery"
        >查询</el-button
      >
    </el-form>

    <!-- 表格 -->
    <el-table :data="filteredAuditList" stripe>
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" prop="xh" />
      <el-table-column label="部门" align="center" prop="bm" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="职务职称" align="center" prop="jobTitle" />
      <el-table-column label="测评日期" align="center" prop="date" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="考核等级" align="center" prop="assessmentLevel" />
      <el-table-column label="排名占比" align="center">
        <template slot-scope="scope">
          <div class="progress-wrapper">
            <el-progress
              :percentage="calculatePercentage(scope.row.xh)"
              :format="format"
              :stroke-width="15"
              :color="getProgressColor(scope.row.xh)"
            >
            </el-progress>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="排名" align="center" prop="total" /> -->
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
export default {
  name: "Audit",
  data() {
    return {
      selectedDepartment: "",
      searchName: "",
      departments: [
        { value: "调查处", label: "调查处" },
        { value: "利用处", label: "利用处" },
      ],
      auditList: [
        {
          xh: 1,
          bm: "调查处",
          name: "张老师",
          jobTitle: "二级科员",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 1,
        },
        {
          xh: 2,
          bm: "利用处",
          name: "李老师",
          jobTitle: "二级科员",
          date: "2024年",
          assessmentLevel: "优秀",
          total: 2,
        },
      ],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bm: null,
        name: null,
        jobTitle: null,
        date: null,
        assessmentLevel: null,
        total: null,
        morals: null,
        ability: null,
        diligent: null,
        achievement: null,
        integrity: null,
        status: null,
      },
    };
  },
  computed: {
    filteredAuditList() {
      // 简单的过滤逻辑，可以根据选中的部门和姓名进行过滤
      return this.auditList.filter((item) => {
        return (
          (!this.selectedDepartment || item.bm === this.selectedDepartment) &&
          (!this.searchName || item.name.includes(this.searchName))
        );
      });
    },
  },
  created() {
    this.getList();
  },
  methods: {
    handleQuery() {
      // 执行查询操作，可以在这里添加更复杂的逻辑
    },
    getList() {
      this.total = this.auditList.length; // 更新总条数
    },
    calculatePercentage(rank) {
      const totalPeople = this.auditList.length; // 总人数，您可以根据实际情况修改
      const percentage = Math.round((1 - rank / totalPeople) * 100);
      return percentage;
    },
    format(percentage) {
      return `${percentage}%`;
    },
    getProgressColor(rank) {
      // 根据排名返回不同的颜色
      if (rank === 1) return "#67C23A";
      if (rank === 2) return "#409EFF";
      if (rank === 3) return "#F56C6C";
      return "#909399";
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  background: #ffffff;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}

.department-select {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  line-height: 20px;
  font-weight: 400;
}

::v-deep .el-table th.el-table__cell > .cell {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #203f78;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 700;
}

::v-deep .el-table tr {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 400;
}

.search-btn {
  background: #1b5dd8;
  border-radius: 16px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.pagination-container {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // right: 0;
  // height: 50px;
  // z-index: 1000;
}

/* 偶数行背景色 */
.el-table .even-row {
  background-color: #da3333;
}

/* 奇数行背景色 */
.el-table .odd-row {
  background-color: #ffffff;
}
</style>