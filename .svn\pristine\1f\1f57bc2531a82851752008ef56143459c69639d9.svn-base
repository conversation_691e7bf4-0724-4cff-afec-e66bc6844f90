<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes
        :horizontal="this.$store.getters.device === 'mobile'"
        class="default-theme"
      >
        <!--部门数据-->
        <pane size="16" class="treeDiv">
          <!-- <p>数据资源分类体系</p> -->
          <!-- <el-button class="addClass" type="success" size="mini">成功按钮</el-button> -->
          <el-col>
            <el-button
              class="addClass"
              size="small"
              type="success"
              @click="handleAdd"
              >新增分类</el-button
            >
            <div class="head-container">
              <el-input
                v-model="deptName"
                placeholder="搜索分类名称"
                clearable
                size="small"
                prefix-icon="el-icon-search"
                style="margin-bottom: 20px"
              />
            </div>
            <div class="head-container">
              <el-tree
                :data="deptOptions"
                :props="defaultProps"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="tree"
                node-key="id"
                default-expand-all
                highlight-current
              >
                <span class="custom-tree-node" slot-scope="{ data }">
                  <span>
                    <dict-tag
                      class="dictTagClass"
                      size="mini"
                      :options="dict.type.data_resources_system"
                      :value="data.systemType"
                    />
                    {{ data.label }}
                  </span>
                </span>
              </el-tree>
            </div>
          </el-col>
        </pane>
        <!--用户数据-->
        <pane size="84">
          <el-col>
            <el-form
              :model="queryParams"
              ref="queryForm"
              size="small"
              :inline="true"
              v-show="showSearch"
              label-width="68px"
            >
              <el-form-item label="分类名称" prop="categoryName">
                <el-input
                  v-model="queryParams.categoryName"
                  placeholder="请输入分类名称"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
              <el-form-item label="所属体系" prop="systemType">
                <el-select
                  v-model="queryParams.systemType"
                  placeholder="所属体系"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dict.type.data_resources_system"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="queryParams.status"
                  placeholder="状态"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dict.type.data_resources_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQuery"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="success"
                  plain
                  icon="el-icon-edit"
                  size="mini"
                  :disabled="multiple"
                  @click="handleExport"
                  v-hasPermi="['data:datain:batchExport']"
                  >批量导出</el-button
                >
              </el-col>
              <el-col :span="1.5">
                <el-button
                  type="danger"
                  plain
                  icon="el-icon-delete"
                  size="mini"
                  :disabled="multiple"
                  @click="handleBatchDelete"
                  v-hasPermi="['data:datain:batchDelete']"
                  >批量删除</el-button
                >
              </el-col>
              <right-toolbar
                :showSearch.sync="showSearch"
                @queryTable="getList"
                :columns="columns"
              ></right-toolbar>
            </el-row>

            <el-table
              v-loading="loading"
              :data="categoryList"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column
                label="序号"
                type="index"
                width="60"
                align="center"
              />
              <el-table-column label="分类名称" prop="categoryName" sortable />
              <el-table-column label="编码" prop="code" sortable />
              <el-table-column prop="systemType" label="所属体系">
                <template slot-scope="scope">
                  <!-- 遍历后端返回的数组，假设数组在 data 中定义为 tagList -->
                  <!-- <el-tag
                    :key="scope.row.systemType"
                    :type="getTagType(scope.row.systemType)"
                    effect="dark"
                  >
                    {{ getTagLabel(scope.row.systemType) }}
                  </el-tag> -->
                  <dict-tag
                    :options="dict.type.data_resources_system"
                    :value="scope.row.systemType"
                  />
                </template>
              </el-table-column>
              <el-table-column label="父节点" prop="parentId" />
              <el-table-column prop="status" label="状态">
                <template slot-scope="scope">
                  <el-tag
                    :key="scope.row.systemType"
                    :type="getTagType(scope.row.status)"
                    effect="dark"
                  >
                    {{ getTagLabel(scope.row.status) }}
                  </el-tag>
                  <!-- <dict-tag
                    :options="dict.type.data_resources_status"
                    :value="scope.row.status"
                  /> -->
                </template>
              </el-table-column>
              <el-table-column
                label="创建人"
                prop="createByName"
                :show-overflow-tooltip="true"
              />
              <el-table-column
                label="创建时间"
                align="center"
                sortable
                prop="createTime"
                width="180"
              >
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.createTime) }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                    @click="handleUpdate(scope.row)"
                    v-hasPermi="['data:category:edit']"
                    >编辑</el-button
                  >
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="handleDelete(scope.row)"
                    v-hasPermi="['data:category:remove']"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize"
              @pagination="getList"
            />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改分类配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <p class="dialogTitle">数据资源分类体系管理</p>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类名称" prop="categoryName">
              <el-input
                v-model="form.categoryName"
                placeholder="请输入分类名称"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="编码" prop="code">
              <el-input
                v-model="form.code"
                placeholder="系统自动生成"
                :disabled="true"
                maxlength="50"
              >
                <el-button slot="append" @click="generateCode"
                  >生成编码</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属体系" prop="systemType">
              <el-select
                v-model="form.systemType"
                placeholder="请选择所属体系"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in dict.type.data_resources_system"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="父节点" prop="parentId">
              <treeselect
                v-model="form.parentId"
                :options="categoryTreeOptions"
                :show-count="true"
                placeholder="请选择父节点（不选择则为根节点）"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="分类描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                placeholder="请输入分类描述"
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="同级排序" prop="sortOrder">
              <el-input-number
                v-model="form.sortOrder"
                :min="0"
                :max="999"
                placeholder="排序号"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.data_resources_status"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { deptTreeSelect } from "@/api/system/user";
import {
  getOpenSourceIntegrationList,
  getOpenSourceIntegrationTree,
  addOpenSourceIntegration,
  updateOpenSourceIntegration,
  deleteOpenSourceIntegration,
  exportOpenSourceIntegration,
  getClassificationCode,
} from "@/api/data/openSourceIntegration";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";

export default {
  name: "DataManage",
  dicts: ["data_resources_system", "data_resources_status"],
  components: { Treeselect, Splitpanes, Pane },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分类表格数据
      categoryList: [],
      // 弹出层标题
      title: "",
      // 所有部门树选项
      deptOptions: undefined,
      // 过滤掉已禁用部门树选项
      enabledDeptOptions: undefined,
      // 分类树选项
      categoryTreeOptions: [],
      // 体系选项
      systemOptions: [
        { value: "1", label: "边境基建体系" },
        { value: "2", label: "边防斗争体系" },
        { value: "3", label: "高层动态体系" },
        { value: "4", label: "武器装备体系" },
      ],
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryName: undefined,
        systemType: undefined,
        status: undefined,
        deptId: undefined,
        kssj: undefined,
        jssj: undefined,
      },
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `分类名称`, visible: true },
        { key: 2, label: `编码`, visible: true },
        { key: 3, label: `所属体系`, visible: true },
        { key: 4, label: `父节点`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建人`, visible: true },
        { key: 7, label: `创建时间`, visible: true },
      ],
      // 表单校验
      rules: {
        categoryName: [
          { required: true, message: "分类名称不能为空", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "分类名称长度必须介于 2 和 50 之间",
            trigger: "blur",
          },
        ],
        code: [
          { required: true, message: "编码不能为空", trigger: "blur" },
          // {
          //   min: 2,
          //   max: 50,
          //   message: "编码长度必须介于 2 和 50 之间",
          //   trigger: "blur",
          // },
        ],
        systemType: [
          { required: true, message: "所属体系不能为空", trigger: "change" },
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getList();
    this.getDeptTree();
  },
  methods: {
    /**
     * 根据systemType匹配字典，获取Tag的type（颜色）
     * @param {String} systemType - 表格行的systemType字段值
     * @returns {String} 匹配的listClass（如warning/primary）
     */
    getTagType(systemType) {
      // console.log(this.dict.type.data_resources_system, 'this.dict.type.data_resources_system');
      // 查找字典中value与systemType一致的项（若实际匹配raw.dictValue，改为item.raw.dictValue === systemType）
      const matchItem = this.dict.type.data_resources_status.find(
        (item) => item.value === systemType
      );
      // 有匹配项返回listClass，无匹配项默认空（避免报错）
      return matchItem ? matchItem.raw.listClass : "";
    },

    /**
     * 根据systemType匹配字典，获取Tag显示的文字（如“主题”）
     * @param {String} systemType - 表格行的systemType字段值
     * @returns {String} 匹配的label
     */
    getTagLabel(systemType) {
      const matchItem = this.dict.type.data_resources_status.find(
        (item) => item.value === systemType
      );
      // 有匹配项返回label，无匹配项显示原始值
      return matchItem ? matchItem.label : systemType;
    },
    /** 查询分类列表 */
    getList() {
      this.loading = true;
      // 处理日期范围参数
      const params = { ...this.queryParams };
      if (this.dateRange && this.dateRange.length === 2) {
        params.kssj = this.dateRange[0];
        params.jssj = this.dateRange[1];
      }

      getOpenSourceIntegrationList(params)
        .then((response) => {
          this.categoryList = response.rows || [];
          this.total = response.total || 0;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      getOpenSourceIntegrationTree()
        .then((response) => {
          // 转换数据格式，适配el-tree组件
          this.deptOptions = this.transformDeptTreeData(response.data || []);
          this.enabledDeptOptions = this.filterDisabledDept(
            JSON.parse(JSON.stringify(this.deptOptions))
          );
        })
        .catch(() => {
          this.deptOptions = [];
          this.enabledDeptOptions = [];
        });
    },

    /** 转换部门树数据格式 */
    transformDeptTreeData(data) {
      return data.map((item) => ({
        id: item.id,
        label: item.categoryName,
        systemType: item.systemType,
        disabled: false,
        level: this.calculateLevel(item),
        children: item.children
          ? this.transformDeptTreeData(item.children)
          : [],
      }));
    },

    /** 计算节点层级 */
    calculateLevel(item, level = 1) {
      if (item.parentId === null) {
        return 1; // 主题
      } else {
        // 这里可以根据实际业务逻辑计算层级
        // 暂时使用简单的层级计算
        return level;
      }
    },
    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    // handleNodeClick(data) {
    //   this.queryParams.deptId = data.id;
    //   this.handleQuery();
    // },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        categoryName: undefined,
        code: undefined,
        systemType: undefined,
        parentId: null, // 默认父节点为根节点
        description: undefined,
        sortOrder: 0,
        status: "0",
      };
      this.resetForm("form");
    },
    // 生成编码
    generateCode() {
      getClassificationCode()
        .then((response) => {
          if (response.code == 200) {
            this.form.code = response.data || response.msg || "";
          } else {
            this.$modal.msgError(response.msg);
          }
        })
        .catch(() => {
          // 如果API调用失败，使用备用方案
          const timestamp = Date.now();
          const random = Math.floor(Math.random() * 1000)
            .toString()
            .padStart(3, "0");
          this.form.code = `CAT${timestamp}${random}`;
        });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.deptId = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },

    /** 批量删除按钮操作 */
    handleBatchDelete() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要删除的数据");
        return;
      }
      this.$modal
        .confirm("是否确认删除选中的" + this.ids.length + "条数据项？")
        .then(() => {
          const ids = this.ids.join(",");
          deleteOpenSourceIntegration(ids).then((response) => {
            this.getList();
            this.getDeptTree();
            this.$modal.msgSuccess("删除成功");
          });
        })
        .catch(() => {});
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getCategoryTree();
      this.open = true;
      this.title = "添加分类";
      this.generateCode();
    },
    /** 获取分类树 */
    getCategoryTree() {
      getOpenSourceIntegrationTree()
        .then((response) => {
          // 转换数据格式，适配treeselect组件
          this.categoryTreeOptions = this.transformTreeData(
            response.data || []
          );
        })
        .catch(() => {
          this.categoryTreeOptions = [];
        });
    },

    /** 转换树数据格式 */
    transformTreeData(data) {
      return data.map((item) => ({
        id: item.id,
        label: item.categoryName,
        children: item.children ? this.transformTreeData(item.children) : [],
      }));
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getCategoryTree();
      // 模拟获取分类详情，实际应该调用API
      this.form = {
        id: row.id,
        categoryName: row.categoryName,
        code: row.code,
        systemType: row.systemType,
        parentId: row.parentId,
        description: row.description,
        sortOrder: row.sortOrder,
        status: row.status,
      };
      this.open = true;
      this.title = "修改分类";
    },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            // 修改分类
            updateOpenSourceIntegration(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.getDeptTree();
            });
          } else {
            // 新增分类
            addOpenSourceIntegration(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.getDeptTree();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal
        .confirm('是否确认删除分类名称为"' + row.categoryName + '"的数据项？')
        .then(() => {
          deleteOpenSourceIntegration(row.id).then((response) => {
            this.getList();
            this.getDeptTree();
            this.$modal.msgSuccess("删除成功");
          });
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "dataaccess/classificationSystem/export",
        {
          ids: this.ids.join(','),
        },
        `数据资源分类_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.treeDiv {
  position: relative;
}
.addClass {
  width: 100%;
  margin-bottom: 10px;
}
.dictTagClass {
  float: left;
  margin-right: 5px;
  ::v-deep .el-tag {
    height: 20px;
    line-height: 20px;
    font-size: 12px;
  }
}
</style>
