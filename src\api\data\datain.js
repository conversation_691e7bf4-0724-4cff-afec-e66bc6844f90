import request from '@/utils/request'
// ***********************人工填报接口
// 人工填报记录分页查询
export function datain_record_list(query) {
  return request({
    url: '/dataaccess/manual/list',
    method: 'get',
    params: query
  })
}

// 人工填报查询单条（详情） 查询详细
export function getrecord(manualId) {
  return request({
    url: '/dataaccess/manual/' + manualId,
    method: 'get'
  })
}

// 人工填报 新增填报记录
export function addrecord(data) {
  return request({
    url: '/dataaccess/manual/add',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

//人工填报编辑接口  继续编辑填报记录
export function updaterecord(data) {
  return request({
    url: '/dataaccess/manual/',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 人工填报删除接口
export function delrecord(manualId) {
  return request({
    url: '/dataaccess/manual/' + manualId,
    method: 'delete'
  })
}
// 导出接口
// export function exports(data) {
//   return request({
//     url: '/dataaccess/manual/export',
//     method: 'post',
//     // ids: data

//   })
// }
// ************************数据融合接口
// 分页查询
export function datain_dataFusion_list(query) {
  return request({
    url: '/dataaccess/dataFusion/list',
    method: 'get',
    params: query
  })
}
// 根据id删除记录
export function delrecord_dataFusion(manualId) {
  return request({
    url: '/dataaccess/dataFusion/' + manualId,
    method: 'delete'
  })
}
// 导出按钮的接口路径是   /dataaccess/dataFusion/export    融合按钮的接口路径是   /dataaccess/dataFusion/exportKafka
export function exportdataFusion(data) {
  return request({
    url: '/dataaccess/dataFusion/export',
    method: 'post',
    data: data,
  })
}
export function exportKafka(query) {
  return request({
    url: '/dataaccess/dataFusion/exportKafka',
    method: 'get',
    params: query
  })
}
export function addrecord_dataFusion(data) {
  return request({
    url: '/dataaccess/dataFusion/add',
    method: 'post',
    data: data
  })
}
// 查询单条接口
//  /dataaccess/manual/{id}
export function getrecord_dataFusion(manualId) {
  return request({
    url: '/dataaccess/dataFusion/' + manualId,
    method: 'get'
  })
}
// 编辑接口
export function updaterecord_dataFusion(data) {
  return request({
    url: '/dataaccess/dataFusion/',
    method: 'put',
    data: data
  })
}
// 任务详情列表接口
export function getrwlist(query) {
  return request({
    url: "/dataaccess/dataFusion/taskList",
    method: 'get',
    params: query
  })
}
// 删除任务详情列表接口
export function delrwrecord(manualId) {
  return request({
    url: '/dataaccess/dataFusion/' + manualId,
    method: 'delete'
  })
}
// 导出接口
export function exportrwrecord(data) {
  return request({
    url: ' /dataaccess/dataFusion/downloadTask',
    method: 'post',
    data: data
  })
}






