<template>
  <!-- 趋势总览 -->
  <div class="app-container">
    <!-- 左侧列表 -->
    <el-row :gutter="20" class="main-row">
      <el-col :span="6" class="left-panel">
        <el-card shadow="always" class="left-card">
          <div class="history_log">请选择主题</div>
          <el-select v-model="currentTopicId" placeholder="请选择主题" @change="handleTopicChange" style="width:333px">
            <el-option v-for="topic in topicOptions" :key="topic.id" :label="topic.categoryName" :value="topic.id" />
          </el-select>
          <h4>专题列表</h4>
          <el-input v-model="searchTopic" placeholder="搜索专题名称" clearable size="small" prefix-icon="el-icon-search"
            class="search-input" @keyup.enter="fetchThematicList" @input="handleSearchInput" />
          <div class="topic-list-wrapper">
            <div v-for="topic in thematicList" :key="topic.id" class="topic-item"
              :class="{ 'topic-item-active': currentThematicId === topic.id }" @click="handleThematicSelect(topic)">
              <div class="topic-info">
                <div class="topic-name">{{ topic.categoryName }}</div>
                <div class="topic-count">{{ topic.count || 0 }} 条数据</div>
              </div>
            </div>
            <div class="topic-empty" v-if="thematicList.length === 0">
              暂无专题数据
            </div>
          </div>
        </el-card>
      </el-col>
      <!-- 右侧内容区 -->
      <el-col :span="18" class="right-panel">
        <div class="right-container">
          <!-- 添加渐变背景和标签导航 -->
          <div class="page-header">
            <h3 class="page-title">堆下水桩土理货分析</h3>
            <div class="page-tabs">
              <span class="tab-item active">趋势总览</span>
              <span class="tab-item" @click="to_ksh">可视化时间轴</span>
              <span class="tab-item" @click="to_gjjs">高级检索</span>
              <!-- <span class="tab-item" @click="to_ztbg">专题报告</span> -->
            </div>
          </div>
          <!-- 搜索区域 -->
          <el-card class="search-card" shadow="never">
            <el-form :inline="true" :model="queryParams" class="search-form">
              <el-form-item label="分析周期:">
                <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
                  range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
              </el-form-item>
              <!-- <el-form-item label="时间粒度:">
                <el-select v-model="queryParams.period" placeholder="请选择" style="width: 120px">
                  <el-option label="日" value="day" />
                  <el-option label="周" value="week" />
                  <el-option label="月" value="month" />
                  <el-option label="季度" value="month" />
                  <el-option label="年" value="month" />
                </el-select>
              </el-form-item> -->
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
                <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
              <!-- <el-form-item style="float: right">
          <el-button type="success" icon="el-icon-download">导出</el-button>
        </el-form-item> -->
            </el-form>
          </el-card>
          <!-- 专题概述 -->
          <div class="description-box">
            <div>
              专题概述：{{ description }}
              <!-- 本系统基于<strong>{{ 专题名称 }}</strong>数据分析，提供实时监控、数据统计和可视化分析功能。通过多维度数据展示，帮助您更好地了解桩位分布、作业情况和问题预警，提升管理效率。 -->
            </div>
          </div>
          <!-- 美化统计卡片，添加渐变背景和图标 -->
          <el-row :gutter="24" class="stats-row">
            <el-col :xs="12" :sm="8" :md="6" :lg="5">
              <div class="stat-card stat-card-blue">
                <div class="stat-content">
                  <div class="stat-label">事件信息</div>
                  <div class="stat-value"> {{ event }} </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="8" :md="6" :lg="5">
              <div class="stat-card stat-card-green">
                <!-- <div class="stat-icon-wrapper">
            <i class="el-icon-s-data"></i>
          </div> -->
                <div class="stat-content">
                  <div class="stat-label">开源资讯</div>
                  <div class="stat-value"> {{ openSource }} </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="8" :md="6" :lg="5">
              <div class="stat-card stat-card-orange">
                <!-- <div class="stat-icon-wrapper">
            <i class="el-icon-warning"></i>
          </div> -->
                <div class="stat-content">
                  <div class="stat-label">舆情日报</div>
                  <div class="stat-value"> {{ daily }} </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="8" :md="6" :lg="5">
              <div class="stat-card stat-card-red">
                <!-- <div class="stat-icon-wrapper">
            <i class="el-icon-bell"></i>
          </div> -->
                <div class="stat-content">
                  <div class="stat-label">舆情专报</div>
                  <div class="stat-value"> {{ special }} </div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="8" :md="6" :lg="4">
              <div class="stat-card stat-card-purple">
                <!-- <div class="stat-icon-wrapper">
            <i class="el-icon-s-operation"></i>
          </div> -->
                <div class="stat-content">
                  <div class="stat-label">理论研究成果</div>
                  <div class="stat-value">{{ research }}</div>
                </div>
              </div>
            </el-col>
            <!-- <el-col :xs="12" :sm="8" :md="6" :lg="4">
        <div class="stat-card stat-card-cyan">
          <div class="stat-content">
            <div class="stat-label">桩位密度</div>
            <div class="stat-value">3.5<span class="unit">年</span></div>
            <div class="stat-date">2022-08~2025-08</div>
          </div>
        </div>
      </el-col> -->
          </el-row>
          <el-row :gutter="16" class="stats-row">
            <el-col :xs="12" :sm="8" :md="6" :lg="6">
              <div class="stat-card stat-card-blue">
                <div class="stat-content">
                  <div class="stat-label">时间跨度</div>
                  <div class="stat-value">{{ timespan.kuadu }}</div>
                  <div class="stat-value">{{ timespanStartEnd }}</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="8" :md="6" :lg="6">
              <div class="stat-card stat-card-green">
                <!-- <div class="stat-icon-wrapper">
            <i class="el-icon-s-data"></i>
          </div> -->
                <div class="stat-content">
                  <div class="stat-label">峰值时间</div>
                  <div class="stat-value">{{ peakTime }}</div>
                  <div class="stat-value">{{ peakTimeCount }}</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="8" :md="6" :lg="6">
              <div class="stat-card stat-card-orange">
                <!-- <div class="stat-icon-wrapper">
            <i class="el-icon-warning"></i>
          </div> -->
                <div class="stat-content">
                  <div class="stat-label">主要正面事件</div>
                  <div class="stat-value">{{ keyPositiveEvents }}</div>
                  <div class="stat-value">{{ keyPositiveEventsTime }}</div>
                  <!-- keyPositiveEventsTime -->
                </div>
              </div>
            </el-col>
            <el-col :xs="12" :sm="8" :md="6" :lg="6">
              <div class="stat-card stat-card-red">
                <!-- <div class="stat-icon-wrapper">
            <i class="el-icon-bell"></i>
          </div> -->
                <div class="stat-content">
                  <div class="stat-label">主要负面事件</div>
                  <div class="stat-value">{{ keyNegativeEvents }}</div>
                  <div class="stat-value">{{ keyNegativeEventsTime }}</div>
                  <!-- keyNegativeEventsTime -->
                </div>
              </div>
            </el-col>
          </el-row>
          <!-- 图表区域 -->
          <el-row :gutter="16" class="chart-row" v-if="isExist">
            <!--事件发展趋势 -->
            <!-- 这个图由后端回的字段决定展不展示，所以先隐藏，默认隐藏 -->
            <el-col :span="24">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-data-line"></i>
                    事件发展趋势
                  </span>
                  <div class="chart-actions">
                    <el-button @click="handelqz">权重设置</el-button>
                    <el-radio-group v-model="lineChartType" size="mini">
                      <el-radio-button label="折线图">趋势图</el-radio-button>
                      <el-radio-button label="柱状图">散点图</el-radio-button>
                    </el-radio-group>
                  </div>
                </div>
                <!-- 双折线 -->
                <div ref="lineChart" class="chart-container"></div>
                <!-- 散点图 -->
                <!-- <div ref="" class="chart-container"></div> -->
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="16" class="chart-row">
            <!-- -->
            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-s-data"></i>
                    情感趋势
                  </span>
                </div>
                <!-- 三折线图 -->
                <div ref="areaChart" class="chart-container"></div>
              </el-card>
            </el-col>


            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-pie-chart"></i>
                    专题相关实体
                  </span>
                </div>
                <!-- 词云图 -->
                <div ref="pieChart" class="chart-container"></div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="16" class="chart-row">

            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-s-data"></i>
                    事件类型分布
                  </span>
                </div>
                <!-- 饼图 -->
                <div ref="trendChart" class="chart-container"></div>
              </el-card>
            </el-col>


            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-pie-chart"></i>
                    专题声量趋势
                  </span>
                </div>
                <!-- 单条面积图 -->
                <!-- <div ref="" class="chart-container"></div> -->
                <div ref="slChart" class="chart-container"></div>

              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="16" class="chart-row">

            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-s-data"></i>
                    热点词云
                  </span>
                </div>
                <!-- 词云图 -->
                <div ref="rdpieChart" class="chart-container"></div>
              </el-card>
            </el-col>

            <el-col :xs="24" :sm="24" :md="12">
              <el-card class="chart-card" shadow="never">
                <div slot="header" class="chart-header">
                  <span class="chart-title">
                    <i class="el-icon-pie-chart"></i>
                    专题地域分布
                  </span>
                </div>
                <!-- 地域分布图 -->
                <div ref="dtChart" class="chart-container"></div>
              </el-card>
            </el-col>
          </el-row>
          <!-- 事件权重配置 -->

          <el-drawer title="事件权重配置" :visible.sync="drawer" size="20%" custom-class="fancy-drawer">
            <span class="history_log">
              调整不同类型事件的权重系数，将影响趋势图的分析结果。
            </span>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-button>重置默认</el-button>
              </el-col>
              <el-col :span="12">
                <el-button>应用配置</el-button>
              </el-col>
            </el-row>
          </el-drawer>
        </div>
      </el-col>
    </el-row>
    <!-- <el-drawer title="数据预览" :visible.sync="drawer" direction="rtl" size="25%" custom-class="data-preview-drawer"
      :close-on-click-modal="false"> -->

  </div>
</template>

<script>
import * as echarts from "echarts";
import {
  selectListBySystemType, // 获取所有主题
  selectThematicListById, // 根据主题查找专题
  selectTopicDetailById, // 根据主题ID获取专题详情

} from "@/api/historyanalysis/index";
import 'echarts-wordcloud';
export default {
  name: "PileAnalysis",
  data() {
    return {
      // 是否展示折线图
      isExist: true,
      // 左侧主题和专题列表
      topicOptions: [],
      thematicList: [],
      searchTopic: '',
      currentTopicId: null,
      currentThematicId: null,
      currentThematicName: '', // 新增：存储当前专题名称
      typeCount: {
        openSource: 0,
        event: 0,
        daily: 0,
        special: 0,
        research: 0
      },

      // 规则配置页状态
      // isSettingVisible: false,
      activeTab: 'openSource',

      // 右侧预览抽屉
      drawer: false,
      previewData: [],
      previewTotal: 0,

      // 规则数据（严格匹配接口层级：ruleSchedule内包含thematicId和ruleType）
      ruleForm: {
        ruleConfigs: [
          {
            fieldName: '',
            operator: '',
            valueList: '',
            id: '' // 用于修改接口的规则ID
          }
        ],
        ruleSchedule: {
          frequency: '',
          dayOfWeek: '',
          executeHour: '',
          thematicId: '',
          ruleType: 1,
        }
      },

      // 下拉框选项
      fieldOptions: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      operatorOptions: [],
      frequencyTypeOptions: [], // 执行频率选项（每天/每周）
      weekdayOptions: [], // 周几选项
      executeHourOptions: [], // 执行时间选项

      // 右侧列表数据
      tableData: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      loading: {
        openSource: false,
        event: false,
        daily: false,
        special: false,
        theory: false
      },
      total: {
        openSource: 0,
        event: 0,
        daily: 0,
        special: 0,
        theory: 0
      },

      selectedIds: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      // };
      // ***********************************
      // 专题描述字段
      description: '',
      daily: '',                  // 舆情日报
      event: '',                   // 事件信息
      openSource: '',              // 开源咨询
      research: '',                // 理论研究成果
      special: '',                 // 舆情专报
      timespan: '',            //时间跨度
      timespanStartEnd: '',  //时间跨度开始到结束
      peakTime: '',             //峰值时间
      peakTimeCount: '',   //峰值月份事件数量
      keyPositiveEvents: '',  //主要正面事件名称
      keyPositiveEventsTime: '',   //主要正面事件发生事件
      keyPositiveEventsPopularity: '',   //主要正面事件热度
      keyNegativeEvents: '',   //主要负面事件名称
      keyNegativeEventsTime: '',   //主要负面事件发生事件
      keyNegativeEventsPopularity: '',   //主要负面事件热度
      // },
      // **********
      dateRange: [],
      // 右侧弹出层
      drawer: false,
      queryParams: {
        startDate: "2025-04-17",
        endDate: "2025-05-15",
        period: "day",
      },
      // 这里改成变量可切换的两个值
      lineChartType: "折线图",
      // 事件发展趋势图
      lineChart: null,
      // 情感趋势图
      areaChart: null,
      // 专题相关实体词云图
      pieChart: null,
      // 事件类型分布图
      trendChart: null,
      // 专题声量分布图
      slChart: null,
      // 热点词云图
      rdpieChart: null,
      // 地域分布图
      dtChart: null,
    };
  },
  mounted() {
    this.fetchTopicOptions();
    this.fetchFieldOptions();
    this.$nextTick(() => {
      this.initCharts();
      window.addEventListener("resize", this.handleResize);
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
    if (this.lineChart) this.lineChart.dispose();
    if (this.areaChart) this.areaChart.dispose();
    if (this.pieChart) this.pieChart.dispose();
    // if (this.rdpieChart) this.rdpieChart.dispose();
    if (this.trendChart) this.trendChart.dispose();
    if (this.slChart) this.slChart.dispose();
    if (this.dtChart) this.dtChart.dispose();
    if (this.rdpieChart) this.rdpieChart.dispose();

  },
  created() {
    // 初始化时加载基础数据
    this.fetchTopicOptions();
    this.fetchFieldOptions();
  },
  methods: {
    // 获取主题下拉选项
    fetchTopicOptions() {
      const params = { systemType: 1 };
      selectListBySystemType(params)
        .then(response => {
          if (response?.code === 200) {
            this.topicOptions = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名主题'
            }));
            if (this.topicOptions.length > 0) {
              this.currentTopicId = this.topicOptions[0].id;
              this.fetchThematicList();
            }
          } else {
            this.topicOptions = [];
          }
        })
        .catch(error => {
          console.error('获取主题列表失败:', error);
          this.$message.error('获取主题列表失败，请稍后重试');
          this.topicOptions = [];
        });
    },

    // 输入框搜索专题
    handleSearchInput() {
      this.fetchThematicList();
    },

    // 获取专题列表
    fetchThematicList() {
      const params = {
        id: this.currentTopicId,
        searchName: this.searchTopic,
        ruleType: ''
      };
      selectThematicListById(params)
        .then(response => {
          if (response?.code === 200) {
            this.thematicList = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名专题',
              count: item.count || 0
            }));
            if (this.thematicList.length > 0) {
              this.currentThematicId = this.thematicList[0].id;
              this.currentThematicName = this.thematicList[0].categoryName; // 新增：同步专题名称
              this.ruleForm.ruleSchedule.thematicId = this.currentThematicId;
              // this.loadData('openSource');
              // this.fetchTypeCount();
            } else {
              this.currentThematicId = null;
              this.currentThematicName = ''; // 新增：清空专题名称
              this.ruleForm.ruleSchedule.thematicId = '';
              this.typeCount = { openSource: 0, event: 0, daily: 0, special: 0, research: 0 };
            }
          } else {
            this.thematicList = [];
            this.currentThematicId = null;
            this.currentThematicName = ''; // 新增：清空专题名称
            this.ruleForm.ruleSchedule.thematicId = '';
          }
        })
        .catch(error => {
          console.error('获取专题列表失败:', error);
          this.$message.error('获取专题列表失败，请稍后重试');
          this.thematicList = [];
          this.currentThematicId = null;
          this.currentThematicName = ''; // 新增：清空专题名称
          this.ruleForm.ruleSchedule.thematicId = '';
        });
    },
    // 主题选择变化
    handleTopicChange() {
      this.fetchThematicList();
    },

    // 专题选择
    async handleThematicSelect(data) {
      this.currentThematicId = data.id;
      const params = {
        id: this.currentThematicId,
        // searchName: this.searchTopic,
        // ruleType: ''
      };
      let response = await selectTopicDetailById(params);
      if (response.code === 200) {
        this.description = response.data.description;
        this.daily = response.data.daily;
        this.event = response.data.event;
        this.openSource = response.data.openSource;
        this.research = response.data.research;
        this.special = response.data.special;
        this.timespan = response.data.timespan;
        this.timespanStartEnd = response.data.timespanStartEnd;
        this.peakTime = response.data.peakTime;
        this.peakTimeCount = response.data.peakTimeCount;
        this.keyPositiveEvents = response.data.keyPositiveEvents;
        this.keyPositiveEventsTime = response.data.keyPositiveEventsTime;
        this.keyPositiveEventsPopularity = response.data.keyPositiveEventsPopularity;
        this.keyNegativeEvents = response.data.keyNegativeEvents;
        this.keyNegativeEventsTime = response.data.keyNegativeEventsTime;
        this.keyNegativeEventsPopularity = response.data.keyNegativeEventsPopularity;
        // this.ruleForm.ruleSchedule.thematicId = data.id;
      } else { }
      // })
      // .catch(error => { });
      // if (response?.code === 200) {

      //   }));
      //   else {}
      // this.currentThematicName = data.categoryName; // 新增：同步专题名称
      // this.ruleForm.ruleSchedule.thematicId = data.id;
      // // this.isSettingVisible = false;
      // this.loadData(this.activeTab);
      // // this.fetchTypeCount();

    },





    // 获取字段下拉框数据
    fetchFieldOptions() {
      const tabTypes = {
        openSource: 2,
        event: 1,
        daily: 1,
        special: 1,
        theory: 1
      };
      // Object.keys(tabTypes).forEach(tab => {
      //   const type = tabTypes[tab];
      //   fields({ type })
      //     .then(response => {
      //       if (response?.code === 200) {
      //         this.fieldOptions[tab] = (response.data || []).map(item => ({
      //           fieldName: item.fieldName,
      //           displayName: item.displayName || '未命名字段',
      //           fieldType: item.fieldType
      //         }));
      //       } else {
      //         this.fieldOptions[tab] = [];
      //       }
      //     })
      //     .catch(error => {
      //       console.error(`获取${tab}字段列表失败:`, error);
      //       this.$message.error(`获取${tab}字段列表失败，请稍后重试`);
      //       this.fieldOptions[tab] = [];
      //     });
      // });
    },

    fetchOperatorOptions(fieldType) {
      if (!fieldType) {
        this.operatorOptions = [];
        return;
      }
      const params = { fieldType };
      operators(params)
        .then(response => {
          if (response?.code === 200) {
            this.operatorOptions = (response.data || []).map(item => ({
              label: item.label || '未命名操作符',
              value: item.value
            }));
          } else {
            this.operatorOptions = [];
          }
        })
        .catch(error => {
          console.error('获取操作符列表失败:', error);
          this.$message.error('获取操作符列表失败，请稍后重试');
          this.operatorOptions = [];
        });
    },




    // *******************************************************************
    to_ksh() {
      this.$router.push("/history/line");
    },
    to_gjjs() {
      this.$router.push("/concract/gjjs");

    },
    to_ztbg() {
      this.$router.push("/concract/ztbg");

    },
    to_index() {
      this.$router.push("/concract/document");

    },
    // /concract/ztbg
    // /concract/document
    handelqz() {
      // drawerfalse,
      this.drawer = true;
    },
    handleQuery() {
      this.$message.success("查询成功");
      this.initCharts();
    },
    resetQuery() {
      this.queryParams = {
        startDate: "2025-04-17",
        endDate: "2025-05-15",
        period: "day",
      };
    },
    handleResize() {
      if (this.lineChart) this.lineChart.resize();
      if (this.areaChart) this.areaChart.resize();
      if (this.pieChart) this.pieChart.resize();
      if (this.trendChart) this.trendChart.resize();
      if (this.slChart) this.slChart.resize();
      if (this.dtChart) this.dtChart.resize();
      if (this.rdpieChart) this.rdpieChart.resize();

    },
    initCharts() {
      this.initLineChart();
      this.initAreaChart();
      this.initPieChart();
      this.initTrendChart();
      this.initslChart();
      this.initdtChart();
      this.initrdpieChart();

    },
    initLineChart() {
      if (this.lineChart) this.lineChart.dispose();
      this.lineChart = echarts.init(this.$refs.lineChart);

      const option = {
        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#ddd",
          borderWidth: 1,
          textStyle: { color: "#333" },
        },
        legend: {
          // data: ["专题声量趋势",],
          //  type: 'line',
          top: 10,
          right: 20,
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "15%",
          containLabel: true,
        },

        xAxis: {
          type: "category",
          boundaryGap: false,
          // 实际上在后台获取data
          data: [
            "2023/01",
            "2023/02",
            "2023/03",
            "2023/04",
            "2023/05",
            "2023/06",
            "2023/07",
            "2023/08",
            "2023/09",
            "2023/10",
          ],
          // axisLine: { lineStyle: { color: "#ddd" } },
          // axisLabel: { color: "#666" },
        },
        yAxis: {
          type: "value",
          // axisLine: { show: false },
          // axisTick: { show: false },
          // axisLabel: { color: "#666" },
          // splitLine: { lineStyle: { color: "#f0f0f0" } },
        },
        series: [
          {
            name: '原始事件',
            type: 'line',
            stack: 'Total',
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: '加权事件',
            type: 'line',
            stack: 'Total',
            data: [220, 182, 191, 234, 290, 330, 310]
          },

        ]
      };

      this.lineChart.setOption(option);
    },
    // 纵坐标是啥？？
    initAreaChart() {
      if (this.areaChart) this.areaChart.dispose();
      this.areaChart = echarts.init(this.$refs.areaChart);

      const option = {
        animation: true,
        animationDuration: 1800,
        animationEasing: "cubicOut",
        animationDelay: function (idx) {
          return idx * 100;
        },
        tooltip: {
          trigger: "axis",
          backgroundColor: "rgba(255, 255, 255, 0.95)",
          borderColor: "#e4e4e4",
          borderWidth: 1,
          padding: 0,
          zIndex: 1000,
          textStyle: {
            color: "#333",
            fontSize: 14,
          },
          extraCssText:
            "box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); border-radius: 6px;",
          formatter: function (params) {
            var result =
              '<div style="padding:8px;background-color:rgba(255,255,255,0.95);border-radius:6px;box-shadow:0 3px 10px rgba(0,0,0,0.15);min-width:180px;">' +
              '<div style="font-size:16px;font-weight:bold;color:#333;margin-bottom:8px;text-align:center;border-bottom:1px solid #f0f0f0;padding-bottom:6px;">' +
              params[0].name +
              "</div>" +
              '<div style="padding:4px 0;">';
            params.forEach(function (item, index) {
              result +=
                '<div style="display:flex;align-items:center;margin:6px 0;">' +
                '<div style="width:12px;height:12px;border-radius:50%;background-color:' +
                item.color +
                ';margin-right:8px;box-shadow:0 1px 3px rgba(0,0,0,0.2);"></div>' +
                '<div style="color:' +
                (index === 0 ? "#409eff" : "#67c234") +
                ';font-weight:bold;margin-right:10px;flex:1;">' +
                item.seriesName +
                "</div>" +
                '<div style="font-weight:bold;color:#333;font-size:15px;">' +
                item.value +
                "万</div>" +
                "</div>";
            });
            result += "</div></div>";
            return result;
          },
        },
        legend: {
          data: [
            {
              name: "中立",
              icon: "rect",
              textStyle: {
                color: "#377dff",
                fontSize: 14,
                padding: [0, 0, 0, 5],
                fontWeight: "bold",
              },
            },
            {
              name: "正面",
              icon: "rect",
              textStyle: {
                color: "#5fd193",
                fontSize: 14,
                padding: [0, 0, 0, 5],
                fontWeight: "bold",
              },
            },
            {
              name: "负面",
              icon: "rect",
              textStyle: {
                color: "#ff0000",
                fontSize: 14,
                padding: [0, 0, 0, 5],
                fontWeight: "bold",
              },
            },
          ],
          width: "100%",
          left: "center",
          top: "bottom",
          textStyle: {
            color: "#000",
            padding: [0, 0, 0, 5],
          },
          itemStyle: {
            borderWidth: 0,
          },
          itemWidth: 24,
          itemHeight: 12,
          itemGap: 35,
          backgroundColor: "rgba(255, 255, 255, 0.8)",
          borderRadius: 6,
          borderColor: "#f0f0f0",
          borderWidth: 1,
          padding: 10,
        },
        grid: {
          left: "5%",
          right: "5%",
          bottom: "22%",
          top: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: true,
          data: ["2018", "2019", "2020", "2021", "2022", "2023", "2024"],
          axisLine: {
            show: true,
            lineStyle: {
              color: "#e0e6ed",
            },
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: "#e0e6ed",
            },
          },
          axisLabel: {
            color: "#556677",
            fontSize: 13,
            margin: 15,
          },
        },
        yAxis: {
          type: "value",
          name: "万元",
          nameTextStyle: {
            color: "#556677",
            fontSize: 13,
            fontWeight: "bold",
            padding: [0, 0, 0, 30],
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#e0e6ed",
            },
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: "#e0e6ed",
            },
          },
          axisLabel: {
            formatter: "{value}万",
            textStyle: {
              color: "#556677",
              fontSize: 13,
            },
            margin: 15,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#f5f7fa",
              type: "dashed",
            },
          },
        },
        series: [
          {
            name: "中立",
            type: "line",
            symbolSize: 8,
            symbol: "circle",
            smooth: false,
            color: "#377dff",
            yAxisIndex: 0,
            showSymbol: true,
            showAllSymbol: true,
            lineStyle: {
              width: 4,
              color: "#377dff",
              shadowColor: "rgba(55, 125, 255, 0.3)",
              shadowBlur: 10,
              shadowOffsetY: 5,
            },
            areaStyle: {
              color: "rgba(55, 125, 255, 0.1)",
            },
            emphasis: {
              focus: "series",
              itemStyle: {
                borderWidth: 3,
                borderColor: "rgba(55, 125, 255, 0.5)",
                shadowColor: "rgba(0, 0, 0, 0.3)",
                shadowBlur: 10,
                scale: true,
              },
              lineStyle: {
                width: 5,
              },
            },
            data: [300, 500, 400, 300, 400, 700, 800],
          },
          {
            name: "正面",
            type: "line",
            smooth: false,
            symbolSize: 8,
            symbol: "circle",
            color: "#5fd193",
            yAxisIndex: 0,
            showSymbol: true,
            showAllSymbol: true,
            lineStyle: {
              width: 4,
              color: "#5fd193",
              shadowColor: "rgba(95, 209, 147, 0.3)",
              shadowBlur: 10,
              shadowOffsetY: 5,
            },
            areaStyle: {
              color: "rgba(95, 209, 147, 0.1)",
            },
            emphasis: {
              focus: "series",
              itemStyle: {
                borderWidth: 3,
                borderColor: "rgba(95, 209, 147, 0.5)",
                shadowColor: "rgba(0, 0, 0, 0.3)",
                shadowBlur: 10,
                scale: true,
              },
              lineStyle: {
                width: 5,
              },
            },
            data: [800, 900, 1200, 800, 1200, 1100, 1200],
          },
          {
            name: "负面",
            type: "line",
            smooth: false,
            symbolSize: 8,
            symbol: "circle",
            color: "#ff0000",
            yAxisIndex: 0,
            showSymbol: true,
            showAllSymbol: true,
            lineStyle: {
              width: 4,
              color: "#ff0000",
              shadowColor: "rgba(255, 171, 0, 0.3)",
              shadowBlur: 10,
              shadowOffsetY: 5,
            },
            areaStyle: {
              color: "rgba(255, 0, 0, 0.1)",
            },
            emphasis: {
              focus: "series",
              itemStyle: {
                borderWidth: 3,
                borderColor: "rgba(255, 171, 0, 0.5)",
                shadowColor: "rgba(0, 0, 0, 0.3)",
                shadowBlur: 10,
                scale: true,
              },
              lineStyle: {
                width: 5,
              },
            },
            data: [400, 450, 400, 300, 400, 400, 600],
          },
        ],
      };

      this.areaChart.setOption(option);
    },
    initPieChart() {
      if (this.pieChart) this.pieChart.dispose();
      this.pieChart = echarts.init(this.$refs.pieChart);
      const datas = [
        { name: "雨伞", value: 30 },
        { name: "晴天", value: 28 },
        { name: "电话", value: 24 },
        { name: "手机", value: 23 },
        { name: "下雨", value: 22 },
        { name: "暴雨", value: 21 },
        { name: "多云", value: 20 },
        { name: "雨衣", value: 29 },
        { name: "屋檐", value: 28 },
        { name: "大风", value: 27 },
        { name: "台风", value: 26 },
        { name: "下雪", value: 25 },
        { name: "打雷", value: 24 },
        { name: "小雨", value: 30 },
        { name: "中雨", value: 18 },
        { name: "大雨", value: 14 },
        { name: "雷阵雨", value: 13 },
        { name: "下雪", value: 12 },
        { name: "小雪", value: 11 },
        { name: "中雪", value: 10 },
        { name: "大雪", value: 9 },
        { name: "暴雪", value: 8 },
        { name: "东风", value: 7 },
        { name: "南风", value: 6 },
        { name: "西北风", value: 5 },
        { name: "北风", value: 4 },
        { name: "闪电", value: 3 }
      ];
      const option = {
        tooltip: {
          show: true,
          position: 'top',
          textStyle: {
            fontSize: 30
          }
        },
        series: [{
          type: "wordCloud",
          // 网格大小，各项之间间距
          gridSize: 30,
          // 形状 circle 圆，cardioid  心， diamond 菱形，
          // triangle-forward 、triangle 三角，star五角星
          shape: 'circle',
          // 字体大小范围
          sizeRange: [20, 50],
          // 文字旋转角度范围
          rotationRange: [0, 90],
          // 旋转步值
          rotationStep: 90,
          // 自定义图形
          // maskImage: maskImage,
          left: 'center',
          top: 'center',
          right: null,
          bottom: null,
          // 画布宽
          width: '90%',
          // 画布高
          height: '80%',
          // 是否渲染超出画布的文字
          drawOutOfBound: false,
          textStyle: {
            normal: {
              color: function () {
                return 'rgb(' + [
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55)
                ].join(',') + ')';
              }
            },
            emphasis: {
              shadowBlur: 10,
              shadowColor: '#2ac'
            }
          },

          // const data = [
          //   { name: "雨伞", value: 30 },
          //   { name: "晴天", value: 28 },
          //   { name: "电话", value: 24 },
          //   { name: "手机", value: 23 },
          //   { name: "下雨", value: 22 },
          //   { name: "暴雨", value: 21 },
          //   { name: "多云", value: 20 },
          //   { name: "雨衣", value: 29 },
          //   { name: "屋檐", value: 28 },
          //   { name: "大风", value: 27 },
          //   { name: "台风", value: 26 },
          //   { name: "下雪", value: 25 },
          //   { name: "打雷", value: 24 },
          //   { name: "小雨", value: 30 },
          //   { name: "中雨", value: 18 },
          //   { name: "大雨", value: 14 },
          //   { name: "雷阵雨", value: 13 },
          //   { name: "下雪", value: 12 },
          //   { name: "小雪", value: 11 },
          //   { name: "中雪", value: 10 },
          //   { name: "大雪", value: 9 },
          //   { name: "暴雪", value: 8 },
          //   { name: "东风", value: 7 },
          //   { name: "南风", value: 6 },
          //   { name: "西北风", value: 5 },
          //   { name: "北风", value: 4 },
          //   { name: "闪电", value: 3 }
          // ],
          data: datas,

        }]
      };

      this.pieChart.setOption(option);
    },
    initTrendChart() {
      if (this.trendChart) this.trendChart.dispose();
      this.trendChart = echarts.init(this.$refs.trendChart);
      // 事件类型分布图

      const option = {
        title: {
          text: '大唐热源占比',
          //subtext: '纯属虚构',
          x: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: "{a} <br/>{b} : {c} ({d}%)"
        },
        color: ['#1891FF', '#12C3C3', '#FBCD14', '#F14864', '#8542E1', '#7DA6FF', '#4AC312', '#FB8F14', '#F148E7'],
        legend: {
          orient: 'vertical',
          x: 'left',
          left: 10,
          //right: 10,
          top: 20,
          // bottom: 20,
          // data: ['二电一期', '二电二期', '二电三期', '二电南线', '大唐一期', '大唐二期']
        },
        series: [
          {
            name: '大唐热源占比',
            type: 'pie',
            radius: '55%',
            center: ['50%', '60%'],
            label: {
              normal: {

                // formatter: '{a|{a}}{abg|}\n{hr|}\n  {b|{b}：}{c}  {per|{d}%}  ',
                formatter: '{b|{b}：}{c}   {per|{d}%} ',
                // backgroundColor: '#eee',
                // borderColor: '#aaa',
                // borderWidth: 1,
                // borderRadius: 4,
                rich: {
                  // a: {
                  //     color: '#999',
                  //     lineHeight: 22,
                  //     align: 'center'
                  // },
                  // hr: {
                  //     borderColor: '#aaa',
                  //     width: '100%',
                  //     borderWidth: 0.5,
                  //     height: 0
                  // },
                  // b: {
                  //     fontSize: 16,
                  //     lineHeight: 33
                  // },
                  per: {
                    color: '#eee',
                    backgroundColor: '#334455',
                    padding: [2, 4],
                    borderRadius: 13,
                    // center: ['50%', '50%']
                    //  position: 'inside',
                  }
                }
              }
            },
            data: [
              { value: 335, name: '二电一期', selected: true },
              { value: 310, name: '二电二期', selected: true },
              { value: 234, name: '二电三期', selected: true },
              { value: 135, name: '二电南线', selected: true },
              { value: 154, name: '大唐一期', selected: true },
              { value: 148, name: '大唐二期', selected: true }
            ],
            itemStyle: {
              normal: {

                // borderWidth: 4,
                // borderColor: '#ffffff',
                // label:{
                //       position: 'inside',
                // }
              },


              // normal: {
              //     label: {
              //         show: true,
              //         position: 'inside',
              //         // formatter: '{b} \n ({d}%)'
              //     }
              // },

              emphasis: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }

            },
            labelLine: {
              normal: {
                length: 5,
                length2: 1,
                smooth: true,
              }
            },
          }
        ]
      };

      this.trendChart.setOption(option);
    },
    initslChart() {
      if (this.slChart) this.slChart.dispose();
      this.slChart = echarts.init(this.$refs.slChart);
      // 事件类型分布图
      let xjgls = [160, 280, 300, 220, 280, 124, 120, 99, 220];
      let shuiDay = [60, 80, 30, 120, 80, 24, 20, 99, 60];
      let myData1 = [
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9"
      ];
      const option = {
        //你的代码
        backgroundColor: "#0C2B60",
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          confine: true, // 超出范围
          backgroundColor: "rgba(17,95,182,0.5)", //设置背景颜色
          //   borderColor: "rgba(255, 255, 255, .8)",
          formatter: "{b}<br>{a}：{c}/kwh<br>{a1}：{c1}/",
        },
        legend: {
          top: "4%",
          right: "2%",
          itemWidth: 18,
          itemHeight: 10,
          itemGap: 30,
          textStyle: {
            fontSize: 12,
            color: "#fff",
            padding: [0, 0, 0, 10],
          },
          // 图例样式

        },
        grid: {
          top: "18%",
          left: "4%",
          right: "3%",
          bottom: "2%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: myData1,
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              lineStyle: {
                color: "#1C82C5",
              },
            },
            axisLabel: {
              interval: 0,
              margin: 10,
              color: "#05D5FF",
              textStyle: {
                fontSize: 14,
                color: "#fff",
              },
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "",
            splitNumber: 5,
            type: "value",
            min: 0,
            max: 340,
            interval: 20,
            nameTextStyle: {
              color: "#fff",
              fontSize: 12,
              align: "center",
              padding: [0, 28, 0, 0],
            },
            axisLabel: {
              formatter: "{value}",
              color: "rgba(95, 187, 235, 1)",
              textStyle: {
                fontSize: 14,
                color: "#fff",
                lineHeight: 16,
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#1C82C5",
              },

            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(28, 130, 197, .3)",
                type: "dashed",
              },
            },
          },
          { type: 'value', show: false }  // 右侧 Y 轴，设置为不显示
        ],
        series: [

          {
            name: "今日负荷",
            type: "line",
            yAxisIndex: 1, // 与第二个 y 轴关联
            showSymbol: false,
            symbolSize: 8,
            lineStyle: {
              normal: {
                color: "#2073fe",
              },
            },
            itemStyle: {
              color: "#40acff",
              borderColor: "#40acff",
              borderWidth: 2,
            },
            // areaStyle 定义了图表区域的样式。
            // color 属性使用了线性渐变。
            // x 和 y 设置了渐变的起点（0, 0），而 x2 和 y2 设置了渐变的终点（0, 1），表示从上到下的渐变。
            // colorStops 定义了渐变的颜色停靠点。这里设置了从黄色（offset: 0）到透明（offset: 1）的渐变。
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 1,
                    color: 'transparent'
                  },
                  {
                    offset: 0,
                    color: '#051b47'
                  }
                ]
              }
            },
            data: xjgls, // 折线图的数据
          },

          {
            name: "昨日负荷",
            type: "line",
            yAxisIndex: 1, // 与第二个 y 轴关联
            showSymbol: false,
            symbolSize: 8,
            lineStyle: {
              normal: {
                color: "#449daa",
              },
            },
            itemStyle: {
              color: "#00FFF686",
              borderColor: "#00FFF686",
              borderWidth: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 1,
                    color: "transparent",
                  },
                  {
                    offset: 0,
                    color: "#0d3362",
                  }
                ]
              }
            },
            data: shuiDay, // 折线图的数据
          },
        ],
      };

      this.slChart.setOption(option);
    },
    initdtChart() {
      if (this.dtChart) this.dtChart.dispose();
      this.dtChart = echarts.init(this.$refs.dtChart);
      // let nameMap = {
      //   'Afghanistan': '阿富汗',
      //   'Angola': '安哥拉',
      //   'Albania': '阿尔巴尼亚',
      //   'United Arab Emirates': '阿联酋',
      //   'Argentina': '阿根廷',
      //   'Armenia': '亚美尼亚',
      //   'French Southern and Antarctic Lands': '法属南半球和南极领地',
      //   'Australia': '澳大利亚',
      //   'Austria': '奥地利',
      //   'Azerbaijan': '阿塞拜疆',
      //   'Burundi': '布隆迪',
      //   'Belgium': '比利时',
      //   'Benin': '贝宁',
      //   'Burkina Faso': '布基纳法索',
      //   'Bangladesh': '孟加拉国',
      //   'Bulgaria': '保加利亚',
      //   'The Bahamas': '巴哈马',
      //   'Bosnia and Herzegovina': '波斯尼亚和黑塞哥维那',
      //   'Belarus': '白俄罗斯',
      //   'Belize': '伯利兹',
      //   'Bermuda': '百慕大',
      //   'Bolivia': '玻利维亚',
      //   'Brazil': '巴西',
      //   'Brunei': '文莱',
      //   'Bhutan': '不丹',
      //   'Botswana': '博茨瓦纳',
      //   'Central African Republic': '中非共和国',
      //   'Canada': '加拿大',
      //   'Switzerland': '瑞士',
      //   'Chile': '智利',
      //   'China': '中国',
      //   'Ivory Coast': '象牙海岸',
      //   'Cameroon': '喀麦隆',
      //   'Democratic Republic of the Congo': '刚果民主共和国',
      //   'Republic of the Congo': '刚果共和国',
      //   'Colombia': '哥伦比亚',
      //   'Costa Rica': '哥斯达黎加',
      //   'Cuba': '古巴',
      //   'Northern Cyprus': '北塞浦路斯',
      //   'Cyprus': '塞浦路斯',
      //   'Czech Republic': '捷克共和国',
      //   'Germany': '德国',
      //   'Djibouti': '吉布提',
      //   'Denmark': '丹麦',
      //   'Dominican Republic': '多明尼加共和国',
      //   'Algeria': '阿尔及利亚',
      //   'Ecuador': '厄瓜多尔',
      //   'Egypt': '埃及',
      //   'Eritrea': '厄立特里亚',
      //   'Spain': '西班牙',
      //   'Estonia': '爱沙尼亚',
      //   'Ethiopia': '埃塞俄比亚',
      //   'Finland': '芬兰',
      //   'Fiji': '斐',
      //   'Falkland Islands': '福克兰群岛',
      //   'France': '法国',
      //   'Gabon': '加蓬',
      //   'United Kingdom': '英国',
      //   'Georgia': '格鲁吉亚',
      //   'Ghana': '加纳',
      //   'Guinea': '几内亚',
      //   'Gambia': '冈比亚',
      //   'Guinea Bissau': '几内亚比绍',
      //   'Equatorial Guinea': '赤道几内亚',
      //   'Greece': '希腊',
      //   'Greenland': '格陵兰',
      //   'Guatemala': '危地马拉',
      //   'French Guiana': '法属圭亚那',
      //   'Guyana': '圭亚那',
      //   'Honduras': '洪都拉斯',
      //   'Croatia': '克罗地亚',
      //   'Haiti': '海地',
      //   'Hungary': '匈牙利',
      //   'Indonesia': '印尼',
      //   'India': '印度',
      //   'Ireland': '爱尔兰',
      //   'Iran': '伊朗',
      //   'Iraq': '伊拉克',
      //   'Iceland': '冰岛',
      //   'Israel': '以色列',
      //   'Italy': '意大利',
      //   'Jamaica': '牙买加',
      //   'Jordan': '约旦',
      //   'Japan': '日本',
      //   'Kazakhstan': '哈萨克斯坦',
      //   'Kenya': '肯尼亚',
      //   'Kyrgyzstan': '吉尔吉斯斯坦',
      //   'Cambodia': '柬埔寨',
      //   'South Korea': '韩国',
      //   'Kosovo': '科索沃',
      //   'Kuwait': '科威特',
      //   'Laos': '老挝',
      //   'Lebanon': '黎巴嫩',
      //   'Liberia': '利比里亚',
      //   'Libya': '利比亚',
      //   'Sri Lanka': '斯里兰卡',
      //   'Lesotho': '莱索托',
      //   'Lithuania': '立陶宛',
      //   'Luxembourg': '卢森堡',
      //   'Latvia': '拉脱维亚',
      //   'Morocco': '摩洛哥',
      //   'Moldova': '摩尔多瓦',
      //   'Madagascar': '马达加斯加',
      //   'Mexico': '墨西哥',
      //   'Macedonia': '马其顿',
      //   'Mali': '马里',
      //   'Myanmar': '缅甸',
      //   'Montenegro': '黑山',
      //   'Mongolia': '蒙古',
      //   'Mozambique': '莫桑比克',
      //   'Mauritania': '毛里塔尼亚',
      //   'Malawi': '马拉维',
      //   'Malaysia': '马来西亚',
      //   'Namibia': '纳米比亚',
      //   'New Caledonia': '新喀里多尼亚',
      //   'Niger': '尼日尔',
      //   'Nigeria': '尼日利亚',
      //   'Nicaragua': '尼加拉瓜',
      //   'Netherlands': '荷兰',
      //   'Norway': '挪威',
      //   'Nepal': '尼泊尔',
      //   'New Zealand': '新西兰',
      //   'Oman': '阿曼',
      //   'Pakistan': '巴基斯坦',
      //   'Panama': '巴拿马',
      //   'Peru': '秘鲁',
      //   'Philippines': '菲律宾',
      //   'Papua New Guinea': '巴布亚新几内亚',
      //   'Poland': '波兰',
      //   'Puerto Rico': '波多黎各',
      //   'North Korea': '北朝鲜',
      //   'Portugal': '葡萄牙',
      //   'Paraguay': '巴拉圭',
      //   'Qatar': '卡塔尔',
      //   'Romania': '罗马尼亚',
      //   'Russia': '俄罗斯',
      //   'Rwanda': '卢旺达',
      //   'Western Sahara': '西撒哈拉',
      //   'Saudi Arabia': '沙特阿拉伯',
      //   'Sudan': '苏丹',
      //   'South Sudan': '南苏丹',
      //   'Senegal': '塞内加尔',
      //   'Solomon Islands': '所罗门群岛',
      //   'Sierra Leone': '塞拉利昂',
      //   'El Salvador': '萨尔瓦多',
      //   'Somaliland': '索马里兰',
      //   'Somalia': '索马里',
      //   'Republic of Serbia': '塞尔维亚共和国',
      //   'Suriname': '苏里南',
      //   'Slovakia': '斯洛伐克',
      //   'Slovenia': '斯洛文尼亚',
      //   'Sweden': '瑞典',
      //   'Swaziland': '斯威士兰',
      //   'Syria': '叙利亚',
      //   'Chad': '乍得',
      //   'Togo': '多哥',
      //   'Thailand': '泰国',
      //   'Tajikistan': '塔吉克斯坦',
      //   'Turkmenistan': '土库曼斯坦',
      //   'East Timor': '东帝汶',
      //   'Trinidad and Tobago': '特里尼达和多巴哥',
      //   'Tunisia': '突尼斯',
      //   'Turkey': '土耳其',
      //   'United Republic of Tanzania': '坦桑尼亚联合共和国',
      //   'Uganda': '乌干达',
      //   'Ukraine': '乌克兰',
      //   'Uruguay': '乌拉圭',
      //   'United States of America': '美国',
      //   'Uzbekistan': '乌兹别克斯坦',
      //   'Venezuela': '委内瑞拉',
      //   'Vietnam': '越南',
      //   'Vanuatu': '瓦努阿图',
      //   'West Bank': '西岸',
      //   'Yemen': '也门',
      //   'South Africa': '南非',
      //   'Zambia': '赞比亚',
      //   'Zimbabwe': '津巴布韦'
      // }
      // let gMapData = [{
      //   name: 'Afghanistan',
      //   value: 100,
      //   latitude: 42.5,
      //   longitude: 1.5
      // }, {
      //   name: 'China',
      //   value: 100,
      //   latitude: 39.**********,
      //   longitude: 116.**********
      // }, {
      //   name: 'Armenia',
      //   value: 100,
      //   latitude: 40.**********,
      //   longitude: 45.**********
      // },];
      // const option = {
      //   backgroundColor: '#404a59',
      //   tooltip: {
      //     trigger: 'item',
      //     formatter: function (params) {

      //       //  租金总数 租户数 出租率 收缴率
      //       // 20,000 20,000 68% 78%
      //       let res = `<div class="mapInfos" style='background:red'>
      //               <div class="mapInfos-item">
      //                 <div class="round round_1"></div>
      //                 <span class="label">资产数</span>
      //                 <span class="num">12665</span>
      //               </div>
      //               <div class="mapInfos-item">
      //                 <div class="round round_2"></div>
      //                 <span class="label">租金总数</span>
      //                 <span class="num">20,000</span>
      //               </div>
      //               <div class="mapInfos-item">
      //                 <div class="round round_3"></div>
      //                 <span class="label">租户数</span>
      //                 <span class="num">20,000</span>
      //               </div>
      //               <div class="mapInfos-item">
      //                 <div class="round round_4"></div>
      //                 <span class="label">出租率</span>
      //                 <span class="num">68%</span>
      //               </div>
      //               <div class="mapInfos-item">
      //                 <div class="round round_5"></div>
      //                 <span class="label">收缴率</span>
      //                 <span class="num">78%</span>
      //               </div>
      //           </div>`
      //       return res

      //     }
      //   },
      //   geo: {
      //     type: 'map',
      //     map: 'world',
      //     roam: false,
      //     label: {
      //       emphasis: {
      //         show: false
      //       }
      //     },
      //     itemStyle: {
      //       normal: {
      //         color: '#51FFFF',
      //         borderColor: '#0285FF'
      //       },
      //       emphasis: {
      //         color: '#51FFFF'
      //       }
      //     }

      //   },
      //   series: [{
      //     type: 'effectScatter',
      //     coordinateSystem: 'geo',
      //     data: gMapData.map(function (itemOpt) {
      //       return {
      //         name: nameMap[itemOpt.name],
      //         value: [
      //           itemOpt.longitude,
      //           itemOpt.latitude,
      //           itemOpt.value
      //         ],
      //         label: {
      //           normal: {
      //             show: true,
      //             position: 'top',
      //             formatter: '{b}',
      //             textStyle: {
      //               color: 'black',
      //               fontWeight: 'bold'
      //             }
      //           }
      //         },
      //         symbolSize: 10,
      //         showEffectOn: 'render',
      //         rippleEffect: {
      //           brushType: 'stroke'
      //         },
      //         hoverAnimation: true,
      //         itemStyle: {
      //           normal: {
      //             color: '#ff3300',
      //             shadowBlur: 10,
      //             shadowColor: 'black'
      //           }
      //         }
      //       };
      //     })

      //   }],



      // }
      // this.dtChart.setOption(option);

    },
   
   
    initrdpieChart() {
      if (this.rdpieChart) this.rdpieChart.dispose();
      this.rdpieChart = echarts.init(this.$refs.rdpieChart);
      const datas = [
        { name: "雨伞", value: 30 },
        { name: "晴天", value: 28 },
        { name: "电话", value: 24 },
        { name: "手机", value: 23 },
        { name: "下雨", value: 22 },
        { name: "暴雨", value: 21 },
        { name: "多云", value: 20 },
        { name: "雨衣", value: 29 },
        { name: "屋檐", value: 28 },
        { name: "大风", value: 27 },
        { name: "台风", value: 26 },
        { name: "下雪", value: 25 },
        { name: "打雷", value: 24 },
        { name: "小雨", value: 30 },
        { name: "中雨", value: 18 },
        { name: "大雨", value: 14 },
        { name: "雷阵雨", value: 13 },
        { name: "下雪", value: 12 },
        { name: "小雪", value: 11 },
        { name: "中雪", value: 10 },
        { name: "大雪", value: 9 },
        { name: "暴雪", value: 8 },
        { name: "东风", value: 7 },
        { name: "南风", value: 6 },
        { name: "西北风", value: 5 },
        { name: "北风", value: 4 },
        { name: "闪电", value: 3 }
      ];
      const option = {
        tooltip: {
          show: true,
          position: 'top',
          textStyle: {
            fontSize: 30
          }
        },
        series: [{
          type: "wordCloud",
          // 网格大小，各项之间间距
          gridSize: 30,
          // 形状 circle 圆，cardioid  心， diamond 菱形，
          // triangle-forward 、triangle 三角，star五角星
          shape: 'circle',
          // 字体大小范围
          sizeRange: [20, 50],
          // 文字旋转角度范围
          rotationRange: [0, 90],
          // 旋转步值
          rotationStep: 90,
          // 自定义图形
          // maskImage: maskImage,
          left: 'center',
          top: 'center',
          right: null,
          bottom: null,
          // 画布宽
          width: '90%',
          // 画布高
          height: '80%',
          // 是否渲染超出画布的文字
          drawOutOfBound: false,
          textStyle: {
            normal: {
              color: function () {
                return 'rgb(' + [
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55)
                ].join(',') + ')';
              }
            },
            emphasis: {
              shadowBlur: 10,
              shadowColor: '#2ac'
            }
          },

          data: datas,

        }]
      };

      this.rdpieChart.setOption(option);


    },

  },
};
</script>

<style scoped>
.history_log {
  text-align: center;
  font-size: 14px;
  color: #606266;
  font-weight: 600;
}

.app-container {
  padding: 20px;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  min-height: 100vh;
}

/* 美化页面标题，添加渐变效果和标签导航 */
.page-header {
  margin-bottom: 20px;
  /* background: rgba(255, 255, 255, 0.95); */
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 16px 0;
}

.page-tabs {
  display: flex;
  gap: 8px;
}

.tab-item {
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
  background: #f5f7fa;
}

.tab-item:hover {
  background: #e8eaf0;
  color: #409eff;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 500;
}

/* 添加描述文本区域样式 */
.description-box {
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.9) 100%);
  padding: 16px 24px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-left: 4px solid #667eea;
}

.description-box p {
  margin: 0;
  font-size: 14px;
  line-height: 1.8;
  color: #606266;
}

.description-box strong {
  color: #667eea;
  font-weight: 600;
}

.search-card {
  margin-bottom: 16px;
  border-radius: 12px;
}

.stats-row {
  margin-bottom: 16px;
}

/* 重新设计统计卡片，使用渐变背景和图标 */
.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  font-size: 28px;
  color: white;
  transition: all 0.3s;
}

.stat-card:hover .stat-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
}

.stat-card-blue .stat-icon-wrapper {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card-green .stat-icon-wrapper {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stat-card-orange .stat-icon-wrapper {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card-red .stat-icon-wrapper {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-card-purple .stat-icon-wrapper {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card-cyan .stat-icon-wrapper {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  position: relative;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #303133;
  line-height: 1.2;
}

.stat-value .unit {
  font-size: 16px;
  font-weight: 500;
  margin-left: 4px;
  color: #909399;
}

.stat-date {
  font-size: 12px;
  color: #c0c4cc;
  margin-top: 8px;
}

.chart-row {
  margin-bottom: 16px;
}

.chart-card {
  border-radius: 12px;
  margin-bottom: 16px;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
}

.chart-card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-title i {
  color: #667eea;
}

.chart-actions {
  display: flex;
  gap: 10px;
}

.chart-container {
  width: 100%;
  height: 400px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-tabs {
    flex-wrap: wrap;
  }

  .chart-container {
    height: 300px;
  }

  .stat-value {
    font-size: 28px;
  }

  .stat-icon-wrapper {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }
}

/* 其他样式 */

.main-row {
  height: 100%;
  min-height: calc(100vh - 60px);
  padding: 15px;
}

.left-panel,
.right-panel {
  height: 100%;
}

.left-card,
.right-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-input {
  margin-bottom: 15px;
}

.topic-list-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 8px 5px;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  margin-bottom: 6px;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.topic-item-active {
  border-color: #409eff;
  background-color: #f0f7ff;
}

.topic-item:hover {
  background-color: #f5f7fa;
}

.topic-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.topic-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.topic-count {
  font-size: 12px;
  color: #409eff;
}

.topic-setting {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  color: #909399;
  cursor: pointer;
  transition: color 0.2s;
  padding: 0;
}

.topic-setting:hover {
  color: #409eff;
}

.topic-empty {
  text-align: center;
  padding: 30px 0;
  color: #ffffff;
  font-size: 14px;
  background-color: transparent;
  border-radius: 4px;
  margin: 0 5px;
}

.content-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 5px 0;
}

.pagination {
  margin-top: 15px;
  text-align: right;
}

::v-deep .el-tabs__content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

::v-deep .el-tab-pane {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 规则配置页样式 */
.rule-config {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.config-section {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #eee;
}

.config-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #303133;
  display: flex;
  align-items: center;
}

.config-section h4 i {
  margin-left: 5px;
  font-size: 14px;
  color: #909399;
}

.condition-group {
  margin-bottom: 15px;
}

.condition-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

/* 预览抽屉样式 */
::v-deep .data-preview-drawer {
  width: 25% !important;
}

::v-deep .data-preview-drawer .el-drawer__body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100% - 56px);
}

::v-deep .data-preview-drawer .el-drawer__header {
  padding: 14px 20px;
  border-bottom: 1px solid #eee;
}

.history_log {
  text-align: left;
  color: #606266;
  font-weight: 600;
}
</style>
