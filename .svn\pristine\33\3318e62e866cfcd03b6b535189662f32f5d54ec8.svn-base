<template>
  <!-- 文件导入界面 -->
  <div class="app-container">
    <p class="history_log">历史导入记录：共{{historyTotalCount}}条数据</p>
    <!-- 搜索栏 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="执行状态" prop="executionStatus">
        <el-select
          v-model="queryParams.executionStatus"
          placeholder="执行状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.file_import_runstatus"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类别" prop="category">
        <el-select
          v-model="queryParams.category"
          placeholder="类别"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.file_import_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['data:datain:fileImportAdd']"
          >创建任务</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-upload2"
          size="mini"
          :disabled="multiple"
          @click="handleExport"
          v-hasPermi="['data:datain:fileImportUpload']"
          >批量导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['data:datain:fileImportDelete']"
          >批量删除</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 列表页 -->
    <el-table
      v-loading="loading"
      :data="roleList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务名称" prop="taskName" sortable />
      <el-table-column label="类别" prop="category" sortable>
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.file_import_category"
            :value="scope.row.category"
          />
        </template>
      </el-table-column>
      <el-table-column label="执行状态" prop="executionStatus">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.file_import_runstatus"
            :value="scope.row.executionStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="创建人"
        prop="createByName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="创建时间"
        align="center"
        sortable
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="
              scope.row.executionStatus != 1 && scope.row.executionStatus != 3
            "
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['data:datain:fileImportUpdate']"
            >编辑</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetails(scope.row)"
            v-hasPermi="['data:datain:fileImportView']"
            >查看</el-button
          >
          <el-button
            v-if="scope.row.executionStatus === 2"
            size="mini"
            type="text"
            icon="el-icon-circle-close"
            @click="handleStopRun(scope.row)"
            v-hasPermi="['data:datain:fileImportStopRun']"
            >终止运行</el-button
          >
          <el-button
            v-if="scope.row.executionStatus === 3 || scope.row.executionStatus === 4"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleRerun(scope.row)"
            v-hasPermi="['data:datain:fileImportReRun']"
            >重跑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-tickets"
            @click="handleRunLog(scope.row)"
            v-hasPermi="['data:datain:fileImportRunLog']"
            >运行日志</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['data:datain:fileImportDelete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改文件导入任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <p class="dialogTitle">
        选择对应模板，导入事件信息、舆情日报、舆情专报、理论研究成果等信息
      </p>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <p class="dialogText">第一步：创建任务</p>
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="form.taskName" placeholder="请输入任务名称" />
        </el-form-item>

        <p class="dialogText">第二步：选择导入模板</p>
        <p class="secondTitle">
          请先下载模板，按照模板格式填写数据，上传以批量导入数据
        </p>

        <el-form-item label="选择模板" prop="category">
          <el-radio-group
            v-model="form.category"
            :disabled="temDisabled"
            class="template-download-container"
          >
            <el-radio :label="1" class="template-item">
              <div class="template-header">
                <h4>事件信息模板</h4>
              </div>
              <p class="template-desc">
                适用于导入各类时间，包含时间、地点、概况等信息
              </p>
              <p class="template-info">docx格式</p>
            </el-radio>

            <el-radio :label="2" class="template-item">
              <div class="template-header">
                <h4>舆情日报模板</h4>
              </div>
              <p class="template-desc">
                将历史制作的日报文件导入，系统自动解析
              </p>
              <p class="template-info">docx格式</p>
            </el-radio>

            <el-radio :label="3" class="template-item">
              <div class="template-header">
                <h4>舆情专报模板</h4>
              </div>
              <p class="template-desc">
                将历史制作的专报文件导入，系统自动解析
              </p>
              <p class="template-info">docx格式</p>
            </el-radio>

            <el-radio :label="4" class="template-item">
              <div class="template-header">
                <h4>理论研究成果模板</h4>
              </div>
              <p class="template-desc">将理论研究成果文件导入，系统自动解析</p>
              <p class="template-info">docx格式</p>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :disabled="!form.category"
            @click="downloadTemplateByCategory"
          >
            下载选中模板
          </el-button>
        </el-form-item>

        <p class="dialogText">第三步：上传文件</p>
        <p class="secondTitle">
          请上传已填写好的模板文件，支持一次批量导入多个文件
        </p>
        <el-form-item label-width="0" label="" prop="file">
          <commonUpload
            v-model="form.file"
            :file-size="100"
            :file-type="['xls', 'xlsx', 'doc', 'docx', 'wps']"
            upload-url="/api/upload"
          />
        </el-form-item>
      </el-form>

      <el-popover
        placement="left"
        width="400"
        class="dialogPopover"
        trigger="hover"
      >
        <div class="notice-container">
          <div class="notice-header">导入须知</div>
          <div class="notice-content">
            <h3>模板选择</h3>
            <ul>
              <li>
                请确保文件信息与选择的模板一致，否则将导致导入失败或数据错误。
              </li>
              <!-- <li>全量数据导入可能需要较长时间，请耐心等待。系统会在后台执行导入任务。</li>
              <li>导入过程中请勿重复提交相同的导入任务，以免造成数据重复。</li>
              <li>如导入失败，请查看运行日志或联系技术支持获取帮助。</li> -->
            </ul>
            <h3>常见问题</h3>
            Q：各类模板适用的情况 A：事件信息模板： 舆情日报模板：
            舆情专报模板： 理论研究成果模板：
          </div>
        </div>
        <el-tag slot="reference" type="success">导入须知</el-tag>
      </el-popover>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">导入并解析</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情弹框 -->
    <el-dialog
      title="任务详细信息"
      :visible.sync="openDetailDialog"
      width="800px"
      append-to-body
    >
    <!-- {{currentTask}} -->
      <el-descriptions :column="1" border>
        <el-descriptions-item
          label="任务名称"
          label-class-name="my-label"
          content-class-name="my-content"
          >{{
            currentTask.taskName || "xxxxxxxxxxxxxxxxxxx"
          }}</el-descriptions-item
        >
        <el-descriptions-item label="类别">{{
          getCategoryText(currentTask.category) || "类别信息"
        }}</el-descriptions-item>
        <el-descriptions-item label="执行状态">{{
          getStatusText(currentTask.executionStatus) || "执行状态信息"
        }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{
          currentTask.createByName || "创建人信息"
        }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{
          parseTime(currentTask.createTime) || "创建时间信息"
        }}</el-descriptions-item>
        <el-descriptions-item label="上传文件"></el-descriptions-item>
      </el-descriptions>
      <!-- {{fileList}} -->
      <!-- 文件列表 可下载 -->
      <transition-group
        name="file-list"
        tag="div"
        class="file-list"
        v-if="fileList.length > 0"
      >
      <!-- {{fileList}} -->
        <div v-for="file in fileList" :key="file.id" class="file-item">
          <div class="file-info">
            <div class="file-icon" :class="getFileIconClass(file.filename)">
              <svg
                v-if="getFileType(file.filename) === 'excel'"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M10 12L14 16M14 12L10 16"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <svg
                v-else-if="getFileType(file.filename) === 'docx'"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12 11V17M10 13H14M10 15H14"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <svg
                v-else
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>

            <div class="file-details">
              <p class="file-name" :title="file.filename || file.name">
                {{ file.filename || file.name }}
              </p>
              <!-- <p class="file-size">
                {{ formatFileSize(file.fileSize || file.size) }}
              </p> -->
            </div>
          </div>

          <div class="file-actions">
            <button
              type="button"
              class="action-btn download-btn"
              @click="handleDownload(file)"
              title="下载"
            >
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M7 10L12 15L17 10"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12 15V3"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
            <button
              type="button"
              class="action-btn delete-btn"
              @click="handleDeleteFile(file)"
              title="删除"
            >
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3 6H5H21"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M10 11V17"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 11V17"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </transition-group>
    </el-dialog>

    <!-- 运行日志 -->
    <el-drawer
      title="运行日志"
      :visible.sync="drawer"
      :direction="direction"
      size="20%"
      custom-class="fancy-drawer"
    >
      <div class="stats-container">
        <el-card shadow="hover" class="stat-card success-card">
          <div @click="handleViewFailRecords(2)">
            <div class="stat-icon">
              <!-- <i class="el-icon-menu"></i> -->
              <img src="../imgs_erb/icon_counts.png" alt="" srcset="" />
            </div>
            <div class="stat-number">{{ totalCount }}</div>
            <div class="stat-label">总数据条数</div>
          </div>
        </el-card>
        <el-card shadow="hover" class="stat-card success-card">
          <div @click="handleViewFailRecords(1)">
            <div class="stat-icon">
              <img src="../imgs_erb/icon_success_counts.png" alt="" srcset="" />
            </div>
            <div class="stat-number">{{ successCount }}</div>
            <div class="stat-label">成功条数</div>
          </div>
        </el-card>
        <el-card shadow="hover" class="stat-card fail-card">
          <div @click="handleViewFailRecords(0)">
            <div class="stat-icon">
              <img src="../imgs_erb/icon_error_counts.png" alt="" srcset="" />
            </div>
            <div class="stat-number">{{ failCount }}</div>
            <div class="stat-label">失败条数</div>
          </div>
        </el-card>
      </div>
      <div class="fail-records-title">{{logFontTitle}}</div>
      <el-tabs
        v-model="activeTab"
        class="tabs-container"
        @tab-click="handleTabClick"
      >
        <el-tab-pane
          v-for="item in runLogData"
          :key="item.id"
          :label="`${item.filename} (${item.resultCount || 0}条)`"
          :name="item.id"
        >
          <div class="fail-data-item">
            <el-card class="box-card" v-if="fileInfo && fileInfo.length > 0">
              <div
                v-for="(item, index) in fileInfo"
                :key="item.id"
                class="text item"
              >
                {{ index + 1 }}. {{ item.eventName }}
              </div>
            </el-card>
            <el-empty v-else description="暂无记录"></el-empty>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script>
import {
  getFileImportList,
  getFileImportDetail,
  uploadFileImport,
  editFileImport,
  deleteFileImport,
  stopFileImportTask,
  rerunFileImportTask,
  downloadFileImportFile,
  getDialogRunLogCounts,
  getDialogRunLog,
  getFileIdFindNames,
  getDataCountsInfo
} from "@/api/data/fileImport";
import commonUpload from "@/views/components/commonUpload.vue";

export default {
  name: "FileImport",
  dicts: ["file_import_runstatus", "file_import_category"],
  components: { commonUpload },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openDetailDialog: false,
      // 右侧弹出层
      drawer: false,
      direction: "rtl",
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: undefined,
        executionStatus: undefined,
        category: undefined,
        sortOrder: "desc",
      },
      // 表单参数
      form: {},
      // 当前查看的任务详情
      currentTask: {},
      // 文件列表
      fileList: [],
      // 删除的文件ID列表
      deleteFileIds: [],
      // 原始文件列表ID（用于对比删除的文件）
      originalFileList: [],
      // 表单校验
      rules: {
        taskName: [
          { required: true, message: "任务名称不能为空", trigger: "blur" },
        ],
      },
      totalCount: 0,
      successCount: 0,
      failCount: 0,
      currentTaskId: "",
      runLogData: [],
      activeTab: "",
      fileInfo: [],
      historyTotalCount: 0,
      logFontTitle: '',
      temDisabled: false
    };
  },
  created() {
    this.getList();
    this.getDataCounts()
  },
  methods: {
    getDataCounts() {
      // 查询任务详情
      getDataCountsInfo()
        .then((response) => {
          if (response.data) {
            this.historyTotalCount = response.data
          } else {
            console.log("获取历史导入记录失败");
          }
        })
        .catch(() => {
          this.$message.error("获取任务详情失败");
        });
    },
    // 查看详情
    handleViewDetails(row) {
      this.currentTask = row;
      this.openDetailDialog = true;
      // 查询任务详情
      getFileImportDetail(row.id)
        .then((response) => {
          if (response.data) {
            this.currentTask = response.data.taskInfo || {};
            this.fileList = response.data.attachments || [];
          } else {
            console.log("获取任务详情失败");
          }
        })
        .catch(() => {
          this.$message.error("获取任务详情失败");
        });
    },
    // 运行日志条数
    handleRunLog(row) {
      this.currentTaskId = row.id;
      // 查询任务详情
      getDialogRunLogCounts(row.id)
        .then((response) => {
          this.drawer = true;
          if (response.data) {
            this.totalCount = response.data.allCount;
            this.successCount = response.data.totalSuccess;
            this.failCount = response.data.totalFail;
            this.handleViewFailRecords(2);
          } else {
            console.log("获取运行日志失败");
          }
        })
        .catch(() => {
          this.$message.error("获取运行日志失败");
        });
    },
    // 根据status和taskid获取查看运行日志
    handleViewFailRecords(status) {
      // console.log(status, 'status')
      if(status === 2) {
      this.logFontTitle = '总数据记录'
      }else if(status === 1) {
        this.logFontTitle = '成功记录'
      }else {
        this.logFontTitle = '失败记录'
      }
      let params = {
        status: status,
        taskId: this.currentTaskId,
      };
      getDialogRunLog(params)
        .then((response) => {
          if (response.data) {
            this.runLogData = response.data;
            // console.log(this.runLogData, 'runLogData');
            this.handleFileIdFindNames(response.data[0].id);
            this.activeTab = this.runLogData[0].id;
          } else {
            console.log("获取运行日志列表失败");
          }
        })
        .catch(() => {
          this.$message.error("获取运行日志列表失败");
        });
    },
    // 根据文件ID查询事件名称
    handleFileIdFindNames(id) {
      getFileIdFindNames(id)
        .then((response) => {
          if (response.data) {
            // console.log(response.data, '797------');
            this.fileInfo = response.data;
            // this.handleFileIdFindNames(tab._props.name)
          } else {
            console.log("获取事件名称列表失败");
          }
        })
        .catch(() => {
          this.$message.error("获取事件名称列表失败");
        });
    },
    handleTabClick(tab, event) {
      this.handleFileIdFindNames(tab._props.name);
    },
    // 终止运行
    handleStopRun(row) {
      this.$modal
        .confirm('确认要终止运行任务"' + row.taskName + '"吗？')
        .then(() => {
          stopFileImportTask(row.id).then(() => {
            this.$modal.msgSuccess("终止运行成功");
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 重跑
    handleRerun(row) {
      this.$modal
        .confirm('确认要重新运行任务"' + row.taskName + '"吗？')
        .then(() => {
          rerunFileImportTask(row.id).then(() => {
            this.$modal.msgSuccess("重跑成功");
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 根据选择的类别下载模板
    downloadTemplateByCategory() {
      if (!this.form.category) {
        this.$message.warning("请先选择模板类别");
        return;
      }

      const typeMap = {
        1: "event", // 事件信息
        2: "daily", // 舆情日报
        3: "special", // 舆情专报
        4: "research", // 理论研究成果
      };

      this.downloadTemplate(typeMap[this.form.category]);
    },

    // 下载模板
    downloadTemplate(type) {
      const categoryMap = {
        event: 1, // 事件信息
        daily: 2, // 舆情日报
        special: 3, // 舆情专报
        research: 4, // 理论研究成果
      };

      const category = categoryMap[type];
      let filename = "";
      if (category === 1) {
        filename = "事件信息模板";
      }
      if (category === 2) {
        filename = "舆情日报模板";
      }
      if (category === 3) {
        filename = "舆情专报模板";
      }
      if (category === 4) {
        filename = "理论研究成果模板";
      }
      if (category) {
        this.download(
          "dataaccess/fileimport/download/template",
          {
            category,
          },
          `${filename}_${new Date().getTime()}.docx`
        );
      }
    },
    // 获取类别文本
    getCategoryText(value) {
      const category = this.dict.type.file_import_category.find(
        (item) => item.value == value
      );
      return category ? category.label : "";
    },
    // 获取状态文本
    getStatusText(value) {
      const status = this.dict.type.file_import_runstatus.find(
        (item) => item.value == value
      );
      return status ? status.label : "";
    },
    // 获取文件图标类
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },
    // 获取文件类型
    getFileType(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      if (["xls", "xlsx"].includes(ext)) return "excel";
      if (["doc", "docx", "wps"].includes(ext)) return "word";
      if (["txt", "json", "csv"].includes(ext)) return "text";
      return "file";
    },
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toFixed(2) + " " + sizes[i];
    },
    // 下载文件
    handleDownload(file) {
      downloadFileImportFile(file.id)
        .then((response) => {
          const blob = new Blob([response]);
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = file.filename || file.name;
          link.click();
          window.URL.revokeObjectURL(url);
        })
        .catch(() => {
          this.$message.error("文件下载失败");
        });
    },
    // 删除文件
    handleDeleteFile(file) {
      this.$modal
        .confirm('是否确认删除文件"' + (file.filename || file.name) + '"？')
        .then(() => {
          // 标记文件为待删除
          if (!this.deleteFileIds) {
            this.deleteFileIds = [];
          }
          this.deleteFileIds.push(file.id);
          // 从文件列表中移除
          this.fileList = this.fileList.filter((f) => f.id !== file.id);
          this.$modal.msgSuccess("文件已标记为待删除");
        })
        .catch(() => {});
    },
    /** 查询文件导入任务列表 */
    getList() {
      this.loading = true;
      // 处理日期范围参数
      const params = { ...this.queryParams };
      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0];
        params.endTime = this.dateRange[1];
      }
      getFileImportList(params)
        .then((response) => {
          this.roleList = response.rows || [];
          this.total = response.total || 0;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        taskName: undefined,
        category: undefined,
        file: undefined,
      };
      this.deleteFileIds = [];
      this.originalFileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // console.log(selection, 'selection');
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    }, 
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "创建导入任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.deleteFileIds = [];
      const id = row.id || this.ids[0];
      if (id) {
        getFileImportDetail(id).then((response) => {
          if (response.data) {
            this.form = response.data.taskInfo || {};
            
            // 将 attachments 转换为文件列表格式
            const attachments = response.data.attachments || [];
            // 保存原始文件列表，用于后续对比删除的文件
            this.originalFileList = attachments.map(att => att.id);
            
            this.form.file = attachments.map(att => ({
              uid: `file-${att.id}`,
              id: att.id,
              name: att.filename,
              filename: att.filename,
              size: att.fileSize || 0,
              raw: null // 标记为已有文件，不是新上传的
            }));
            
            this.open = true;
            this.title = "修改文件导入任务";
            this.temDisabled = true
          }
        });
      } else {
        this.open = true;
        this.title = "修改文件导入任务";
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            // 编辑任务
            this.editTask();
          } else {
            // 新增任务
            this.addTask();
          }
        }
      });
    },
    // 新增任务
    addTask() {
      // 检查是否选择了类别
      if (!this.form.category) {
        this.$message.warning("请选择类别");
        return;
      }
      // 检查是否上传了文件
      if (!this.form.file || this.form.file.length === 0) {
        this.$message.warning("请上传文件");
        return;
      }

      // 构建FormData
      const formData = new FormData();
      formData.append("taskName", this.form.taskName);
      formData.append("category", this.form.category);

      // 添加文件
      this.form.file.forEach((file) => {
        formData.append("files", file.raw || file);
      });

      uploadFileImport(formData)
        .then((response) => {
          this.$modal.msgSuccess("任务创建成功");
          this.open = false;
          this.getList();
        })
        .catch(() => {
          this.$modal.msgError("任务创建失败");
        });
    },
    // 编辑任务
    editTask() {
      // 构建FormData
      const formData = new FormData();
      formData.append("taskId", this.form.id);
      formData.append("taskName", this.form.taskName);

      // 计算需要删除的文件ID（原始文件列表 - 当前文件列表）
      const currentFileIds = (this.form.file || [])
        .map(f => f.id)
        .filter(id => id); // 过滤掉新上传的文件（没有id）
      
      const deleteFileIds = this.originalFileList.filter(
        id => !currentFileIds.includes(id)
      );
      
      // 添加删除的文件ID
      deleteFileIds.forEach((fileId) => {
        formData.append("deleteFileIds", fileId);
      });

      // 添加新上传的文件（只有有raw属性的文件才是新上传的）
      if (this.form.file && this.form.file.length > 0) {
        this.form.file.forEach((file) => {
          if (file.raw) {
            formData.append("newFiles", file.raw);
          }
        });
      }

      editFileImport(formData)
        .then((response) => {
          this.$modal.msgSuccess("任务编辑成功");
          this.open = false;
          this.getList();
        })
        .catch(() => {
          this.$modal.msgError("任务编辑失败");
        });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除任务编号为"' + ids + '"的数据项？')
        .then(function () {
          return deleteFileImport(Array.isArray(ids) ? ids.join(",") : ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "dataaccess/fileimport/export",
        {
          ids: this.ids.join(','),
        },
        `文件导入任务_${new Date().getTime()}.xlsx`
      );
    },
    /** 导出按钮操作 */
    // handleExport() {
    //   // 构建导出参数
    //   const params = { ...this.queryParams };
    //   if (this.dateRange && this.dateRange.length === 2) {
    //     params.startTime = this.dateRange[0];
    //     params.endTime = this.dateRange[1];
    //   }

    //   exportFileImport(params)
    //     .then((response) => {
    //       const blob = new Blob([response]);
    //       const url = window.URL.createObjectURL(blob);
    //       const link = document.createElement("a");
    //       link.href = url;
    //       link.download = `文件导入任务_${new Date().getTime()}.xlsx`;
    //       link.click();
    //       window.URL.revokeObjectURL(url);
    //       this.$modal.msgSuccess("导出成功");
    //     })
    //     .catch(() => {
    //       this.$modal.msgError("导出失败");
    //     });
    // },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.history_log {
  font-size: 14px;
    color: #606266;
    font-weight: 600;
}
.dialogTitle {
  position: absolute;
  top: 35px;
}
.dialogPopover {
  position: absolute;
  top: 15px;
  right: 50px;
}
.dialogText {
  font-size: 14px;
  font-weight: 600;
  color: #1f2f3d;
}

.secondTitle {
  font-size: 12px;
  font-weight: 400;
  color: #1f2f3d59;
}

.notice-container {
  width: 100%;
  margin: 20px auto;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-family: "Microsoft Yahei", sans-serif;
}

.notice-header {
  background-color: #d1e7ff;
  padding: 12px 20px;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.notice-content {
  padding: 20px;
  background-color: #fff;
}

.notice-content h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 12px;
  font-weight: 600;
}

.notice-content ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-bottom: 20px;
}

.notice-content ul li {
  margin-bottom: 10px;
  line-height: 1.6;
  color: #555;
}

.notice-content p {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #555;
}
.my-label {
  background: #e1f3d8;
}

.my-content {
  background: #fde2e2;
}
// 模板下载样式
.template-download-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 10px;
}

.template-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
  transition: all 0.3s ease;
  width: 280px;
  margin: 0;
}

.template-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  margin-top: 10px;
}

.template-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.template-desc {
  font-size: 10px;
  color: #606266;
  margin: 8px 0;
  line-height: 1.5;
}

.template-info {
  font-size: 12px;
  color: #909399;
  margin: 0;
}

// 文件列表样式
.file-list {
  margin-top: 20px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: #fff;
  transition: all 0.3s ease;
}

.file-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.file-icon-excel {
  background-color: #f0f9ff;
  color: #1d4ed8;
}

.file-icon-word {
  background-color: #f0f9ff;
  color: #1d4ed8;
}

.file-icon-text {
  background-color: #f0fdf4;
  color: #16a34a;
}

.file-icon-file {
  background-color: #f8fafc;
  color: #64748b;
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #909399;
  margin: 0;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.download-btn {
  background-color: #f0f9ff;
  color: #1d4ed8;
}

.download-btn:hover {
  background-color: #dbeafe;
}

.delete-btn {
  background-color: #fef2f2;
  color: #dc2626;
}

.delete-btn:hover {
  background-color: #fee2e2;
}

.action-btn svg {
  width: 16px;
  height: 16px;
}

// 运行日志el-drawer 的样式  start

.fancy-drawer {
  background-color: #f9fafc;
}
.stats-container {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  flex-wrap: wrap;
  margin-top: 10px;
  padding: 0 12px;
}
.stat-card {
  // width: 200px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}
.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.stat-icon {
  font-size: 24px;
  margin-bottom: 10px;
  text-align: center;
  img {
    width: 24px;
    height: 24px;
  }
}
.stat-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
  text-align: center;
}
.stat-label {
  font-size: 14px;
  text-align: center;
}
.all-card {
  // border: 1px solid #67c23a;
  color: #ffbf00;
}
.success-card {
  // border: 1px solid #67c23a;
  color: #3586ff;
}
.fail-card {
  // border: 1px solid #f56c6c;
  color: #f56c6c;
}
.fail-records-title {
  font-size: 16px;
  // font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  padding: 0 20px;
}
.tabs-container {
  background-color: #f8f9fb;
  padding: 10px;
  border-radius: 4px;
  padding: 0 20px;
}
.fail-data-item {
  margin-bottom: 10px;
  // padding: 10px;
  background-color: #f9fafc;
  border-radius: 4px;
  height: 65vh;
  overflow-y: overlay;
  .text {
    font-size: 14px;
  }

  .item {
    padding: 18px 0;
  }
}
.data-id {
  // font-weight: bold;
  font-size: 14px;
  margin-bottom: 5px;
}
.data-title {
  font-size: 14px;
  color: #666;
}
// 运行日志el-drawer 的样式  end
</style>