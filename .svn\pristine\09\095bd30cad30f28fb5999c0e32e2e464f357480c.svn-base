import request from '@/utils/request'

// 数据报告查询报告列表
export function Reportlist(data) {
  return request({
    url: '/dataaccess/topicReport/list',
    method: 'get',
    params: data
  })
}

// 数据报告分析内容下拉框
export function getAnalysisTaskList(data) {
  return request({
    url: '/dataaccess/topicReport/getAnalysisTaskList',
    method: 'get',
    params: data
  })
}

// 数据报告模板下拉框
export function getAllTemplate(data) {
  return request({
    url: '/dataaccess/topicTemplate/getAllTemplate',
    method: 'get',
    params: data
  })
}

// 数据报告获取模板列表
export function TemplateList(data) {
  return request({
    url: '/dataaccess/topicTemplate/list',
    method: 'get',
    params: data
  })
}

// 数据报告添加报告列表
export function topicReporttj(data) {
  return request({
    url: '/dataaccess/topicReport',
    method: 'post',
    data: data
  })
}

// 数据报告修改报告列表
export function topicReportxg(data) {
  return request({
    url: '/dataaccess/topicReport',
    method: 'put',
    data: data
  })
}

// 数据报告删除报告列表
export function topicReportsc(id) {
  return request({
    url: `/dataaccess/topicReport/${id}`, // 用反引号拼接路径参数
    method: 'delete'
    // 无需params，路径参数直接在url中拼接
  })
}

//数据报告模板管理删除
export function topicTemplate(id) {
  return request({
    url: `/dataaccess/topicTemplate/${id}`, 
    method: 'delete'
  })
}