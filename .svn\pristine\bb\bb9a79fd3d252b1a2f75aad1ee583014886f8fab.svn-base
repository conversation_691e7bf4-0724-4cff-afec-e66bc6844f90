import request from '@/utils/request'

// 开源引接任务列表查询
export function getOpenSourceIntegrationList(query) {
  return request({
    url: '/dataaccess/openSourceIntegration/list',
    method: 'get',
    params: query
  })
}

// 开源引接任务新增
export function addOpenSourceIntegration(data) {
  return request({
    url: '/dataaccess/openSourceIntegration/file',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 开源引接任务编辑
export function updateOpenSourceIntegration(data) {
  return request({
    url: '/dataaccess/openSourceIntegration',
    method: 'put',
    data: data
  })
}

// 开源引接任务删除（单条和批量删除共用）
export function deleteOpenSourceIntegration(ids) {
  return request({
    url: '/dataaccess/openSourceIntegration/' + ids,
    method: 'delete'
  })
}

// 开源引接任务导出
export function exportOpenSourceIntegration(query) {
  return request({
    url: '/dataaccess/openSourceIntegration/export',
    method: 'post',
    data: query
  })
}

// 上传开源引接文件
export function uploadOpenSourceIntegrationFile(data) {
  return request({
    url: '/dataaccess/openSourceIntegration/file',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 通过任务id查询任务下文件
export function getOpenSourceIntegrationFileList(taskId) {
  return request({
    url: '/dataaccess/openSourceIntegration/file/list',
    method: 'get',
    params: { taskId }
  })
}

// 通过开源引接id删除文件
export function deleteOpenSourceIntegrationFile(id) {
  return request({
    url: '/dataaccess/openSourceIntegration/file/' + id,
    method: 'delete'
  })
}

// 开源引接记录
export function getDataCountsInfo(query) {
  return request({
    url: '/dataaccess/openSourceIntegration/file/count',
    method: 'get',
    params: query
  })
}

// 重新运行任务
export function rerunFileImportTask(taskId) {
  return request({
    url: `/dataaccess/openSourceIntegration/file/retry`,
    method: 'get',
    params: { taskId }
  })
}


// 终止运行任务
export function stopFileImportTask(taskId) {
  return request({
    url: `/dataaccess/openSourceIntegration/file/stop`,
    method: 'get',
    params: { taskId }
  })
}
// 获取任务运行日志
export function getDialogRunLogCounts(taskId) {
  return request({
    url: '/dataaccess/openSourceIntegration/statisticsByTaskAsMap',
    method: 'get',
    params: { taskId }
  })
}


// 获取任务运行日志列表
export function getDialogRunLog(query) {
  return request({
    url: '/dataaccess/openSourceIntegration/getFilesByStatus',
    method: 'get',
    params: query
  })
}
// 根据文件ID查询事件名称
export function getFileIdFindNames(query) {
  return request({
    url: '/dataaccess/openSourceIntegration/getContentFileId',
    method: 'get',
    params: query
  })
}

// 获取文件导入任务详情
export function getFileImportDetail(id) {
  return request({
    url: '/dataaccess/openSourceIntegration/'  + id,
    method: 'get'
  })
}

// 编辑任务
export function editFileImport(formData) {
  return request({
    url: '/dataaccess/openSourceIntegration/update',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 下载任务文件
export function downloadFileImportFile(id) {
  return request({
    url: `/dataaccess/openSourceIntegration/file/download/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}