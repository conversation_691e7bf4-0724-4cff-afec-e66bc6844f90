<template>
  <!-- 开源引接界面 -->
  <div class="app-container">
    <p class="history_log">开源引接记录：共{{historyTotalCount}}条数据</p>
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="执行状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="执行状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_execution_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery"
          >应用筛选</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:role:add']"
          >创建任务</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-upload2"
          size="mini"
          :disabled="multiple"
          @click="handleExport"
          v-hasPermi="['data:datain:batchExport']"
          >批量导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['data:datain:batchDelete']"
          >批量删除</el-button
        >
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="roleList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务名称" prop="taskName" sortable />
      <el-table-column prop="status" label="执行状态">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_execution_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="createByName" :show-overflow-tooltip="true" />
      <el-table-column
        label="创建时间"
        align="center"
        sortable
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status != 1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['data:datain:update']"
            >编辑</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetails(scope.row)"
            v-hasPermi="['data:datain:view']"
            >查看</el-button
          >
          <el-button
            v-if="scope.row.status === 2"
            size="mini"
            type="text"
            icon="el-icon-circle-close"
            @click="handleStopRun(scope.row)"
            v-hasPermi="['data:datain:stopRun']"
            >终止运行</el-button
          >
          <el-button
            v-if="scope.row.status === 3"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleRerun(scope.row)"
            v-hasPermi="['data:datain:rerun']"
            >重跑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-tickets"
            @click="handleRunLog(scope.row)"
            v-hasPermi="['data:datain:runLog']"
            >运行日志</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['data:datain:batchDelete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <p class="dialogTitle">引接本地化部署的开源信息监测软件系统的数据文件</p>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <p class="dialogText">第一步：创建任务</p>
        <el-form-item label="任务名称" prop="taskName">
          <el-input v-model="form.taskName" placeholder="请输入任务名称" />
        </el-form-item>
        <p class="dialogText">第二步：导入配置</p>
        <el-form-item label="数据过滤规则">
          <el-input
            v-model="form.filterRules"
            type="textarea"
            placeholder="默认导入全部数据，可设置过滤条件..."
          ></el-input>
        </el-form-item>
        <el-form-item label="重复数据处理策略">
          <el-select class="width100" v-model="form.handlingStrategies" placeholder="请选择策略">
            <el-option
              v-for="dict in dict.type.sys_duplicate_data_strategy"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <p class="dialogText">第三步：上传文件</p>
        <p class="secondTitle">
          请上传开源信息监测软件系统的数据文件，支持一次批量导入多个文件
        </p>
        <el-form-item label-width="0" label="" prop="files">
          <commonUpload
            v-model="form.files"
            :file-size="1000"
            :file-type="['json','zip']"
            upload-url="/api/upload"
          />
        </el-form-item>
      </el-form>

      <el-popover placement="left" width="400" class="dialogPopover" trigger="hover">
        <div class="notice-container">
          <div class="notice-header">引接须知</div>
          <div class="notice-content">
            <h3>操作知悉</h3>
            <ul>
              <li>
                全量数据导入可能需要较长时间，请耐心等待。系统会在后台执行导入任务，您可以关闭页面或继续其他操作。
              </li>
              <li>
                导入过程中请勿重复提交相同的导入任务，以免造成数据重复或系统负载过高。
              </li>
              <li>
                如导入失败，请查看运行日志或联系技术支持获取帮助。重要数据建议先备份再执行导入操作。
              </li>
            </ul>
            <h3>常见问题</h3>
            <p>Q：导入失败如何处理？</p>
            <p>A：查看运行日志或联系技术支持人员进行处理。</p>
          </div>
        </div>
        <el-tag slot="reference" type="success">引接须知</el-tag>
        <!-- <el-button slot="reference">hover 激活</el-button> -->
      </el-popover>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">导入并解析</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情弹框 -->
    <el-dialog
      title="任务详细信息"
      :visible.sync="openDetailDialog"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item
          label="任务名称"
          label-class-name="my-label"
          content-class-name="my-content"
          >{{ currentTask.taskName || 'xxxxxxxxxxxxxxxxxxx' }}</el-descriptions-item
        >
        <el-descriptions-item label="数据过滤规则"
          >{{ currentTask.filterRules || '数据过滤规则数据过滤规则数据过滤规则数据过滤规则' }}</el-descriptions-item
        >
        <el-descriptions-item label="重复数据处理策略"
          >{{ getHandlingStrategyText(currentTask.handlingStrategies) || '重复数据处理策略重复数据处理策略重复数据处理策略' }}</el-descriptions-item
        >
        <el-descriptions-item label="上传文件"></el-descriptions-item>
      </el-descriptions>
      <!-- 文件列表 可下载 -->
      <transition-group
        name="file-list"
        tag="div"
        class="file-list"
        v-if="fileList.length > 0"
      >
        <div v-for="file in fileList" :key="file.uid" class="file-item">
          <div class="file-info">
            <div class="file-icon" :class="getFileIconClass(file.name)">
              <svg
                v-if="getFileType(file.name) === 'excel'"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M10 12L14 16M14 12L10 16"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <svg
                v-else-if="getFileType(file.name) === 'zip'"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12 11V17M10 13H14M10 15H14"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <svg
                v-else
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>

            <div class="file-details">
              <p class="file-name" :title="file.filename || file.name">{{ file.filename || file.name }}</p>
              <p class="file-size">{{ formatFileSize(file.fileSize || file.size) }}</p>
            </div>
          </div>

          <div class="file-actions">
            <button
              type="button"
              class="action-btn download-btn"
              @click="handleDownload(file)"
              title="下载"
            >
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M7 10L12 15L17 10"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12 15V3"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
            <button
              type="button"
              class="action-btn delete-btn"
              @click="handleDeleteFile(file)"
              title="删除"
            >
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M3 6H5H21"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M10 11V17"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 11V17"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </transition-group>
    </el-dialog>
    <!-- 运行日志 -->
    <el-drawer
      title="运行日志"
      :visible.sync="drawer"
      :direction="direction"
      size="20%"
      custom-class="fancy-drawer"
    >
      <div class="stats-container">
        <el-card shadow="hover" class="stat-card success-card">
          <div @click="handleViewFailRecords(2)">
            <div class="stat-icon">
              <!-- <i class="el-icon-menu"></i> -->
              <img src="../imgs_erb/icon_counts.png" alt="" srcset="" />
            </div>
            <div class="stat-number">{{ totalCount }}</div>
            <div class="stat-label">总数据条数</div>
          </div>
        </el-card>
        <el-card shadow="hover" class="stat-card success-card">
          <div @click="handleViewFailRecords(1)">
            <div class="stat-icon">
              <img src="../imgs_erb/icon_success_counts.png" alt="" srcset="" />
            </div>
            <div class="stat-number">{{ successCount }}</div>
            <div class="stat-label">成功条数</div>
          </div>
        </el-card>
        <el-card shadow="hover" class="stat-card fail-card">
          <div @click="handleViewFailRecords(0)">
            <div class="stat-icon">
              <img src="../imgs_erb/icon_error_counts.png" alt="" srcset="" />
            </div>
            <div class="stat-number">{{ failCount }}</div>
            <div class="stat-label">失败条数</div>
          </div>
        </el-card>
      </div>
      <div class="fail-records-title">{{logFontTitle}}</div>
      <el-tabs
        v-model="activeTab"
        class="tabs-container"
        @tab-click="handleTabClick"
      >
        <el-tab-pane
          v-for="item in runLogData"
          :key="item.id"
          :label="`${item.filename} (${item.resultCount || 0}条)`"
          :name="item.id"
        >
          <div class="fail-data-item">
            <el-card class="box-card" v-if="fileInfo && fileInfo.length > 0">
              <div
                v-for="(item, index) in fileInfo"
                :key="item.id"
                class="text item"
              >
                {{ index + 1 }}. {{ item.eventName }}
              </div>
            </el-card>
            <el-empty v-else description="暂无记录"></el-empty>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script>
import { 
  getOpenSourceIntegrationList, 
  deleteOpenSourceIntegration,
  addOpenSourceIntegration,
  updateOpenSourceIntegration,
  uploadOpenSourceIntegrationFile,
  getOpenSourceIntegrationFileList,
  downloadOpenSourceIntegrationFile,
  deleteOpenSourceIntegrationFile,
  getDataCountsInfo,
  rerunFileImportTask,
  stopFileImportTask,
  getDialogRunLogCounts,
  getDialogRunLog,
  getFileIdFindNames,
  getFileImportDetail,
  editFileImport
} from "@/api/data/openSourceIntegration";

import commonUpload from "@/views/components/commonUpload.vue";

export default {
  name: "Role",
  dicts: ["sys_duplicate_data_strategy", "sys_execution_status"],
  components: { commonUpload },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openDetailDialog: false,
      // 右侧弹出层
      drawer: false,
      direction: "rtl",
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: undefined,
        handlingStrategies: undefined,
        kssj: undefined,
        jssj: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        taskName: [{ required: true, message: "任务名称不能为空", trigger: "blur" }]
      },
      fileList: [
        {
          uid: "file-1760678872252-0",
          name: "工作.txt",
          size: 10817,
          raw: {},
          url: "blob:http://localhost/34b5fed2-4632-457b-8ace-a16dda188f36",
        },
        {
          uid: "file-1760678883922-1",
          name: "附件1.xlsx",
          size: 63495,
          raw: {},
          url: "blob:http://localhost/d9e1bbc5-f510-441b-b453-9e81900175ae",
        },
      ],
      activeTab: "file1",
      file1Data: [{ id: "ID1001" }, { id: "ID1002" }],
      file2Data: [{ id: "ID2001" }],
      file3Data: [{ id: "ID3001" }, { id: "ID3002" }, { id: "ID3003" }],
      // 当前查看的任务详情
      currentTask: {},
      currentTaskId: "",
      totalCount: 0,
      successCount: 0,
      failCount: 0,
      runLogData: [],
      historyTotalCount: 0,
      logFontTitle: '',
      fileInfo: [],
      // 删除的文件ID列表
      deleteFileIds: [],
      logStatus: null,
      // 原始文件列表ID（用于对比删除的文件）
      originalFileList: [],
    };
  },
  created() {
    this.getList();
    this.getDataCounts()
  },
  methods: {
    // 重跑
    handleRerun(row) {
      this.$modal
        .confirm('确认要重新运行任务"' + row.taskName + '"吗？')
        .then(() => {
          rerunFileImportTask(row.id).then(() => {
            this.$modal.msgSuccess("重跑成功");
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 终止运行
    handleStopRun(row) {
      this.$modal
        .confirm('确认要终止运行任务"' + row.taskName + '"吗？')
        .then(() => {
          stopFileImportTask(row.id).then(() => {
            this.$modal.msgSuccess("终止运行成功");
            this.getList();
          });
        })
        .catch(() => {});
    },
    // 开源引接记录
    getDataCounts() {
      getDataCountsInfo()
        .then((response) => {
          if (response.data) {
            this.historyTotalCount = response.data
          } else {
            console.log("获取历史导入记录失败");
          }
        })
        .catch(() => {
          this.$message.error("获取任务详情失败");
        });
    },
    // 查看详情
    handleViewDetails(row) {
      this.currentTask = row;
      this.openDetailDialog = true;
      // 查询任务下的文件列表
      this.getTaskFileList(row.id);
    },
    // 运行日志
    handleRunLog(row) {
      // this.drawer = true;
      this.currentTaskId = row.id;
      // 查询任务详情
      getDialogRunLogCounts(row.id)
        .then((response) => {
          this.drawer = true;
          if (response.data) {
            this.totalCount = response.data.allCount;
            this.successCount = response.data.totalSuccess;
            this.failCount = response.data.totalFail;
            this.handleViewFailRecords(2);
            this.logStatus = 2;
          } else {
            console.log("获取运行日志失败");
          }
        })
        .catch(() => {
          this.$message.error("获取运行日志失败");
        });
    },
    // 根据status和taskid获取查看运行日志
    handleViewFailRecords(status) {
      // console.log(status, 'status')
      this.logStatus = status
      if(status === 2) {
      this.logFontTitle = '总数据记录'
      }else if(status === 1) {
        this.logFontTitle = '成功记录'
      }else {
        this.logFontTitle = '失败记录'
      }
      let params = {
        status: status,
        taskId: this.currentTaskId,
      };
      getDialogRunLog(params)
        .then((response) => {
          if (response.data) {
            this.runLogData = response.data;
            this.handleFileIdFindNames(response.data[0].id, status);
            this.activeTab = this.runLogData[0].id;
          } else {
            console.log("获取运行日志列表失败");
          }
        })
        .catch(() => {
          this.$message.error("获取运行日志列表失败");
        });
    },
    // 根据文件ID查询事件名称
    handleFileIdFindNames(id, status) {
      let params = {
        status,
        id
      };
      getFileIdFindNames(params)
        .then((response) => {
          if (response.data) {
            this.fileInfo = response.data;
          } else {
            console.log("获取事件名称列表失败");
          }
        })
        .catch(() => {
          this.$message.error("获取事件名称列表失败");
        });
    },
    handleTabClick(tab, event) {
      // console.log(tab._props, '728---')
      this.handleFileIdFindNames(tab._props.name, this.logStatus);
    },
    /** 查询开源引接任务列表 */
    getList() {
      this.loading = true;
      // 处理日期范围参数
      const params = { ...this.queryParams };
      if (this.dateRange && this.dateRange.length === 2) {
        params.kssj = this.dateRange[0];
        params.jssj = this.dateRange[1];
      }
      
      getOpenSourceIntegrationList(params).then((response) => {
        this.roleList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      (this.menuExpand = false),
        (this.menuNodeAll = false),
        (this.deptExpand = true),
        (this.deptNodeAll = false),
        (this.form = {
          id: undefined,
          taskName: undefined,
          filterRules: undefined,
          handlingStrategies: undefined,
          status: undefined,
          // successRecords: 0,
          // failedRecords: 0,
          files: undefined
        });
      this.deleteFileIds = [];
      this.originalFileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "创建开源引接任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.deleteFileIds = [];
      const id = row.id || this.ids[0];
      if (id) {
        getFileImportDetail(id).then((response) => {
          if (response.data) {
            this.form = response.data.taskInfo || {};
            
            // 将 attachments 转换为文件列表格式
            const attachments = response.data.attachments || [];
            // 保存原始文件列表，用于后续对比删除的文件
            this.originalFileList = attachments.map(att => att.id);
            
            this.form.files = attachments.map(att => ({
              uid: `file-${att.id}`,
              id: att.id,
              name: att.filename,
              filename: att.filename,
              size: att.fileSize || 0,
              raw: null // 标记为已有文件，不是新上传的
            }));
            
            this.open = true;
            this.title = "修改文件导入任务";
            this.temDisabled = true
          }
        });
      } else {
        this.open = true;
        this.title = "修改文件导入任务";
      }
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != undefined) {
            // 修改任务
            // updateOpenSourceIntegration(this.form).then((response) => {
            //   this.$modal.msgSuccess("修改成功");
            //   this.open = false;
            //   this.getList();
            // });
            // 编辑任务
            this.editTask();
          } else {
             // 新增任务
            this.addTask();
          }
        }
      });
    },

        // 新增任务
    addTask() {
      // 检查是否选择了类别
      // if (!this.form.category) {
      //   this.$message.warning("请选择类别");
      //   return;
      // }
      // 检查是否上传了文件
      if (!this.form.files || this.form.files.length === 0) {
        this.$message.warning("请上传文件");
        return;
      }
      // 构建FormData
      const formData = new FormData();
      formData.append("taskName", this.form.taskName);
      formData.append("filterRules", this.form.filterRules);
      formData.append("handlingStrategies", this.form.handlingStrategies);
      // 添加文件
      this.form.files.forEach((file) => {
        formData.append("files", file.raw || file);
      });
      addOpenSourceIntegration(formData)
        .then((response) => {
          this.$modal.msgSuccess(response.message);
          this.open = false;
          this.getList();
        })
        .catch(() => {
          this.$modal.msgError(response.message);
        });
    },
    // 编辑任务
    editTask() {
      // 构建FormData
      const formData = new FormData();
      formData.append("id", this.form.id);
      formData.append("taskName", this.form.taskName);
      formData.append("filterRules", this.form.filterRules);
      formData.append("handlingStrategies", this.form.handlingStrategies);

      // 计算需要删除的文件ID（原始文件列表 - 当前文件列表）
      const currentFileIds = (this.form.files || [])
        .map(f => f.id)
        .filter(id => id); // 过滤掉新上传的文件（没有id）
      
      const deleteFileIds = this.originalFileList.filter(
        id => !currentFileIds.includes(id)
      );
      
      // 添加删除的文件ID
      deleteFileIds.forEach((fileId) => {
        formData.append("deleteFileIds", fileId);
      });

      // 添加新上传的文件（只有有raw属性的文件才是新上传的）
      if (this.form.files && this.form.files.length > 0) {
        this.form.files.forEach((file) => {
          if (file.raw) {
            formData.append("files", file.raw);
          }
        });
      }

      editFileImport(formData)
        .then((response) => {
          this.$modal.msgSuccess("任务编辑成功");
          this.open = false;
          this.getList();
        })
        .catch(() => {
          this.$modal.msgError("任务编辑失败");
        });
    },



    
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },
    getFileType(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      if (["xls", "xlsx"].includes(ext)) return "excel";
      if (["zip", "rar", "7z"].includes(ext)) return "zip";
      if (["txt", "json", "csv"].includes(ext)) return "text";
      return "file";
    },
    formatFileSize(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toFixed(2) + " " + sizes[i];
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除任务编号为"' + ids + '"的数据项？')
        .then(function () {
          return deleteOpenSourceIntegration(Array.isArray(ids) ? ids.join(",") : ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "dataaccess/openSourceIntegration/export",
        {
          ids: this.ids.join(','),
        },
        `开源引接任务_${new Date().getTime()}.xlsx`
      );
    },
    handleDownload(file) {
      // 使用新的下载API
      downloadOpenSourceIntegrationFile(file.id).then((response) => {
        const blob = new Blob([response]);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = file.filename || file.name;
        link.click();
        window.URL.revokeObjectURL(url);
      }).catch(() => {
        this.$message.error("文件下载失败");
      });
    },
    // 获取处理策略文本
    getHandlingStrategyText(value) {
      const strategy = this.dict.type.sys_duplicate_data_strategy.find(item => item.value === value);
      return strategy ? strategy.label : '';
    },
    // 获取任务文件列表
    getTaskFileList(taskId) {
      getOpenSourceIntegrationFileList(taskId).then((response) => {
        this.fileList = response.data || [];
      }).catch(() => {
        this.fileList = [];
      });
    },
    // 删除文件
    handleDeleteFile(file) {
      this.$modal.confirm('是否确认删除文件"' + (file.filename || file.name) + '"？').then(() => {
        return deleteOpenSourceIntegrationFile(file.id);
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        // 重新加载文件列表
        this.getTaskFileList(this.currentTask.id);
      }).catch(() => {});
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.history_log {
  font-size: 14px;
    color: #606266;
    font-weight: 600;
}
.fail-data-item {
  margin-bottom: 10px;
  // padding: 10px;
  background-color: #f9fafc;
  border-radius: 4px;
  height: 65vh;
  overflow-y: overlay;
  .text {
    font-size: 14px;
  }

  .item {
    padding: 18px 0;
  }
}
</style>
