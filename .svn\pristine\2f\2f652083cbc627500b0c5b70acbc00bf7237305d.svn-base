import request from '@/utils/request'

// 资产构建获取所有主题
export function selectListBySystemType(data) {
  return request({
    url: '/datamanger/asset/construction/selectListBySystemType',
    method: 'get',
    params: data
  })
}

// 资产构建根据主题查找专题
export function selectThematicListById(data) {
  return request({
    url: '/datamanger/asset/construction/selectThematicListById',
    method: 'get',
    params: data
  })
}

// 资产构建列表查询
export function selectDataByThenaticId(data) {
  return request({
    url: '/datamanger/asset/construction/selectDataByThenaticId',
    method: 'get',
    params: data
  })
}

// 根据专题获取个数
export function selectThematicCountByRuleType(data) {
  return request({
    url: 'datamanger/asset/construction/selectThematicCountByRuleType',
    method: 'get',
    params: data
  })
}

// 资产构建专题数据汇聚配置字段下拉框
export function fields(data) {
  return request({
    url: '/datamanger/asset/construction/fields',
    method: 'get',
    params: data
  })
}

// 专题数据汇聚配置操作符下拉框
export function operators(data) {
  return request({
    url: '/datamanger/asset/construction/operators',
    method: 'get',
    params: data
  })
}

// 获取执行频率（每天/每周）
export function data_manage_frequency(data) {
  return request({
    url: '/system/dict/data/type/data_manage_frequency',
    method: 'get',
    params: data    
  })
}

// 获取执行频率（星期几）
export function data_manage_dayofweek(data) {
  return request({
    url: '/system/dict/data/type/data_manage_dayofweek',
    method: 'get',
    params: data    
  })
}

// 获取执行频率（0-24时）
export function data_manage_executehour(data) {
  return request({
    url: '/system/dict/data/type/data_manage_executehour',
    method: 'get',
    params: data    
  })
}

// 保存规则
export function saveRules(data) {
  return request({
    url: '/datamanger/asset/construction/saveRules',
    method: 'post',
    data: data
  })
}

// 返显已配置规则
export function getRuleByThematic(data) {
  return request({
    url: '/datamanger/asset/construction/getRuleByThematic',
    method: 'get',
    params: data
  })
}

// 修改规则
export function updateRules(data) {
  return request({
    url: '/datamanger/asset/construction/updateRules',
    method: 'post',
    data: data
  })
}

// 弹窗查询
export function execute(data) {
  return request({
    url: '/datamanger/asset/construction/execute',
    method: 'post',
    data: data
  })
}

// 根据专题获取类别
export function selectTabData(data) {
  return request({
    url: 'datamanger/asset/construction/selectTabData',
    method: 'get',
    params: data
  })
}
