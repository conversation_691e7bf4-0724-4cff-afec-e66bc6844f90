<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes
        :horizontal="this.$store.getters.device === 'mobile'"
        class="default-theme"
      >
        <!--部门数据-->
        <pane size="84">
          <evaluation-form
            title="员工绩效评价表"
            :evaluation-sections="evaluationSections"
            :grades="grades"
            :assessmentType="assessmentType"
            :deptOptions="deptOptions"
            :evaluate="evaluate"
            @submit="handleSubmit"
          />
        </pane>
      </splitpanes>
    </el-row>
  </div>
</template>

<script>
import { deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import EvaluationForm from "@/components/EvaluationForm";

export default {
  name: "PerformanceEvaluation",
  components: { Treeselect, Splitpanes, Pane, EvaluationForm },
  data() {
    return {
      // 部门名称
      deptName: "",
      // 所有部门树选项
      deptOptions: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 是否已评价
      evaluate: {
        totality: 7, //总数
        rated: 5, //已评价
        unrated: 2, //未评价
      },
      // 考核类型
      assessmentType: 1,
      evaluationSections: [
        {
          label: "德的评分",
          tooltip: "主要考察政治品质、职业道德、工作作风等方面表现",
        },
        {
          label: "能的评分",
          tooltip: "主要考察专业知识、业务能力、创新能力等方面表现",
        },
        {
          label: "勤的评分",
          tooltip: "主要考察工作态度、出勤情况、责任心等方面表现",
        },
        {
          label: "绩的评分",
          tooltip: "主要考察工作效率、完成质量、实际贡献等方面表现",
        },
        {
          label: "廉的评分",
          tooltip: "主要考察廉洁自律、遵纪守法等方面表现",
        },
      ],
      grades: [
        { value: "优秀", label: "优秀", min: 90, max: 100 },
        { value: "称职", label: "称职", min: 70, max: 89 },
        { value: "基本称职", label: "基本称职", min: 60, max: 69 },
        { value: "不称职", label: "不称职", min: 0, max: 59 },
      ],
      deptOptions: [
        {
          label: "黑龙江省自然资源厅",
          sfpj: 0,
          children: [
            {
              label: "张老师 （黑龙江省地址矿产局）",
              name: "张老师",
              department: "黑龙江省地址矿产局",
              position: "局长",
              sfpj: 1,
            },
            {
              label: "王老师 （黑龙江省国土空间规划研究院）",
              name: "王老师",
              department: "黑龙江省国土空间规划研究院",
              position: "院长",
              sfpj: 1,
            },
            {
              label: "李老师 （黑龙江省自然资源权益调查监测院）",
              name: "李老师",
              department: "黑龙江省自然资源权益调查监测院",
              position: "院长",
              sfpj: 1,
            },
            {
              label: "张老师 （黑龙江省自然资源生态保护修复监测中心）",
              name: "张老师",
              department: "黑龙江省自然资源生态保护修复监测中心",
              position: "主任",
              sfpj: 1,
            },
            {
              label: "王老师 （黑龙江省自然资源和不动产登记中心）",
              name: "王老师",
              department: "黑龙江省自然资源和不动产登记中心",
              position: "主任",
              sfpj: 1,
            },
            {
              label: "李老师 （黑龙江省自然资源技术保障中心）",
              name: "李老师",
              department: "黑龙江省自然资源技术保障中心",
              position: "主任",
              sfpj: 1,
            },
            {
              label: "张老师 （黑龙江省地质环境监测总站）",
              name: "张老师",
              department: "黑龙江省地质环境监测总站",
              position: "站长",
              sfpj: 2,
            },
            {
              label: "王老师 （黑龙江省地质资料档案馆）",
              name: "王老师",
              department: "黑龙江省地质资料档案馆",
              position: "馆长",
              sfpj: 2,
            },
            {
              label: "张老师 （调查处）",
              name: "张老师",
              department: "调查处",
              position: "处长",
              sfpj: 1,
            },
          ],
        },
      ],
    };
  },
  created() {
    // this.getDeptTree();
  },
  methods: {
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      console.log(data, "节点单击事件");

      // this.queryParams.deptId = data.id;
      // this.handleQuery();
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then((response) => {
        this.deptOptions = response.data;
        this.enabledDeptOptions = this.filterDisabledDept(
          JSON.parse(JSON.stringify(response.data))
        );
      });
    },
    handleSubmit(formData) {
      // 模拟API调用
      setTimeout(() => {
        this.$message({
          dangerouslyUseHTMLString: true, // 确保这个选项设置为true，以允许使用HTML字符串
          message:
            formData.department +
            "-" +
            formData.position +
            "-" +
            formData.name +
            "<br>德的评分是" +
            ":" +
            formData.sections[0].rating +
            "<br>能的评分是" +
            ":" +
            formData.sections[1].rating +
            "<br>勤的评分是" +
            ":" +
            formData.sections[2].rating +
            "<br>绩的评分是" +
            ":" +
            formData.sections[3].rating +
            "<br>廉的评分是" +
            ":" +
            formData.sections[4].rating,
          customClass: "message-spacing", // 添加自定义CSS类
        });
      }, 1500);
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 20px;
  /* background-color: #f5f7fa; */
  min-height: calc(100vh - 84px);
}

.custom-tree-node {
  display: flex;
  align-items: center;
}

.prefix-text {
  background-color: #409eff;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 8px;
}
</style>