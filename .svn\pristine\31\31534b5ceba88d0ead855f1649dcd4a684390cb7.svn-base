<template>
  <div class="old-device-chart">
    <!-- ECharts 容器 -->
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
// 引入 ECharts
import * as echarts from 'echarts';

export default {
  name: 'OldDeviceChart',
  props: {
    // 图表数据（可选，默认使用示例数据）
    chartData: {
      type: Array,
      default: () => [
        { value: 2356, name: '0-10' },
        { value: 2356, name: '10-20' },
        { value: 2356, name: '20-30' },
        { value: 2356, name: '30-40' },
        { value: 2356, name: '40-50' },
        { value: 2356, name: '50以上' }
      ]
    },
    // 图表标题（可选，默认"老旧设备"）
    title: {
      type: String,
      default: '老旧设备'
    },
    // 图表尺寸（可选，默认宽高100%）
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      chartInstance: null, // ECharts 实例
      // 颜色配置（渐变颜色组）
      colors: [
        ['#42f4ff', '#07bbd0'],
        ['#f9d55c', '#f9d55c00'],
        ['#e3a207', '#e3a20700'],
        ['#e37107', '#e3710700'],
        ['#4ff0b0', '#4ff0b000'],
        ['#2d8dff', '#3d7ffe00'],
        ['#16bfff', '#1278c400']
      ]
    }
  },
  mounted() {
    // 初始化 ECharts 实例
    this.initChart()
    // 监听窗口 resize 事件，自适应图表
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // 销毁 ECharts 实例，释放资源
    if (this.chartInstance) {
      this.chartInstance.dispose()
    }
    // 移除 resize 事件监听
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    // 监听图表数据变化，更新图表
    chartData: {
      handler(newVal) {
        if (this.chartInstance) {
          this.chartInstance.setOption({
            series: [
              // 只更新数据系列（保持其他配置不变）
              ...this.getSeriesConfig(newVal)
            ]
          })
        }
      },
      deep: true // 深度监听对象/数组变化
    },
    // 监听标题变化，更新图表
    title(newVal) {
      if (this.chartInstance) {
        this.chartInstance.setOption({
          title: {
            text: newVal
          }
        })
      }
    }
  },
  methods: {
    // 初始化图表
    initChart() {
      // 获取容器 DOM 元素
      const container = this.$refs.chartContainer
      // 创建 ECharts 实例
      this.chartInstance = echarts.init(container)
      
      // 设置图表配置项
      const option = this.getChartOption(this.chartData)
      this.chartInstance.setOption(option)
    },

    // 构建图表完整配置项
    getChartOption(data) {
      return {
        color: this.getGradientColors(), // 渐变颜色配置
        // backgroundColor: '#051662',
        title: {
          text: this.title,
          left: 'center',
          top: '51%',
          textStyle: {
            fontWeight: 400,
            color: '#fff',
            fontSize: 18
          }
        },
        grid: {
          top: 40,
          left: 40,
          right: 40,
          bottom: 20,
          containLabel: true
        },
        tooltip: { show: true },
        series: this.getSeriesConfig(data) // 系列配置
      }
    },

    // 构建系列配置（外圆背景、数据、内圆虚线）
    getSeriesConfig(data) {
      return [
        {
          name: '外圆背景',
          type: 'pie',
          z: 0,
          silent: true,
          radius: ['40%', '60%'],
          label: { show: false },
          labelLine: { show: false },
          itemStyle: { color: '#0A3994' },
          data: [1]
        },
        {
          name: '数据',
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['45%', '55%'],
          z: 3,
          label: {
            show: true,
            color: '#fff',
            fontSize: 12,
            lineHeight: 20,
            formatter: '{c}\t\t{d}%\n{b}'
          },
          labelLine: {
            show: true,
            length: 30,
            length2: 15
          },
          tooltip: { show: false },
          data: data
        },
        {
          name: '內园虚线',
          type: 'pie',
          z: 0,
          silent: true,
          radius: ['32%', '32%'],
          label: { show: false },
          labelLine: { show: false },
          itemStyle: {
            color: 'none',
            borderColor: '#1C8AA1',
            borderType: [5, 8],
            borderDashOffset: 20
          },
          data: [1]
        }
      ]
    },

    // 生成渐变颜色数组
    getGradientColors() {
      return this.colors.map(color => ({
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 1, color: color[1] }, // 100% 处的颜色
          { offset: 0, color: color[0] }  // 0% 处的颜色
        ],
        global: false
      }))
    },

    // 图表自适应 resize
    handleResize() {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    }
  }
}
</script>

<style scoped>
/* 图表容器样式 */
.chart-container {
  width: 100%;
  height: 100%;
  min-width: 500px;
  min-height: 300px;
}
</style>