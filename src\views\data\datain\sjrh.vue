<template>
  <!-- 数据融合界面 -->
  <div class="app-container">
    <!-- <div>数据融合记录</div>
    <div>
      <el-row :gutter="24" type="flex" justify="space-between">
        <el-col :span="12">
          <span>共：{{ totalSum }} 条数据</span>
        </el-col>
        <el-col :span="12" style="text-align: right"> </el-col>
      </el-row>
    </div> -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <!-- <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="数据来源" prop="status">
        <el-select v-model="queryParams.sourceType" placeholder="请选择数据来源" clearable style="width: 240px">
          <el-option v-for="dict in dict.type.data_fusion" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="调度类型" prop="scheduleType">
       
        <el-select
          v-model="queryParams.scheduleType"
          placeholder="调度类型"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in scheduleTypes"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->

      <el-form-item label="任务创建时间">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <div class="public_button clear_button fl position_btn" @click="resetQuery">
          <i class="mr10 el-icon-refresh"></i>重置
        </div>

        <div @click="handleQuery" class="public_button search_btn fl position_btn">
          <i class="mr10 el-icon-search"></i>应用筛选
        </div>
        <div @click="handleExport" v-hasPermi="['data:datain:fileImportUpload']"
          class="public_button export_button fl position_btn">
          <i class="mr10 el-icon-upload2"></i>导出
        </div>
        <div @click="handleAdd" v-hasPermi="['data:datain:fileImportDelete']"
          class="public_button delete_button fl position_btn">
          <i class="mr10 el-icon-plus"></i>融合
        </div>
        <div class="public_button clear_button fl position_btn" @click="showrw"
          style="margin-left: 10px;padding: 12px 20px;">
          <i class="mr10 el-icon-view" size="small"></i>任务详情
        </div>
      </el-form-item>
    </el-form>
 
    <el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" type="index" width="120" />

      <el-table-column label="事件名称" prop="eventName" sortable />
      <!-- <el-table-column prop="status" label="执行状态" width="80">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_execution_status"
            :value="scope.row.status"
          />
        </template>
</el-table-column> -->
      <el-table-column label="数据来源" prop="sourceType" />
      <el-table-column label="摘要" :show-overflow-tooltip="true" width="300" prop="abstractText" />
      <el-table-column label="创建人" prop="createByName" />

      <!-- <el-table-column
        label="创建人"
        prop="createByName"
        width="70"
        :show-overflow-tooltip="true"
      /> -->
      <el-table-column label="创建时间" align="center" sortable prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <!-- 编辑、删除、运行日志、立即执行、激活、更新调度、下线 -->
      <!-- <el-table-column
        label="操作"
        width="400"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <el-button
            v-if="scope.row.zxzt != 1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['data:datain:update']"
            >编辑</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetails(scope.row)"
            v-hasPermi="['data:datain:view']"
            >查看</el-button
          >
          <el-button
            v-if="scope.row.zxzt === 2"
            size="mini"
            type="text"
            icon="el-icon-circle-close"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['data:datain:stopRun']"
            >终止运行</el-button
          >
          <el-button
            v-if="scope.row.zxzt === 3"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['data:datain:rerun']"
            >重跑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-tickets"
            @click="handleRunLog(scope.row)"
            v-hasPermi="['data:datain:runLog']"
            >运行日志</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['data:datain:batchDelete']"
            >删除</el-button
          >
        </template>
      </el-table-column> -->



    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <p class="dialogTitle">
        引接政工大数据平台的数据库的专题数据，进行融合处理
      </p>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <p class="dialogText">第一步：创建任务</p>
        <el-form-item label="任务名称" prop="rwmc">
          <el-input v-model="form.rwmc" placeholder="请输入任务名称" />
        </el-form-item>
        <p class="dialogText">第二步：源端表配置</p>
        <el-form-item label="数据源类型">
          <el-select class="width100" v-model="form.sex" placeholder="请选择数据源类型">
            <el-option v-for="dict in dict.type.data_fusion_source_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据库">
          <el-select class="width100" v-model="form.sex" placeholder="请选择数据库">
            <el-option v-for="dict in dict.type.data_fusion_source_database" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="源端表">
          <el-select class="width100" v-model="form.sex" placeholder="请选择源端表">
            <el-option v-for="dict in dict.type.data_fusion_source_table" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <p class="dialogText">第三步：目标表配置</p>
        <el-form-item label="目标数据类型">
          <el-select class="width100" v-model="form.sex" placeholder="请选择目标数据类型">
            <el-option v-for="dict in dict.type.data_fusion_target_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="目标数据库">
          <el-select class="width100" v-model="form.sex" placeholder="请选择目标数据库">
            <el-option v-for="dict in dict.type.data_fusion_target_database" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目标表">
          <el-select class="width100" v-model="form.sex" placeholder="请选择目标表">
            <el-option v-for="dict in dict.type.data_fusion_target_table" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <p class="dialogText">第四步：字段映射配置</p>
        <p class="dialogText">第五步：任务调度配置</p>
        <el-form-item label="汇聚方式" prop="isOpenFire">
          <el-radio-group v-model="form.isOpenFire">
            <el-radio :label="0">全量</el-radio>
            <el-radio :label="1">增量</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="加工策略" prop="isOpenFire">
          <el-radio-group v-model="form.isOpenFire">
            <el-radio :label="0">追加</el-radio>
            <el-radio :label="1">覆盖</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调度类型" prop="isOpenFire">
          <el-radio-group v-model="form.isOpenFire">
            <el-radio :label="0">人工调度</el-radio>
            <el-radio :label="1">周期调度</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="生效日期" prop="isOpenFire">
          <!-- <el-radio-group v-model="form.isOpenFire">
            <el-radio :label="0">人工调度</el-radio>
            <el-radio :label="1">周期调度</el-radio>
          </el-radio-group> -->
        </el-form-item>
        <el-form-item label="调度周期" prop="isOpenFire">
          <el-radio-group v-model="form.isOpenFire">
            <el-radio :label="0">小时</el-radio>
            <el-radio :label="1">日</el-radio>
            <el-radio :label="1">周</el-radio>
            <el-radio :label="1">月</el-radio>
            <el-radio :label="1">年</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- <p class="secondTitle">
          请上传开源信息监测软件系统的数据文件，支持一次批量导入多个文件
        </p>
        <el-form-item label-width="0" label="" prop="file">
          <commonUpload
            v-model="form.file"
            :file-size="10"
            :file-type="['xls', 'xlsx', 'zip', 'txt']"
            upload-url="/api/upload"
          />
        </el-form-item> -->
      </el-form>

      <el-popover placement="left" width="400" class="dialogPopover" trigger="hover">
        <div class="notice-container">
          <div class="notice-header">融合须知</div>
          <div class="notice-content">
            <h3>操作知悉</h3>
            <ul>
              <li>
                确保两边有统一的主键或可映射的唯一标识，避免出现重复、丢失或错误映射。
              </li>
              <li>
                不同系统的时区、时钟差异可能造成数据错乱，建议统一使用UTC时间或标准时间戳。
              </li>
              <!-- <li>
                如导入失败，请查看运行日志或联系技术支持获取帮助。重要数据建议先备份再执行导入操作。
              </li> -->
            </ul>
            <h3>常见问题</h3>
            <p>Q：汇聚方式如何选择？</p>
            <p>
              A：全量同步适用于首次初始化，但数据量大、耗时长；增量同步是基于时间戳或变更标记字段进行同步，故效率较高。
            </p>
          </div>
        </div>
        <el-tag slot="reference" type="success">融合须知</el-tag>
        <!-- <el-button slot="reference">hover 激活</el-button> -->
      </el-popover>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情弹框 -->
    <el-dialog title="任务详细信息" :visible.sync="openDetailDialog" width="800px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="任务名称" label-class-name="my-label"
          content-class-name="my-content">xxxxxxxxxxxxxxxxxxx</el-descriptions-item>
        <el-descriptions-item label="数据过滤规则">数据过滤规则数据过滤规则数据过滤规则数据过滤规则</el-descriptions-item>
        <el-descriptions-item label="重复数据处理策略">重复数据处理策略重复数据处理策略重复数据处理策略</el-descriptions-item>
        <el-descriptions-item label="上传文件"></el-descriptions-item>
      </el-descriptions>
      <!-- 文件列表 可下载 -->
      <transition-group name="file-list" tag="div" class="file-list" v-if="fileList.length > 0">
        <div v-for="(file, index) in fileList" :key="file.uid" class="file-item">
          <div class="file-info">
            <div class="file-icon" :class="getFileIconClass(file.name)">
              <svg v-if="getFileType(file.name) === 'excel'" viewBox="0 0 24 24" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M10 12L14 16M14 12L10 16" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
              <svg v-else-if="getFileType(file.name) === 'zip'" viewBox="0 0 24 24" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M12 11V17M10 13H14M10 15H14" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </div>

            <div class="file-details">
              <p class="file-name" :title="file.name">{{ file.name }}</p>
              <p class="file-size">{{ formatFileSize(file.size) }}</p>
            </div>
          </div>

          <div class="file-actions">
            <button type="button" class="action-btn download-btn" @click="handleDownload(file)" title="下载">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
                <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                  stroke-linejoin="round" />
              </svg>
            </button>
          </div>
        </div>
      </transition-group>
    </el-dialog>
    <!-- 查看详情列表 -->
    <el-dialog title="任务详情列表" :visible.sync="rwdia" width="800px" style="max-height: 800px; overflow-y: auto;">
  <el-table :data="roleListrw">
    <el-table-column label="任务类型" prop="taskType">
      <template slot-scope="scope">
        <span>{{ scope.row.taskType === 0 ? '导出' : scope.row.taskType === 2 ? '融合' : '未知' }}</span>
      </template>
    </el-table-column>
    <el-table-column label="任务状态" prop="status">
      <template slot-scope="scope">
        <span>{{ scope.row.status === 0 ? '执行中' : scope.row.status === 1 ? '执行失败' : scope.row.status === 2 ? '执行成功' : '未知' }}</span>
      </template>
    </el-table-column>
    <el-table-column label="创建时间" prop="createTime">
      <template slot-scope="scope">
        <span>{{ parseTime(scope.row.createTime) }}</span>
      </template>
    </el-table-column>
   <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
  <template slot-scope="scope">
    <!-- 下载按钮 -->
    <el-button 
      size="mini" 
      type="text" 
      icon="el-icon-download" 
      @click="handledownloadrw(scope.row)" 
      v-if="scope.row.taskType === 0 && scope.row.status === 2"
      v-hasPermi="['data:datain:batchDelete']">下载</el-button>
    <!-- 删除按钮 -->
    <el-button 
      size="mini" 
      type="text" 
      icon="el-icon-delete" 
      @click="handleDeleterw(scope.row)" 
      v-if="scope.row.status !== 0"
      v-hasPermi="['data:datain:batchDelete']">删除</el-button>
  </template>
</el-table-column>

  </el-table>
</el-dialog>

  </div>
</template>

<script>
import // listRole,
  "@/api/system/role";

import commonUpload from "@/views/components/commonUpload.vue";
import {
  // 分页查询import
  datain_dataFusion_list,
  // dimportatain_dataFusion_list,
  // 导出
  exportdataFusion,
  // 融合
  exportKafka,
  // 任务详情
  getrwlist,
  // 下载
  // 删除
  delrwrecord,
  exportrwrecord,


} from "@/api/data/datain";
export default {
  name: "Role",
  dicts: [
    "manual_theme",
    "manual_status",
    "manual_main_sentiment",
    "manual_main_category",
    "manual_main_topic",
    "manual_ct_type",
    "data_fusion",
    "data_fusion_source_table",
    "data_fusion_source_type",
    "data_fusion_source_database",
    "data_fusion_target_table",
    "data_fusion_target_database",
    "data_fusion_target_type",
  ],
  components: { commonUpload },
  data() {

    return {
      rwdia: false,
      totalSum: 0,
      // 调度类型
      scheduleTypes: [
        { value: 1, label: "人工调度" },
        { value: 2, label: "周期调度" },
      ],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 100,
      // 角色表格数据
      roleList: [],
      // 任务列表
      roleListrw: [],

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openDetailDialog: false,
      // 右侧弹出层
      drawer: false,
      direction: "rtl",
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // roleName: [{ required: true, message: "角色名称不能为空", trigger: "blur" }]
      },
      fileList: [
        {
          uid: "file-1760678872252-0",
          name: "工作.txt",
          size: 10817,
          raw: {},
          url: "blob:http://localhost/34b5fed2-4632-457b-8ace-a16dda188f36",
        },
        {
          uid: "file-1760678883922-1",
          name: "附件1.xlsx",
          size: 63495,
          raw: {},
          url: "blob:http://localhost/d9e1bbc5-f510-441b-b453-9e81900175ae",
        },
      ],
      totalCount: 65,
      successCount: 59,
      failCount: 1126,
      activeTab: "file1",
      file1Data: [{ id: "ID1001" }, { id: "ID1002" }],
      file2Data: [{ id: "ID2001" }],
      file3Data: [{ id: "ID3001" }, { id: "ID3002" }, { id: "ID3003" }],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handledownloadrw(row){
        const params ={id:row.id};
      this.download(

        "/dataaccess/dataFusion/downloadTask",
        params,
        `任务详情.xlsx`
    )
    },
        
      // );
    async handleDeleterw(row){


   const roleIds = row.id;
      this.$modal
        .confirm('是否删除当前任务？')
        .then(function () {
          return delrwrecord(roleIds);
        })
        .then(() => {
          this.showrw();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    async showrw() {
      let result = await getrwlist();
        this.roleListrw = result.rows;

      this.rwdia = true;
    },

    // 查看详情
    handleViewDetails(row) {
      this.openDetailDialog = true;
    },
    // 运行日志
    handleRunLog(row) {
      this.drawer = true;
    },
    /** 查询数据融合列表 */
    getList() {
      this.loading = true;
      datain_dataFusion_list(
        this.addDateRange(this.queryParams, this.dateRange)
      ).then((response) => {
        this.roleList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.totalSum = response.totalSum;
      });

    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      (this.menuExpand = false),
        (this.menuNodeAll = false),
        (this.deptExpand = true),
        (this.deptNodeAll = false),
        (this.form = {
          roleId: undefined,
          roleName: undefined,
          roleKey: undefined,
          roleSort: 0,
          status: "0",
          menuIds: [],
          deptIds: [],
          menuCheckStrictly: true,
          deptCheckStrictly: true,
          remark: undefined,
        });
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      // this.queryParams.sourceType = '';
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
      };
      this.handleQuery();
    },
    // 多选框选中数据this
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 
     * 融合按钮 */
    handleAdd() {
      exportKafka(
        this.addDateRange(this.queryParams, this.dateRange)
      ).then((response) => {
        if (response.code == 200) {
          this.$message.success("融合成功");
        } else {
          this.$message.error("融合失败");
        }

      });

    },
    /** 导出按钮操作 */
    handleExport() {
      const params = this.addDateRange(this.queryParams, this.dateRange);
      exportdataFusion(params).then((response) => {
        if (response.code == 200) {
          this.$message.success("导出成功");
        } else {
          this.$message.error("导出失败");
        }
      });
      // this.download(

      //   "dataaccess/dataFusion/export",
      //   params,
      //   `融合记录.xlsx`
      // );
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids;
      // const roleMenu = this.getRoleMenuTreeselect(roleId);
      getRole(roleId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.then((res) => {
            let checkedKeys = res.checkedKeys;
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false);
              });
            });
          });
        });
      });
      this.title = "修改角色";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.roleId != undefined) {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            updateRole(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            addRole(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },
    getFileType(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      if (["xls", "xlsx"].includes(ext)) return "excel";
      if (["zip", "rar", "7z"].includes(ext)) return "zip";
      if (["txt", "json", "csv"].includes(ext)) return "text";
      return "file";
    },
    formatFileSize(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toFixed(2) + " " + sizes[i];
    },


    handleDownload(file) {
      // 如果文件有 URL,直接下载
      if (file.url) {
        const link = document.createElement("a");
        link.href = file.url;
        link.download = file.name;
        link.click();
      } else if (file.raw) {
        // 如果有原始文件对象,创建临时 URL 下载
        const url = URL.createObjectURL(file.raw);
        const link = document.createElement("a");
        link.href = url;
        link.download = file.name;
        link.click();
        URL.revokeObjectURL(url);
      } else {
        this.$message.warning("文件不可下载");
      }
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped></style>
