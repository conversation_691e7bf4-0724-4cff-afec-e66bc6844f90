<template>
  <div class="assessment-status">
    <div class="appTitle">
      <div class="btTitle">下载</div>
    </div>
    <div class="flex">
      <div class="flex-item" v-for="(item, index) in downloadList" :key="index">
        <img src="./img/word-iocn (1).png" class="word-icon" alt="" />
        <span class="wdName">{{ item.fileName }}</span>
        <!-- <span class="wdTime">2024-03-04 11:00:00</span> -->
        <img
          src="./img/下载.png"
          @click="downloadkh(item.fileName)"
          class="download-icon"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script>
import { fileName, fileDownload } from "../../../api/system/download";
export default {
  name: "assessmentStatus",
  components: {},
  data() {
    return {
      downloadList: [],
    };
  },
  mounted() {
    this.getDownload();
  },
  methods: {
    async getDownload() {
      const res = await fileName();
      console.log(res, "res");
      this.downloadList = res;
    },
    async downloadkh(item) {
      let params = {
        fileName: item,
        delete: false,
      };
      // const res = await fileDownload(params);
      // this.dom_download(res, item);



      this.download('api/khqk/fileDownload', {
        ...params
      }, item)



    },
    //处理下载流
    dom_download(content, fileName) {
      const blob = new Blob([content]); // 创建一个类文件对象：Blob对象表示一个不可变的、原始数据的类文件对象
      //console.log(blob)
      const url = window.URL.createObjectURL(blob); //URL.createObjectURL(object)表示生成一个File对象或Blob对象
      let dom = document.createElement("a"); //设置一个隐藏的a标签，href为输出流，设置download
      dom.style.display = "none";
      dom.href = url;
      dom.setAttribute("download", fileName); // 指示浏览器下载url,而不是导航到它；因此将提示用户将其保存为本地文件
      document.body.appendChild(dom);
      dom.click();
    },
  },
};
</script>
<style scoped lang="scss">
.assessment-status {
  width: 100%;
  height: 100%;
  background: #f7f9fc;
  border-radius: 0px 0px 4px 4px;
  margin: 0 auto;
  background: url(./img/bg002.png) no-repeat center center;
  background-size: 100% 100%;
  padding: 22px;
}
.appTitle {
  width: 100%;
  height: 30px;
  border-bottom: 1px solid #c1ccd9;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #1b5dd8;
  font-weight: 600;
  position: relative;
}
.btTitle {
  width: 50px;
  height: 100%;
  display: flex;
  // align-items: center;
  justify-content: center;
  border-bottom: 2px solid #1b5dd8;
  position: absolute;
  bottom: -1px;
}
.flex {
  display: flex;
  align-items: center;
}
.flex-item {
  width: 620px;
  height: 72px;
  background: #ffffff;
  border: 1px solid rgba(215, 218, 224, 1);
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.15);
  position: relative;
  margin-top: 32px;
  margin-right: 20px;
}
.word-icon {
  width: 30px;
  height: 34px;
  position: absolute;
  top: 17px;
  left: 15px;
}
.wdName {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #2975e6;
  letter-spacing: 0;
  font-weight: 400;
  position: absolute;
  top: 23px;
  left: 57px;
}
.wdTime {
  font-family: SourceHanSansSC-Regular;
  font-size: 12px;
  color: #666666;
  letter-spacing: 0;
  font-weight: 400;
  position: absolute;
  top: 37px;
  left: 57px;
}
.download-icon {
  width: 20px;
  height: 19.17px;
  position: absolute;
  top: 26px;
  right: 26px;
  cursor: pointer;
}
</style>
