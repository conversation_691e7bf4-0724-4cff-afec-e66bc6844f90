<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="处室" prop="name">
        <el-select v-model="value" placeholder="请选择">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
      v-hasPermi="['system:audit:add']">查询</el-button>
    </el-form>

    <el-table v-loading="loading" :data="auditList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="xh" />
      <el-table-column label="部门" align="center" prop="bm" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="职务职称" align="center" prop="jobTitle" />
      <el-table-column label="测评日期" align="center" prop="date" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.date, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="考核等级" align="center" prop="assessmentLevel" />
      <el-table-column label="排名" align="center" prop="total" />
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:audit:edit']">修改</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改重要岗位审核情况对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="日期" prop="date">
          <el-date-picker clearable v-model="form.date" type="date" value-format="yyyy-MM-dd" placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="总分" prop="total">
          <el-input v-model="form.total" placeholder="请输入总分" />
        </el-form-item>
        <el-form-item label="德" prop="morals">
          <el-input v-model="form.morals" placeholder="请输入德" />
        </el-form-item>
        <el-form-item label="能" prop="ability">
          <el-input v-model="form.ability" placeholder="请输入能" />
        </el-form-item>
        <el-form-item label="勤" prop="diligent">
          <el-input v-model="form.diligent" placeholder="请输入勤" />
        </el-form-item>
        <el-form-item label="绩" prop="achievement">
          <el-input v-model="form.achievement" placeholder="请输入绩" />
        </el-form-item>
        <el-form-item label="廉" prop="integrity">
          <el-input v-model="form.integrity" placeholder="请输入廉" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAudit, getAudit, delAudit, addAudit, updateAudit } from "@/api/system/audit";

export default {
  name: "Audit",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 重要岗位审核情况表格数据
      auditList: [
        {
          xh: 1,
          bm: '调查处',
          name: '张老师',
          jobTitle: '处长',
          date: '2024年',
          assessmentLevel: '优秀',
          total: 1
        },
        {
          xh: 2,
          bm: '利用处',
          name: '李老师',
          jobTitle: '处长',
          date: '2024年',
          assessmentLevel: '优秀',
          total: 2
        }
      ],      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bm: null,
        name: null,
        jobTitle: null,
        date: null,
        assessmentLevel: null,
        total: null,
        morals: null,

        ability: null,
        diligent: null,
        achievement: null,
        integrity: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    /** 查询重要岗位审核情况列表 */
    // getList() {
    //   this.loading = true;
    //   listAudit(this.queryParams).then(response => {
    //     this.auditList = response.rows;
    //     this.total = response.total;
    //     this.loading = false;
    //   });
    // },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        bm: null,
        name: null,
        jobTitle: null,
        date: null,
        assessmentLevel: null,
        total: null,
        morals: null,

        ability: null,
        diligent: null,
        achievement: null,
        integrity: null,
        status: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加重要岗位审核情况";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getAudit(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改重要岗位审核情况";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateAudit(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAudit(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除重要岗位审核情况编号为"' + ids + '"的数据项？').then(function () {
        return delAudit(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/audit/export', {
        ...this.queryParams
      }, `audit_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
