<template>
  <div ref="mapContainer" style="width: 100%; height: 100%;"></div>
</template>

<script>
import * as echarts from "echarts";
import chinaDatas from './china.js'

export default {
  name: 'ChinaMapEnhanced',
  props: {
    mapConfig: {
      type: Object,
      default: () => ({
        mapColor: '#0a1e3a',
        borderColor: '#1e90ff',
        shadowColor: '#1e90ff',
        shadowBlur: 10,
        setIntervalTime: 3000,
      })
    },
    markers: {
      type: Array,
      default: () => [
        // 示例数据格式:
        // { name: '建筑名称', coords: [lng, lat], type: 'building', value: 100, info: '详细描述' }
      ]
    },
    routes: {
      type: Array,
      default: () => [
        // 示例数据格式:
        // { name: '路线名称', from: [lng, lat], to: [lng, lat], type: 'road', info: '详细描述' }
      ]
    }
  },
  data() {
    return {
      myChart: null,
      option: null,
      refreshInterval: null,
      typeStyles: {
        building: {
          color: ['#00ffff', '#00d4ff', '#00a8ff'],
          symbol: 'pin',
          symbolSize: [30, 40],
          label: '建筑'
        },
        landmark: {
          color: ['#ffd700', '#ffb700', '#ff9700'],
          symbol: 'diamond',
          symbolSize: 35,
          label: '地标'
        },
        factory: {
          color: ['#ff6b6b', '#ff4444', '#ff2222'],
          symbol: 'rect',
          symbolSize: 30,
          label: '工厂'
        },
        office: {
          color: ['#a78bfa', '#8b5cf6', '#7c3aed'],
          symbol: 'roundRect',
          symbolSize: 28,
          label: '办公楼'
        },
        road: {
          color: ['#10b981', '#059669', '#047857'],
          width: 2,
          label: '道路'
        },
        highway: {
          color: ['#f59e0b', '#d97706', '#b45309'],
          width: 3,
          label: '高速公路'
        },
        railway: {
          color: ['#3b82f6', '#2563eb', '#1d4ed8'],
          width: 2,
          label: '铁路'
        }
      }
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval)
    }
    if (this.myChart) {
      window.removeEventListener('resize', this.handleResize)
      this.myChart.dispose()
    }
  },
  methods: {
    initChart() {
      this.myChart = echarts.init(this.$refs.mapContainer)
      this.loadMapData()
    },

    loadMapData() {
      const chinaJson = chinaDatas
      echarts.registerMap('china', chinaJson)
      this.prepareChartData()
      this.setRefreshInterval()
    },

    prepareChartData() {
      const chartMarkers = this.markers.length > 0 ? this.markers : this.getDefaultMarkers()
      const chartRoutes = this.routes.length > 0 ? this.routes : this.getDefaultRoutes()
      
      this.setOption(chartMarkers, chartRoutes)
    },

    getDefaultMarkers() {
      return [
        { name: '国贸大厦', coords: [116.479008, 39.937923], type: 'building', value: 120, info: '北京CBD核心区，高度330米' },
        { name: '东方明珠', coords: [121.506662, 31.245853], type: 'landmark', value: 150, info: '上海标志性建筑，高度468米' },
        { name: '广州塔', coords: [113.324498, 23.109113], type: 'landmark', value: 140, info: '广州新电视塔，高度600米' },
        { name: '深圳科技园', coords: [113.946498, 22.539113], type: 'office', value: 100, info: '高新技术产业园区' },
        { name: '武汉光谷', coords: [114.401354, 30.478413], type: 'factory', value: 90, info: '光电子产业基地' },
        { name: '成都天府广场', coords: [104.06783, 30.657876], type: 'landmark', value: 110, info: '成都市中心地标' },
        { name: '西安钟楼', coords: [108.946466, 34.259611], type: 'landmark', value: 130, info: '古都标志性建筑' },
        { name: '杭州阿里巴巴', coords: [120.219375, 30.259244], type: 'office', value: 95, info: '互联网科技总部' },
        { name: '南京紫峰大厦', coords: [118.796877, 32.060255], type: 'building', value: 105, info: '江苏第一高楼，450米' },
        { name: '重庆环球金融中心', coords: [106.551556, 29.563010], type: 'building', value: 100, info: '重庆地标建筑' }
      ]
    },

    getDefaultRoutes() {
      return [
        { name: '京沪高速', from: [116.479008, 39.937923], to: [121.506662, 31.245853], type: 'highway', info: '全长1318公里，6车道' },
        { name: '京广高铁', from: [116.479008, 39.937923], to: [113.324498, 23.109113], type: 'railway', info: '全长2298公里，350km/h' },
        { name: '沪蓉高速', from: [121.506662, 31.245853], to: [104.06783, 30.657876], type: 'highway', info: '全长1966公里，连接东西' },
        { name: '沪杭高铁', from: [121.506662, 31.245853], to: [120.219375, 30.259244], type: 'railway', info: '全长160公里，350km/h' },
        { name: '广深高速', from: [113.324498, 23.109113], to: [113.946498, 22.539113], type: 'road', info: '全长122公里，珠三角主干道' }
      ]
    },

    convertMarkers(markers, type) {
      return markers
        .filter(item => item.type === type)
        .map(item => ({
          name: item.name,
          value: item.coords.concat(item.value || 100),
          itemStyle: {
            color: this.typeStyles[type].color[0]
          },
          info: item.info || ''
        }))
    },

    convertRoutes(routes, type) {
      return routes
        .filter(item => item.type === type)
        .map(item => ({
          name: item.name,
          coords: [item.from, item.to],
          info: item.info || '',
          lineStyle: {
            color: this.typeStyles[type].color[0],
            width: this.typeStyles[type].width
          }
        }))
    },

    setOption(markers, routes) {
      const series = []
      
      Object.keys(this.typeStyles).forEach(type => {
        if (['building', 'landmark', 'factory', 'office'].includes(type)) {
          const data = this.convertMarkers(markers, type)
          if (data.length > 0) {
            // 发光效果散点
            series.push({
              name: this.typeStyles[type].label,
              type: 'effectScatter',
              coordinateSystem: 'geo',
              data: data,
              // symbol: this.typeStyles[type].symbol,
              // symbolSize: this.typeStyles[type].symbolSize,
              showEffectOn: 'render',
              rippleEffect: {
                brushType: 'stroke',
                scale: 4,
                period: 4,
              },
              label: {
                formatter: '{b}',
                position: 'top',
                show: false,
                color: '#fff',
                fontSize: 12
              },
              emphasis: {
                label: {
                  show: true
                },
                scale: 1.3
              },
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: this.typeStyles[type].color[0] },
                  { offset: 0.5, color: this.typeStyles[type].color[1] },
                  { offset: 1, color: this.typeStyles[type].color[2] }
                ]),
                shadowBlur: 20,
                shadowColor: this.typeStyles[type].color[0],
              },
              zlevel: 3
            })
            
            // 常规散点图层
            series.push({
              name: this.typeStyles[type].label + '底层',
              type: 'scatter',
              coordinateSystem: 'geo',
              data: data,
              // symbol: this.typeStyles[type].symbol,
              // symbolSize: Array.isArray(this.typeStyles[type].symbolSize) 
              //   ? this.typeStyles[type].symbolSize.map(s => s * 0.8)
              //   : this.typeStyles[type].symbolSize * 0.8,
              itemStyle: {
                color: this.typeStyles[type].color[2],
                opacity: 0.6
              },
              zlevel: 2
            })
          }
        }
      })
      
      Object.keys(this.typeStyles).forEach(type => {
        if (['road', 'highway', 'railway'].includes(type)) {
          const data = this.convertRoutes(routes, type)
          if (data.length > 0) {
            // 发光线条效果
            series.push({
              name: this.typeStyles[type].label,
              type: 'lines',
              coordinateSystem: 'geo',
              data: data,
              effect: {
                show: true,
                period: 4,
                trailLength: 0.3,
                symbol: 'arrow',
                symbolSize: 8,
                color: this.typeStyles[type].color[0]
              },
              lineStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: this.typeStyles[type].color[0] },
                  { offset: 0.5, color: this.typeStyles[type].color[1] },
                  { offset: 1, color: this.typeStyles[type].color[2] }
                ]),
                width: this.typeStyles[type].width,
                curveness: 0.2,
                shadowBlur: 15,
                shadowColor: this.typeStyles[type].color[0]
              },
              zlevel: 2
            })
            
            // 底层静态线条
            series.push({
              name: this.typeStyles[type].label + '底层',
              type: 'lines',
              coordinateSystem: 'geo',
              data: data,
              lineStyle: {
                color: this.typeStyles[type].color[2],
                width: this.typeStyles[type].width * 1.5,
                opacity: 0.3,
                curveness: 0.2
              },
              zlevel: 1
            })
          }
        }
      })

      this.option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(10, 30, 58, 0.95)',
          borderColor: '#1e90ff',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: function(params) {
            if (params.componentSubType === 'lines') {
              return `
                <div style="padding: 8px;">
                  <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px; color: #00ffff;">
                    ${params.name}
                  </div>
                  <div style="color: #a0d8f1; line-height: 1.8;">
                    ${params.data.info || '暂无详细信息'}
                  </div>
                </div>
              `
            } else {
              return `
                <div style="padding: 8px;">
                  <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px; color: #00ffff;">
                    ${params.name}
                  </div>
                  <div style="color: #a0d8f1; margin-bottom: 4px;">
                    类型: ${params.seriesName}
                  </div>
                  <div style="color: #a0d8f1; margin-bottom: 4px;">
                    数值: ${params.value[2]}
                  </div>
                  <div style="color: #a0d8f1; line-height: 1.8;">
                    ${params.data.info || '暂无详细信息'}
                  </div>
                </div>
              `
            }
          }
        },
        geo: {
          map: 'china',
          label: {
            emphasis: {
              show: true,
              color: '#fff'
            }
          },
          regions: [{
            name: '南海诸岛',
            value: 0,
            itemStyle: {
              normal: {
                opacity: 0,
                label: {
                  show: false
                }
              }
            }
          }],
          roam: false,
          scaleLimit: {
            min: 1,
            max: 5
          },
          itemStyle: {
            normal: {
              areaColor: this.mapConfig.mapColor,
              borderColor: this.mapConfig.borderColor,
              borderWidth: 1.5,
              shadowColor: this.mapConfig.shadowColor,
              shadowBlur: this.mapConfig.shadowBlur,
            },
            emphasis: {
              areaColor: '#0d2847',
              borderColor: '#4fc3f7',
              borderWidth: 2,
              shadowBlur: 20,
              shadowColor: '#1e90ff'
            }
          }
        },
        series: series
      }

      this.myChart.setOption(this.option)
      window.addEventListener('resize', this.handleResize)
    },

    setRefreshInterval() {
      this.refreshInterval = setInterval(() => {
        const chartMarkers = this.markers.length > 0 ? this.markers : this.getDefaultMarkers()
        const chartRoutes = this.routes.length > 0 ? this.routes : this.getDefaultRoutes()
        
        // 随机更新一些点的大小以产生动态效果
        const updatedMarkers = chartMarkers.map(marker => ({
          ...marker,
          value: marker.value + Math.random() * 20 - 10
        }))
        
        this.setOption(updatedMarkers, chartRoutes)
      }, this.mapConfig.setIntervalTime)
    },

    handleResize() {
      if (this.myChart) {
        this.myChart.resize()
      }
    }
  }
}
</script>

<style scoped>
/* 添加容器样式增强视觉效果 */
div {
  background: radial-gradient(ellipse at center, rgba(30, 144, 255, 0.1) 0%, transparent 70%);
}
</style>
