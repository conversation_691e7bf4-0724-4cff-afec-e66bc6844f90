import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listDs(query) {
  return request({
    url: '/system/ds/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getDs(id) {
  return request({
    url: '/system/ds/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addDs(data) {
  return request({
    url: '/system/ds',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateDs(data) {
  return request({
    url: '/system/ds',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delDs(id) {
  return request({
    url: '/system/ds/' + id,
    method: 'delete'
  })
}
