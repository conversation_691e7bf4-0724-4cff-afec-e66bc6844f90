import request from '@/utils/request'

// 开源引接任务列表查询
export function getOpenSourceIntegrationList(query) {
  return request({
    url: '/dataaccess/openSourceIntegration/list',
    method: 'get',
    params: query
  })
}

// 开源引接任务详情查询
export function getOpenSourceIntegrationDetail(id) {
  return request({
    url: '/dataaccess/openSourceIntegration/' + id,
    method: 'get'
  })
}

// 开源引接任务新增
export function addOpenSourceIntegration(data) {
  return request({
    url: '/dataaccess/openSourceIntegration',
    method: 'post',
    data: data
  })
}

// 开源引接任务编辑
export function updateOpenSourceIntegration(data) {
  return request({
    url: '/dataaccess/openSourceIntegration',
    method: 'put',
    data: data
  })
}

// 开源引接任务删除（单条和批量删除共用）
export function deleteOpenSourceIntegration(ids) {
  return request({
    url: '/dataaccess/openSourceIntegration/' + ids,
    method: 'delete'
  })
}

// 开源引接任务导出
export function exportOpenSourceIntegration(query) {
  return request({
    url: '/dataaccess/openSourceIntegration/export',
    method: 'post',
    data: query
  })
}

// 上传开源引接文件
export function uploadOpenSourceIntegrationFile(data) {
  return request({
    url: '/dataaccess/openSourceIntegration/file',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 通过任务id查询任务下文件
export function getOpenSourceIntegrationFileList(taskId) {
  return request({
    url: '/dataaccess/openSourceIntegration/file/list',
    method: 'get',
    params: { taskId }
  })
}

// 通过开源引接id下载文件
export function downloadOpenSourceIntegrationFile(id) {
  return request({
    url: '/dataaccess/openSourceIntegration/file/download/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

// 通过开源引接id删除文件
export function deleteOpenSourceIntegrationFile(id) {
  return request({
    url: '/dataaccess/openSourceIntegration/file/' + id,
    method: 'delete'
  })
}
