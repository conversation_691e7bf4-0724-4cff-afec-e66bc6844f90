<template>
  <div class="api-management">
    <!-- 搜索栏 -->
    <el-form class="search_form" @submit.native.prevent>
      <div class="search-bar" style="display: flex; align-items: center;"> 
        <el-input v-model="searchApiName" placeholder="API名称" prefix-icon="el-icon-search" style="width: 200px;"
          @keyup.enter.native="fetchData" />
        <div class="public_button clear_button fl position_btn" @click="fetchData" style="margin-left: 10px;padding: 12px 20px;">
          <i class="mr10 el-icon-search" size="small"></i>搜索
        </div>
        <div @click="handleCreate" class="public_button export_button fl position_btn" style="margin-left: 10px;padding: 12px 20px;">
          <i class="mr10 el-icon-circle-plus-outline"></i>新增
        </div>
        <el-button type="text" class="expression-mode" @click="openApiDocDialog = true"
          style="margin-left: auto;">API接口使用说明</el-button>
      </div>
    </el-form>


    <!-- 表格 -->
    <el-table :data="tableData" style="width: 100%; margin-top: 20px;">
      <el-table-column prop="id" label="服务ID" width="80" />
      <el-table-column prop="apiName" label="API名称" />
      <el-table-column prop="topic" label="关联专题" />
      <el-table-column prop="apiUrl" label="API URL" />
      <el-table-column prop="createByCode" label="创建人" />
      <el-table-column prop="createdTime" label="创建时间" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- <el-button type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(scope.row)">删除</el-button> -->
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pageSize"
      @pagination="fetchData" style="margin-top: 20px; float: right;" class="pagination" />

    <!-- 新增/编辑弹框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="API名称" prop="apiName">
          <el-input v-model="form.apiName" />
        </el-form-item>
        <el-form-item label="API URL" prop="apiUrl">
          <el-input v-model="form.apiUrl" placeholder="API相对路径" />
        </el-form-item>
        <el-form-item label="关联专题" prop="topic">
          <el-select v-model="form.topic" placeholder="请选择专题">
            <el-option v-for="item in thematicList" :key="item.id" :label="item.categoryName"
              :value="item.categoryName" />
          </el-select>
        </el-form-item>
        <el-form-item label="API Token鉴权" prop="tokenRequired">
          <el-switch v-model="form.tokenRequired" active-value="1" inactive-value="2" active-text="开启"
            inactive-text="关闭" />
        </el-form-item>
        <el-form-item label="API Token" v-if="form.tokenRequired === '1'">
          <el-input v-model="form.apiToken" placeholder="API调用时需要在请求头中携带此Token进行身份验证" :disabled="true" />
          <!-- <el-button type="primary" size="small" style="margin-left: 10px;" @click="getToken">重新生成</el-button> -->
          <div class="public_button clear_button fl position_btn" @click="getToken">
            <i class="mr10 el-icon-refresh" size="small"></i>重新生成
          </div>
        </el-form-item>
        <el-form-item label="信息描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="4" placeholder="请输入信息描述，限制256个字符" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogVisible = false">取消</el-button> -->
        <!-- <el-button type="primary" @click="handleSubmit">发布</el-button> -->
        <div @click="dialogVisible = false" v-hasPermi="['data:datain:fileImportDelete']"
          class="public_button delete_button fl position_btn">
          <i class="mr10 el-icon-delete"></i>取消
        </div>
        <div class="ach_right" v-hasPermi="['data:datain:fileImportAdd']" @click="handleSubmit">
          <div class="public_button position_add_btn search_btn">
            <i class="mr10 el-icon-circle-plus-outline"></i>发布
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 查看详情弹框 -->
    <el-dialog title="API详细信息" :visible.sync="openDetailDialog" width="800px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="API名称" label-class-name="my-label">
          {{ currentTask.apiName || "无名称" }}
        </el-descriptions-item>
        <el-descriptions-item label="服务ID" label-class-name="my-label">
          {{ currentTask.id || "无ID" }}
        </el-descriptions-item>
        <el-descriptions-item label="关联专题" label-class-name="my-label">
          {{ currentTask.topic || "无专题" }}
        </el-descriptions-item>
        <el-descriptions-item label="API URL" label-class-name="my-label">
          {{ currentTask.apiUrl || "无URL" }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人" label-class-name="my-label">
          {{ currentTask.createByCode || "未知" }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" label-class-name="my-label">
          {{ currentTask.createdTime || "无时间" }}
        </el-descriptions-item>
        <el-descriptions-item label="API Token鉴权" label-class-name="my-label">
          {{ currentTask.tokenRequired === 1 ? "开启" : "关闭" }}
        </el-descriptions-item>
        <el-descriptions-item label="信息描述" label-class-name="my-label">
          {{ currentTask.description || "无描述" }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- API接口使用说明弹框 -->
    <el-dialog title="即将跳转到外部网站" :visible.sync="openApiDocDialog" width="500px" :show-close="true">
      <div style="padding: 10px 0; line-height: 1.8; color: #ffffff;">
        <p
          style="color: #f56c6c; display: flex; align-items: center; margin: 0; padding: 8px 15px; background-color: #fef0f0; border-radius: 4px;">
          <i class="el-icon-warning-outline" style="margin-right: 5px;"></i>外部网站安全性未知
        </p>
        <div
          style="margin: 15px 0; padding: 10px; background: transparent; border-radius: 4px; word-break: break-all; border: 0.1px solid #fef0f0;">
          <i class="el-icon-link" style="margin-right: 5px; color: #ffffff;"></i>
          https://s.apifox.cn/6e5b71c4-e6a5-41f9-b8c7-eb02882a962c/api-62070964
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <!-- 用a标签原生跳转，避免拦截 -->
        <div @click="openApiDocDialog = false" v-hasPermi="['data:datain:fileImportDelete']"
          class="public_button delete_button fl position_btn">
          <i class="mr10 el-icon-delete"></i>取消
        </div>
        <a href="https://s.apifox.cn/6e5b71c4-e6a5-41f9-b8c7-eb02882a962c/api-62070964" target="_blank"
          style="text-decoration: none;">
          <div class="public_button position_add_btn search_btn">
            <i class="mr10 el-icon-circle-plus-outline"></i>继续访问
          </div>
        </a>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getPageByapiName,
  selectAllThematicList,
  addDataSharing,
  updateDataSharing,
  deleteDataSharing,
  getToken,
} from "@/api/data/datashare";

export default {
  name: 'ApiManagement',
  data() {
    return {
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      searchApiName: '',
      dialogVisible: false,
      openDetailDialog: false,
      openApiDocDialog: false, 
      dialogTitle: '新建API',
      currentTask: {},
      thematicList: [], 
      form: {
        id: '', 
        apiName: '',
        apiUrl: '',
        topic: '',
        tokenRequired: '1', 
        apiToken: '',
        description: ''
      },
      rules: {
        apiName: [
          { required: true, message: '请输入API名称', trigger: 'blur' }
        ],
        apiUrl: [
          { required: true, message: '请输入API URL', trigger: 'blur' }
        ],
        topic: [
          { required: true, message: '请选择关联专题', trigger: 'change' }
        ],
        description: [
          { max: 256, message: '信息描述不能超过256个字符', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.fetchThematicList();
    this.fetchData();
  },
  methods: {
    fetchThematicList() {
      selectAllThematicList().then(res => {
        if (res.code === 200) {
          this.thematicList = res.data || [];
        } else {
          this.$message.error(res.msg || '获取专题列表失败');
        }
      }).catch(err => {
        console.error('获取专题列表失败', err);
        this.$message.error('获取专题列表失败，请稍后重试');
      });
    },
    fetchData() {
      const params = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        apiName: this.searchApiName
      };
      getPageByapiName(params).then(res => {
        this.tableData = res.rows || [];
        this.total = res.total || 0;
      }).catch(err => {
        console.error('分页查询失败', err);
        this.$message.error('数据加载失败，请稍后重试');
      });
    },
    // 移除原有的单独分页事件处理（公共组件通过.sync自动同步，无需单独处理）
    handleCreate() {
      this.dialogTitle = '新建API';
      this.form = {
        id: '',
        apiName: '',
        apiUrl: '',
        topic: '',
        tokenRequired: '1',
        apiToken: '',
        description: ''
      };
      this.dialogVisible = true;
    },
    handleView(row) {
      this.currentTask = { ...row };
      this.openDetailDialog = true;
    },
    handleEdit(row) {
      this.dialogTitle = '编辑API';
      this.form = { ...row };
      this.dialogVisible = true;
    },
    handleDelete(row) {
      this.$confirm('确认删除此API吗?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteDataSharing({ id: row.id }).then(res => {
          if (res.code === 200) {
            this.$message({ type: 'success', message: '删除成功!' });
            this.fetchData(); 
          } else {
            this.$message.error(res.msg || '删除失败');
          }
        }).catch(err => {
          console.error('删除接口调用失败', err);
          this.$message.error('删除失败，请稍后重试');
        });
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除' });
      });
    },
    getToken() {
      getToken().then(res => {
        if (res.code === 200) {
          this.form.apiToken = res.msg;
        } else {
          this.$message.error(res.msg || '获取Token失败');
        }
      }).catch(err => {
        console.error('获取Token接口调用失败', err);
        this.$message.error('获取Token失败，请稍后重试');
      });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.id) {
            updateDataSharing(this.form).then(res => {
              if (res.code === 200) {
                this.$message({ type: 'success', message: '修改成功!' });
                this.dialogVisible = false;
                this.fetchData(); 
              } else {
                this.$message.error(res.msg || '修改失败');
              }
            }).catch(err => {
              console.error('修改接口调用失败', err);
              this.$message.error('修改失败，请稍后重试');
            });
          } else {
            addDataSharing(this.form).then(res => {
              if (res.code === 200) {
                this.$message({ type: 'success', message: '发布成功!' });
                this.dialogVisible = false;
                this.fetchData(); 
              } else {
                this.$message.error(res.msg || '发布失败');
              }
            }).catch(err => {
              console.error('新增接口调用失败', err);
              this.$message.error('发布失败，请稍后重试');
            });
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.api-management {
  padding: 20px;
}
.search-bar {
  display: flex;
  align-items: center;
  /* justify-content: space-between; */
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px; /* 按钮间距 */
}
/* 去除a标签默认下划线，保持按钮样式统一 */
.dialog-footer a .el-button {
  text-decoration: none !important;
}
.my-label {
  background: #e1f3d8;
  width: 100px;
}
.my-content {
  background: #fde2e2;
}

/* 继承公共分页样式（如需单独调整可补充） */
::v-deep .pagination {
  color: #dcdfe6 !important;
}

::v-deep .el-form-item:nth-last-child(2) .el-input__inner,  /* 定位「API Token」输入框 */
::v-deep .el-form-item:last-child .el-input__inner {         /* 定位「信息描述」输入框 */
  background-color: transparent !important;  /* 底色透明 */
  border-color: transparent !important;  /* 边框颜色 */
}

.expression-mode {
  margin-left: 10px;
  color: #ffffff;
}

</style>