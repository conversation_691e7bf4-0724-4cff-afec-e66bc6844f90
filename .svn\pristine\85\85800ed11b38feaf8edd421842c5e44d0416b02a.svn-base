<template>
  <div class="single-ring-chart-container">
    <div class="chart-title">{{ title }}</div>
    <!-- ECharts 容器 -->
    <div ref="chart" class="chart"></div>
  </div>
</template>

<script>
// import echarts from 'echarts';
import * as echarts from 'echarts';

export default {
  name: 'SingleRingChart',
  props: {
    /**
     * @description 图表数据
     * @type {Array<Object>}
     * @default []
     * @example [{ "name": "人工填报", "value": 6, "percent": "5.0" }]
     */
    chartData: {
      type: Array,
      required: true,
      validator: (value) => {
        return value.every(item => 
          item.hasOwnProperty('name') && 
          item.hasOwnProperty('value') &&
          item.hasOwnProperty('percent')
        );
      }
    },
    /**
     * @description 图表宽度
     * @type {String|Number}
     * @default '100%'
     */
    width: {
      type: [String, Number],
      default: '100%'
    },
    title: {
      type: String,
      default: '默认标题'
    },
    /**
     * @description 图表高度
     * @type {String|Number}
     * @default '600px'
     */
    height: {
      type: [String, Number],
      default: '600px'
    }
  },
  data() {
    return {
      myChart: null,
      option: null
    };
  },
watch: {
  // 深度监听 chartData 变化
  chartData: {
    // 使用箭头函数来确保 this 指向组件实例
    handler: function(newVal, oldVal) {
      // 确保 chartData 有值
      if (newVal && newVal.length > 0) {
        // 如果 myChart 实例已存在，调用 updateChart
        if (this.myChart) {
          this.updateChart();
        } else {
          // 如果 myChart 实例不存在，说明组件还未挂载或初始化，手动调用 initChart
          // initChart 内部会生成 option 并调用 setOption
          this.initChart();
        }
      }
    },
    deep: true,
    // 关键：添加 immediate: true，确保在 watcher 被创建时立即执行一次回调
    // 这样可以处理组件挂载前数据就已准备好的情况
    immediate: true 
  }
},
  mounted() {
    this.initChart();
    // 监听窗口大小变化，使图表自适应
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
      this.myChart = null;
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }
      const chartDom = this.$refs.chart;
      if (!chartDom) return;
      this.myChart = echarts.init(chartDom);
      this.generateOption();
      this.myChart.setOption(this.option);
    },

    generateOption() {
      // 过滤掉 value 为 0 的数据，因为它们在饼图中不占空间
      const validData = this.chartData.filter(item => item.value > 0);

      this.option = {
        // backgroundColor: '#1f4279',
        color: ['#4CD57D', '#0CE5F3', '#4772E1', '#EFB062', '#4b5cc4', '#fa8c35', '#1891FF', '#12C3C3', '#FBCD14', '#F14864', '#8542E1', '#7DA6FF', '#4AC312', '#FB8F14', '#F148E7'],
        tooltip: {
          show: true,
          backgroundColor: '#02132fC9',
          borderColor: '#0D97E460',
          // 使用传入的 percent 值
          formatter: ' {b}: {c} ({d}%)',
          textStyle: {
            fontSize: 16,
            color: '#DCDCDC'
          },
        },
        legend: {
          type: 'scroll',
          pageIconColor: "#76b9ff",
          pageIconInactiveColor: "#ccc",
          pageTextStyle: {
            color: "#76b9ff",
            fontSize: 14,
          },
          show: true,
          top: '2%',
          bottom: '1%',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#ccc',
            fontSize: 12,
            padding: [0, 8, 0, 8]
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            selectedMode: 'single',
          radius: ['42%', '68%'],
          center: ['50%', '55%'], // 微调中心位置，适配顶部标题
            avoidLabelOverlap: false,
            label: {
              normal: {
                // 自定义标签格式，显示名称和数值
                formatter: '{b|{b}：}{c|{c}}',
                borderColor: 'rgb(255 255 255 / 45%)',
                borderWidth: 1,
                borderRadius: 4,
                rich: {
                  b: {
                    fontSize: 14,
                    lineHeight: 33,
                    // fontWeight: 'bold',
                    padding: [2, 20],
                    color: 'rgba(255,255,255,1)'
                  },
                  c: {
                    fontSize: 14,
                    color: 'rgba(255,255,255,1)',
                    backgroundColor: 'rgb(255 255 255 / 10%)',
                    padding: [10, 20],
                    borderRadius: 3,
                  },
                }
              }
            },
            labelLine: {
              length: 10, // 标签引导线长度
            },
            // 使用过滤后的数据
            data: validData
          }
        ]
      };
    },

    updateChart() {
      if (this.myChart) {
        this.generateOption();
        this.myChart.setOption(this.option, true);
      } else {
        this.initChart();
      }
    },
    
    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    }
  }
};
</script>

<style scoped>
.single-ring-chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 标题样式：居中、白色、黑体、18px */
.chart-title {
  width: 100%;
  text-align: center; /* 居中 */
  color: #ffffff; /* 白色 */
  font-weight: bold; /* 黑体（粗体） */
  font-size: 18px; /* 大小18px */
  margin-bottom: 10px; /* 与图表间距 */
}
.chart {
  width: 100%;
  height: 100%;
  min-width: 500px;
  min-height: 300px;
}
</style>