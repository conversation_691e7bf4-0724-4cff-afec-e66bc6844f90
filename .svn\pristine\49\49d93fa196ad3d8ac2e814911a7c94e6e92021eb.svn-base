<template>
  <div class="api-management">
    <!-- 搜索栏 -->
    <el-form class="search_form" @submit.native.prevent>
      <div class="search-bar" style="display: flex; align-items: center;">
        <el-input v-model="searchParams.reportName" placeholder="任务名称" prefix-icon="el-icon-search"
          style="width: 200px;" @keyup.enter.native="fetchTaskList" />
        <div class="public_button clear_button fl position_btn" @click="fetchTaskList"
          style="margin-left: 10px;padding: 12px 20px;">
          <i class="mr10 el-icon-search" size="small"></i>搜索
        </div>
        <div @click="handleCreate" class="public_button export_button fl position_btn"
          style="margin-left: 10px;padding: 12px 20px;">
          <i class="mr10 el-icon-circle-plus-outline"></i>新增
        </div>
        <!-- 模板管理按钮 -->
        <div class="public_button search_btn fl position_btn" style="margin-left: 10px;padding: 12px 20px;"
          @click="openTemplateDialog = true">
          <i class="mr10 el-icon-search"></i>模板管理
        </div>
        <div @click="handleBatchDelete" class="public_button delete_button fl position_btn"
          style="margin-left: 10px;padding: 12px 20px;">
          <i class="mr10 el-icon-delete"></i>批量删除
        </div>
      </div>
    </el-form>

    <!-- 表格 -->
    <el-table :data="taskList" style="width: 100%; margin-top: 20px;" ref="taskTable">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="reportName" label="报告名称" />
      <el-table-column prop="topicName" label="涉及专题" />
      <el-table-column prop="fillMethod" label="报告生成方式" :formatter="formatFillMethod" />
      <el-table-column prop="templateName" label="报告模板" />
      <el-table-column prop="createdTime" label="创建时间" />
      <el-table-column prop="generatedTime" label="生成时间" v-if="false" /> <!-- 若接口无此字段可隐藏 -->
      <el-table-column prop="status" label="进度" v-if="false" /> <!-- 若接口无此字段可隐藏 -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-download" @click="handleDownload(scope.row)">下载</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination v-show="total > 0" :total="total" :page.sync="searchParams.pageNum" :limit.sync="searchParams.pageSize"
      @pagination="fetchTaskList" style="margin-top: 20px; float: right;" class="pagination" />

    <!-- 新增/编辑弹框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="报告名称" prop="reportName">
          <el-input v-model="form.reportName" placeholder="报告名称" style="width: 400px;" />
        </el-form-item>
        <el-form-item label="涉及专题" prop="topicId">
          <el-select v-model="form.topicId" placeholder="涉及专题" style="width: 400px;">
            <el-option v-for="item in analysisTaskList" :key="item.id" :label="item.analysisName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="报告生成方式" prop="fillMethod">
          <el-select v-model="form.fillMethod" placeholder="报告生成方式" style="width: 400px;">
            <el-option label="人工填报" value="1" />
            <el-option label="自动填报" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="报告模板" prop="templateId">
          <el-select v-model="form.templateId" placeholder="报告模板" style="width: 400px;">
            <el-option v-for="item in allTemplateList" :key="item.id" :label="item.templateName" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <div @click="dialogVisible = false" v-hasPermi="['data:datain:fileImportDelete']"
          class="public_button delete_button fl position_btn">
          <i class="mr10 el-icon-delete"></i>取消
        </div>
        <div class="ach_right" v-hasPermi="['data:datain:fileImportAdd']" @click="handleSubmit">
          <div class="public_button search_btn">
            <i class="mr10 el-icon-circle-plus-outline"></i>发布
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 模板管理弹框 -->
    <el-dialog :title="'模板管理'" :visible.sync="openTemplateDialog" width="1200px">
      <div class="search-bar" style="display: flex; align-items: center;">
        <el-input v-model="templateSearchParams.templateName" placeholder="模板名称" prefix-icon="el-icon-search"
          style="width: 200px;" @keyup.enter.native="fetchTemplateList" />
        <div class="public_button clear_button fl position_btn" @click="fetchTemplateList"
          style="margin-left: 10px;padding: 12px 20px;margin-bottom: 20px;">
          <i class="mr10 el-icon-search" size="small"></i>搜索
        </div>
        <div @click="handleTemplateBatchDelete" class="public_button delete_button fl position_btn"
          style="margin-left: 10px;padding: 12px 20px;margin-bottom: 20px;">
          <i class="mr10 el-icon-delete"></i>批量删除
        </div>
      </div>
      <el-table :data="templateList" style="width: 100%;" ref="templateTable">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="templateName" label="模板名称" />
        <el-table-column prop="createByCode" label="上传人" />
        <el-table-column prop="createdTime" label="上传时间" />
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-download" @click="downloadTemplate(scope.row)">下载</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteTemplate(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- DOCX预览弹框 -->
    <el-dialog :title="`${currentDocName} - 预览`" :visible.sync="docxPreviewVisible" width="1200px" height="800px"
      append-to-body @close="cleanPreviewResource">
      <div style="width: 100%; height: calc(100% - 40px); overflow: auto; padding: 20px;">
        <div ref="docxPreviewRef"></div>
        <div v-if="isPreviewLoading"
          style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: #666;">
          <el-loading-spinner type="circle" size="50px"></el-loading-spinner>
          <p>文档加载中...</p>
        </div>
        <div v-if="previewError"
          style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: #f56c6c;">
          <i class="el-icon-error" style="font-size: 40px; margin-bottom: 10px;"></i>
          <p>文档预览失败，请尝试下载查看</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  Reportlist,
  getAnalysisTaskList,
  getAllTemplate,       
  getDocxFileStream,
  TemplateList, // 模板管理列表接口
  downloadTemplate, 
  topicTemplate, // 模板删除接口（仅用于弹框）
  topicReporttj, // 新增报告接口
  topicReportxg, // 修改报告接口
  topicReportsc  // 任务列表删除接口（主列表）
} from "@/api/data/reportList";

// import { renderAsync } from 'docx-preview';
// import JSZip from 'jszip';
// window.JSZip = JSZip;

export default {
  name: 'ApiManagement',
  data() {
    return {
      taskList: [],
      total: 0,
      searchParams: {
        reportName: '',
        pageNum: 1,
        pageSize: 10
      },
      dialogVisible: false,
      openTemplateDialog: false, 
      dialogTitle: '新建报告',
      analysisTaskList: [], 
      allTemplateList: [],  
      templateList: [], 
      templateSearchParams: { // 模板搜索参数（独立于任务搜索）
        templateName: '',
        pageNum: 1,
        pageSize: 10
      },
      form: {
        id: '',
        reportName: '',
        topicId: '',
        templateId: '',
        fillMethod: ''
      },
      rules: {
        reportName: [
          { required: true, message: '请输入报告名称', trigger: 'blur' }
        ],
        topicId: [
          { required: true, message: '请选择涉及专题', trigger: 'change' }
        ],
        fillMethod: [
          { required: true, message: '请选择报告生成方式', trigger: 'change' }
        ],
        templateId: [
          { required: true, message: '请选择报告模板', trigger: 'change' }
        ]
      },
      docxPreviewVisible: false,
      currentDocName: '',
      currentDocId: '',
      isPreviewLoading: false,
      previewError: false,
      isDownloading: false
    };
  },
  created() {
    this.fetchAnalysisTaskList();
    this.fetchAllTemplateList();
    this.fetchTaskList(); 
  },
  watch: {
    openTemplateDialog(val) {
      if (val) {
        this.fetchTemplateList();
      }
    }
  },
  methods: {
    formatFillMethod(row) {
      return row.fillMethod === 1 ? '人工填报' : '自动填报';
    },
    fetchAnalysisTaskList() {
      getAnalysisTaskList().then(res => {
        if (res.code === 200) {
          this.analysisTaskList = res.rows || [];
        } else {
          this.$message.error(res.msg || '获取分析内容列表失败');
        }
      }).catch(err => {
        console.error('获取分析内容列表失败', err);
        this.$message.error('分析内容列表加载失败，请稍后重试');
      });
    },

    fetchAllTemplateList() {
      getAllTemplate().then(res => {
        if (res.code === 200) {
          this.allTemplateList = res.rows || [];
        } else {
          this.$message.error(res.msg || '获取模板列表失败');
        }
      }).catch(err => {
        console.error('获取模板列表失败', err);
        this.$message.error('模板列表加载失败，请稍后重试');
      });
    },

    fetchTaskList() {
      Reportlist(this.searchParams).then(res => {
        if (res.code === 200) {
          this.taskList = res.rows || [];
          this.total = res.total || 0;
        } else {
          this.$message.error(res.msg || '获取任务列表失败');
        }
      }).catch(err => {
        console.error('调用Reportlist接口失败', err);
        this.$message.error('任务列表加载失败，请稍后重试');
      });
    },

    // 模板列表搜索功能：使用模板搜索参数
    fetchTemplateList() {
      const params = {
        templateName: this.templateSearchParams.templateName, // 模板名称搜索
        pageNum: this.templateSearchParams.pageNum,
        pageSize: this.templateSearchParams.pageSize
      };
      TemplateList(params).then(res => {
        if (res.code === 200) {
          this.templateList = res.rows || [];
        } else {
          this.$message.error(res.msg || '获取模板列表失败');
        }
      }).catch(err => {
        console.error('调用TemplateList接口失败', err);
        this.$message.error('获取模板列表失败，请稍后重试');
      });
    },

    handleCreate() {
      this.dialogTitle = '新建报告';
      this.form = {
        id: '',
        reportName: '',
        topicId: '',
        templateId: '',
        fillMethod: ''
      };
      this.dialogVisible = true;
    },

    handleView(row) {
      this.currentDocId = row.id;
      this.currentDocName = row.reportName;
      this.isPreviewLoading = true;
      this.previewError = false;
      this.docxPreviewVisible = true;

      getDocxFileStream({ id: row.id }, { responseType: 'blob' })
        .then(res => {
          this.$refs.docxPreviewRef.innerHTML = '';
          return renderAsync(res, this.$refs.docxPreviewRef);
        })
        .catch(err => {
          console.error('DOCX预览失败', err);
          this.previewError = true;
          this.$message.error('文档预览失败，请重试或下载查看');
        })
        .finally(() => {
          this.isPreviewLoading = false;
        });
    },

    handleEdit(row) {
      this.dialogTitle = '编辑报告';
      this.form = { ...row };
      this.dialogVisible = true;
    },

    // 主列表删除：使用任务删除接口
    handleDelete(row) {
      this.$confirm('确认删除此任务吗?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        topicReportsc(row.id).then(res => { // 任务删除接口
          if (res.code === 200) {
            this.$message({ type: 'success', message: '删除成功!' });
            this.fetchTaskList();
          } else {
            this.$message.error(res.msg || '删除失败');
          }
        }).catch(err => {
          console.error('任务删除接口调用失败', err);
          this.$message.error('删除失败，请稍后重试');
        });
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除' });
      });
    },

    handleBatchDelete() {
      const selectedRows = this.$refs.taskTable.selection;
      if (!selectedRows || selectedRows.length === 0) {
        this.$message.warning('请选择需要删除的任务');
        return;
      }
      const ids = selectedRows.map(row => row.id).join(',');
      this.$confirm(`确认删除选中的${selectedRows.length}个任务吗?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        topicReportsc(ids).then(res => { // 任务批量删除接口
          if (res.code === 200) {
            this.$message({ type: 'success', message: '批量删除成功!' });
            this.fetchTaskList();
          } else {
            this.$message.error(res.msg || '批量删除失败');
          }
        }).catch(err => {
          console.error('任务批量删除接口调用失败', err);
          this.$message.error('批量删除失败，请稍后重试');
        });
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除' });
      });
    },

    // 模板弹框批量删除
    handleTemplateBatchDelete() {
      const selectedRows = this.$refs.templateTable.selection;
      if (!selectedRows || selectedRows.length === 0) {
        this.$message.warning('请选择需要删除的模板');
        return;
      }
      const ids = selectedRows.map(row => row.id).join(',');
      this.$confirm(`确认删除选中的${selectedRows.length}个模板吗?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        topicTemplate(ids).then(res => { // 模板批量删除接口
          if (res.code === 200) {
            this.$message({ type: 'success', message: '批量删除成功!' });
            this.fetchTemplateList();
            this.fetchAllTemplateList();
          } else {
            this.$message.error(res.msg || '批量删除失败');
          }
        }).catch(err => {
          console.error('模板批量删除接口调用失败', err);
          this.$message.error('批量删除失败，请稍后重试');
        });
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除' });
      });
    },

    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const api = this.form.id ? topicReportxg : topicReporttj;
          api(this.form).then(res => {
            if (res.code === 200) {
              this.$message({ type: 'success', message: this.form.id ? '修改成功!' : '发布成功!' });
              this.dialogVisible = false;
              this.fetchTaskList();
            } else {
              this.$message.error(res.msg || (this.form.id ? '修改失败' : '发布失败'));
            }
          }).catch(err => {
            console.error(this.form.id ? '修改接口失败' : '新增接口失败', err);
            this.$message.error(this.form.id ? '修改失败，请重试' : '发布失败，请重试');
          });
        }
      });
    },

    handleDownload(row) {
      this.isDownloading = true;
      getDocxFileStream({ id: row.id }, { responseType: 'blob' })
        .then(res => {
          const blob = new Blob([res], {
            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          });
          const downloadUrl = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = downloadUrl;
          a.download = `${row.reportName}.docx`;
          a.click();
          URL.revokeObjectURL(downloadUrl);
          this.$message.success('下载成功');
        })
        .catch(err => {
          console.error('DOCX下载失败', err);
          this.$message.error('下载失败，请稍后重试');
        })
        .finally(() => {
          this.isDownloading = false;
        });
    },

    cleanPreviewResource() {
      if (this.$refs.docxPreviewRef) {
        this.$refs.docxPreviewRef.innerHTML = '';
      }
    },

    downloadTemplate(row) {
      downloadTemplate({ id: row.id }).then(res => {
        const blob = new Blob([res], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        });
        const downloadUrl = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = `${row.templateName}.docx`;
        a.click();
        URL.revokeObjectURL(downloadUrl);
        this.$message.success('模板下载成功');
      }).catch(err => {
        console.error('模板下载失败', err);
        this.$message.error('模板下载失败，请稍后重试');
      });
    },

    // 模板弹框单行删除：使用模板删除接口
    deleteTemplate(row) {
      this.$confirm('确认删除此模板吗?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        topicTemplate(row.id).then(res => { // 模板删除接口
          if (res.code === 200) {
            this.$message({ type: 'success', message: '模板删除成功!' });
            this.fetchTemplateList(); 
            this.fetchAllTemplateList(); 
          } else {
            this.$message.error(res.msg || '模板删除失败');
          }
        }).catch(err => {
          console.error('模板删除接口调用失败', err);
          this.$message.error('模板删除失败，请稍后重试');
        });
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除' });
      });
    }
  }
};
</script>

<style scoped>
.api-management {
  padding: 20px;
}
.search-bar {
  display: flex;
  align-items: center;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
.dialog-footer a .el-button {
  text-decoration: none !important;
}
.my-label {
  background: #e1f3d8;
  width: 100px;
}
.my-content {
  background: #fde2e2;
}
::v-deep .pagination {
  color: #dcdfe6 !important;
}
::v-deep .el-form-item:nth-last-child(2) .el-input__inner,
::v-deep .el-form-item:last-child .el-input__inner {
  background-color: transparent !important;
  border-color: transparent !important;
}
.expression-mode {
  margin-left: 10px;
  color: #ffffff;
}
</style>