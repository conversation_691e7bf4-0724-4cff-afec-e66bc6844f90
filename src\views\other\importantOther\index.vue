<template>
  <analysis-comparison
    :filter-config="filterConfig"
    :comparison-types="comparisonTypes"
    :table-columns="tableColumns"
    :chart-indicators="chartIndicators"
    :treedisabled="treedisabled"
    :showComparisonType="showComparisonType"
    :deptConfig="deptConfig"
    :tableData="tableData"
    @search="handleSearch"
    @treeclick="handleTreeClick"
    @radiochange="handleRadiochange"
  />
</template>

<script>
import AnalysisComparison from "@/components/AnalysisComparison";

export default {
  name: "ImportantOther",
  components: {
    AnalysisComparison,
  },
  data() {
    return {
      treedisabled: true,
      showComparisonType: true,
      orgName: "黑龙江省自然资源厅",
      filterConfig: [
        {
          id: 1,
          type: "select",
          key: "cs",
          placeholder: "处室",
          className: "rank-select",
          options: [{ label: "全部", value: "all" }],
        },
        {
          id: 2,
          type: "input",
          key: "name",
          placeholder: "姓名",
          className: "rank-select",
          // options: [
          //   { label: '全部', value: 'all' }
          // ]
        },
        {
          id: 3,
          type: "select",
          key: "rank",
          placeholder: "职务职级",
          className: "rank-select",
          options: [
            { label: "全部", value: "all" },
            { label: "二级巡查员", value: "ejxcy" },
            { label: "两总工", value: "lzg" },
            { label: "处长", value: "cz" },
          ],
        },
      ],
      deptConfig: {
        data: [
          {
            id: 100,
            label: "黑龙江省自然资源厅",
            disabled: false,
            children: [
              {
                id: 101,
                label: "内设机构",
                disabled: false,
                children: [
                  {
                    id: 102,
                    label: "调查处",
                    disabled: false,
                    children: [
                     
                      {
                        id: 104,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 105,
                        label: "王老师",
                        disabled: false,
                      },
                      {
                        id: 106,
                        label: "李老师",
                        disabled: false,
                      },
                      {
                        id: 107,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 108,
                        label: "王老师",
                        disabled: false,
                      },
                      {
                        id: 109,
                        label: "李老师",
                        disabled: false,
                      },
                     
                    ],
                  },
                  {
                    id: 103,
                    label: "权益处",
                    disabled: false,
                    children:[
                    {
                        id: 110,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 111,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 112,
                        label: "王老师",
                        disabled: false,
                      },
                      {
                        id: 113,
                        label: "李老师",
                        disabled: false,
                      },
                      {
                        id: 114,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 115,
                        label: "王老师",
                        disabled: false,
                      },
                      {
                        id: 116,
                        label: "李老师",
                        disabled: false,
                      },
                      {
                        id: 117,
                        label: "张老师",
                        disabled: false,
                      },
                      {
                        id: 118,
                        label: "王老师",
                        disabled: false,
                      },
                      {
                        id: 119,
                        label: "李老师",
                        disabled: false,
                      },
                      {
                        id: 120,
                        label: "张老师",
                        disabled: false,
                      },
                    ]
                  },
                ],
              },
            ],
          },
        ],
        props: {
          children: "children",
          label: "label",
        },
      },
      comparisonTypes: [
        { value: 1, label: "选中同志与全部其他同职务职级公务员平均分比较分析" },
        { value: 2, label: "选中同志与同处室其他公务员平均分比较分析" },
      ],
      staffList: [
        { id: "1", name: "张老师", department: "调查处" },
        { id: "2", name: "王老师", department: "登记局" },
        { id: "3", name: "李老师", department: "权益处" },
      ],
      tableColumns: [
        { prop: "category", label: "类别" },
        { prop: "personalScore", label: "张老师（调查处）" },
        { prop: "averageScore", label: "全部重要岗位平均分" },
      ],
      tableData: [
        { category: "德", personalScore: 80, averageScore: 85 },
        { category: "能", personalScore: 70, averageScore: 75 },
        { category: "勤", personalScore: 90, averageScore: 95 },
        { category: "绩", personalScore: 85, averageScore: 90 },
        { category: "廉", personalScore: 75, averageScore: 80 },
      ],
      chartIndicators: [
        { name: "德", max: 100 },
        { name: "能", max: 100 },
        { name: "勤", max: 100 },
        { name: "绩", max: 100 },
        { name: "廉", max: 100 },
      ],
    };
  },
  methods: {
    handleSearch(filterValues) {
      console.log("搜索条件：", filterValues);
      // 实现搜索逻辑
    },
    handleStaffSelect(staffId) {
      console.log("选中的人员ID：", staffId);
      // 实现人员选择逻辑
    },
    handleTreeClick(data) {
      this.tableColumns[1].label = data.label;
      this.tableData = [
        { category: "德", personalScore: 88, averageScore: 85 },
        { category: "能", personalScore: 77, averageScore: 75 },
        { category: "勤", personalScore: 99, averageScore: 95 },
        { category: "绩", personalScore: 10, averageScore: 90 },
        { category: "廉", personalScore: 20, averageScore: 80 },
      ];
    },
    handleRadiochange(value) {
      if (value.value == 1) {
        this.tableColumns[2].label = "全部重要岗位平均分";
      } else if (value.value == 2) {
        this.tableColumns[2].label = "同处室其他公务员平均分";
      }
      this.tableData = [
        { category: "德", personalScore: 88, averageScore: 85 },
        { category: "能", personalScore: 77, averageScore: 75 },
        { category: "勤", personalScore: 99, averageScore: 95 },
        { category: "绩", personalScore: 10, averageScore: 90 },
        { category: "廉", personalScore: 20, averageScore: 20 },
      ];
    },
  },
};
</script>
