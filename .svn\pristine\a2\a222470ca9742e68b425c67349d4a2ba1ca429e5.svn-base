<template>
  <div id="app">
    <img class="head_bg" v-if="$router.currentRoute.path != '/login'" src="./assets/images/icon_head_bg.png" alt="" srcset="">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";

export default {
  name: "App",
  components: { ThemePicker },
  metaInfo() {
    return {
      title:
        this.$store.state.settings.dynamicTitle &&
        this.$store.state.settings.title,
      titleTemplate: (title) => {
        return title
          ? `${title} - ${process.env.VUE_APP_TITLE}`
          : process.env.VUE_APP_TITLE;
      },
    };
  },
};
</script>
<style scoped>
#app {
  background-image: url('./assets/images/app_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  /* background: #0d7154; */
}
#app .head_bg {
  position: absolute;
  top: 0;
  left: 0;
    width: 100%;
}
#app .theme-picker {
  display: none;
}
@font-face {
  font-family: SourceHanSansSC-Bold;
  src: url("./assets/SourceHanSansSC-Bold-2.otf");
}
@font-face {
  font-family: SourceHanSansSC-Regular;
  src: url("./assets/SourceHanSansSC-Regular-2.otf");
}
@font-face {
  font-family: Alimama_ShuHeiTi_Bold;
  src: url("./assets/Alimama_ShuHeiTi_Bold.ttf");
}
@font-face {
  font-family: LetsgoDigital-Regular;
  src: url("./assets/Let_s_go_Digital_Regular.ttf");
}
</style>
