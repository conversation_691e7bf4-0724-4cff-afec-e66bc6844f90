<template>
  <!-- 开源引接界面 -->
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="任务名称" prop="roleName">
        <el-input
          v-model="queryParams.roleName"
          placeholder="请输入任务名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="执行状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="执行状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.sys_execution_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery"
          >应用筛选</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:role:add']"
          >创建任务</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['data:datain:batchExport']"
          >批量导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['data:datain:batchDelete']"
          >批量删除</el-button
        >
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="roleList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务名称" prop="rwmc" sortable />
      <el-table-column prop="zxzt" label="执行状态">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_execution_status" :value="scope.row.zxzt" />
        </template>
      </el-table-column>
      <el-table-column label="创建人" prop="cjr" :show-overflow-tooltip="true" />
      <el-table-column
        label="创建时间"
        align="center"
        sortable
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <el-button
            v-if="scope.row.zxzt != 1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['data:datain:update']"
            >编辑</el-button
          >
          <el-button
            v-else
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetails(scope.row)"
            v-hasPermi="['data:datain:view']"
            >查看</el-button
          >
          <el-button
            v-if="scope.row.zxzt === 2"
            size="mini"
            type="text"
            icon="el-icon-circle-close"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['data:datain:stopRun']"
            >终止运行</el-button
          >
          <el-button
            v-if="scope.row.zxzt === 3"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['data:datain:rerun']"
            >重跑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-tickets"
            @click="handleDelete(scope.row)"
            v-hasPermi="['data:datain:runLog']"
            >运行日志</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['data:datain:batchDelete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <p class="dialogTitle">引接本地化部署的开源信息监测软件系统的数据文件</p>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <p class="dialogText">第一步：创建任务</p>
        <el-form-item label="任务名称" prop="rwmc">
          <el-input v-model="form.rwmc" placeholder="请输入任务名称" />
        </el-form-item>
        <p class="dialogText">第二步：导入配置</p>
        <el-form-item label="数据过滤规则">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="默认导入全部数据，可设置过滤条件..."
          ></el-input>
        </el-form-item>
        <el-form-item label="重复数据处理策略">
          <el-select class="width100" v-model="form.sex" placeholder="请选择策略">
            <el-option
              v-for="dict in dict.type.sys_duplicate_data_strategy"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <p class="dialogText">第三步：上传文件</p>
        <p class="secondTitle">
          请上传开源信息监测软件系统的数据文件，支持一次批量导入多个文件
        </p>
        <el-form-item label-width="0" label="" prop="file">
          <commonUpload
            v-model="form.file"
            :file-size="10"
            :file-type="['xls', 'xlsx', 'zip', 'txt']"
            upload-url="/api/upload"
          />
        </el-form-item>
      </el-form>

      <el-popover placement="left" width="400" class="dialogPopover" trigger="hover">
        <div class="notice-container">
          <div class="notice-header">引接须知</div>
          <div class="notice-content">
            <h3>操作知悉</h3>
            <ul>
              <li>
                全量数据导入可能需要较长时间，请耐心等待。系统会在后台执行导入任务，您可以关闭页面或继续其他操作。
              </li>
              <li>
                导入过程中请勿重复提交相同的导入任务，以免造成数据重复或系统负载过高。
              </li>
              <li>
                如导入失败，请查看运行日志或联系技术支持获取帮助。重要数据建议先备份再执行导入操作。
              </li>
            </ul>
            <h3>常见问题</h3>
            <p>Q：导入失败如何处理？</p>
            <p>A：查看运行日志或联系技术支持人员进行处理。</p>
          </div>
        </div>
        <el-tag slot="reference" type="success">引接须知</el-tag>
        <!-- <el-button slot="reference">hover 激活</el-button> -->
      </el-popover>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">导入并解析</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看详情弹框 -->
    <el-dialog
      title="任务详细信息"
      :visible.sync="openDetailDialog"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="1" border>
        <el-descriptions-item
          label="任务名称"
          label-class-name="my-label"
          content-class-name="my-content"
          >xxxxxxxxxxxxxxxxxxx</el-descriptions-item
        >
        <el-descriptions-item label="数据过滤规则"
          >数据过滤规则数据过滤规则数据过滤规则数据过滤规则</el-descriptions-item
        >
        <el-descriptions-item label="重复数据处理策略"
          >重复数据处理策略重复数据处理策略重复数据处理策略</el-descriptions-item
        >
        <el-descriptions-item label="上传文件"></el-descriptions-item>
      </el-descriptions>
      <!-- 文件列表 可下载 -->
      <transition-group
        name="file-list"
        tag="div"
        class="file-list"
        v-if="fileList.length > 0"
      >
        <div v-for="(file, index) in fileList" :key="file.uid" class="file-item">
          <div class="file-info">
            <div class="file-icon" :class="getFileIconClass(file.name)">
              <svg
                v-if="getFileType(file.name) === 'excel'"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M10 12L14 16M14 12L10 16"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <svg
                v-else-if="getFileType(file.name) === 'zip'"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12 11V17M10 13H14M10 15H14"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <svg
                v-else
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>

            <div class="file-details">
              <p class="file-name" :title="file.name">{{ file.name }}</p>
              <p class="file-size">{{ formatFileSize(file.size) }}</p>
            </div>
          </div>

          <div class="file-actions">
            <button
              type="button"
              class="action-btn download-btn"
              @click="handleDownload(file)"
              title="下载"
            >
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M7 10L12 15L17 10"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12 15V3"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </transition-group>
    </el-dialog>
  </div>
</template>

<script>
import // listRole,
"@/api/system/role";

import commonUpload from "@/views/components/commonUpload.vue";

export default {
  name: "Role",
  dicts: ["sys_duplicate_data_strategy", "sys_execution_status"],
  components: { commonUpload },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openDetailDialog: false,
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        // roleName: [{ required: true, message: "角色名称不能为空", trigger: "blur" }]
      },
      fileList: [
        {
          uid: "file-1760678872252-0",
          name: "工作.txt",
          size: 10817,
          raw: {},
          url: "blob:http://localhost/34b5fed2-4632-457b-8ace-a16dda188f36",
        },
        {
          uid: "file-1760678883922-1",
          name: "附件1.xlsx",
          size: 63495,
          raw: {},
          url: "blob:http://localhost/d9e1bbc5-f510-441b-b453-9e81900175ae",
        },
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleViewDetails() {
      this.openDetailDialog = true;
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      // listRole(this.addDateRange(this.queryParams, this.dateRange)).then((response) => {
      // this.roleList = response.rows;
      this.roleList = [
        {
          id: 1,
          rwmc: "边界基础设施-2025年9月-数据导入任务",
          zxzt: 1,
          cjr: "张三",
          createTime: "2021-01-01 10:00:00",
        },
        {
          id: 2,
          rwmc: "核心网络设备-2025年10月-配置检测任务",
          zxzt: 2,
          cjr: "李四",
          createTime: "2021-02-15 14:30:00",
        },
        {
          id: 3,
          rwmc: "存储服务器-2025年8月-容量扩容任务",
          zxzt: 1,
          cjr: "王五",
          createTime: "2021-03-20 09:15:00",
        },
        {
          id: 4,
          rwmc: "安全防护系统-2025年11月-漏洞扫描任务",
          zxzt: 3,
          cjr: "赵六",
          createTime: "2021-04-05 16:45:00",
        },
        {
          id: 5,
          rwmc: "边缘计算节点-2025年9月-性能测试任务",
          zxzt: 1,
          cjr: "孙七",
          createTime: "2021-05-10 11:20:00",
        },
        {
          id: 6,
          rwmc: "数据备份中心-2025年10月-恢复演练任务",
          zxzt: 2,
          cjr: "周八",
          createTime: "2021-06-18 15:50:00",
        },
        {
          id: 7,
          rwmc: "负载均衡设备-2025年8月-策略调整任务",
          zxzt: 1,
          cjr: "吴九",
          createTime: "2021-07-25 08:30:00",
        },
        {
          id: 8,
          rwmc: "云平台资源-2025年11月-权限梳理任务",
          zxzt: 3,
          cjr: "郑十",
          createTime: "2021-08-30 13:10:00",
        },
        {
          id: 9,
          rwmc: "监控告警系统-2025年9月-阈值优化任务",
          zxzt: 1,
          cjr: "钱一",
          createTime: "2021-09-12 10:40:00",
        },
        {
          id: 10,
          rwmc: "网络布线系统-2025年10月-故障排查任务",
          zxzt: 2,
          cjr: "冯二",
          createTime: "2021-10-17 17:25:00",
        },
      ];
      // this.total = response.total;
      this.loading = false;
      // });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      (this.menuExpand = false),
        (this.menuNodeAll = false),
        (this.deptExpand = true),
        (this.deptNodeAll = false),
        (this.form = {
          roleId: undefined,
          roleName: undefined,
          roleKey: undefined,
          roleSort: 0,
          status: "0",
          menuIds: [],
          deptIds: [],
          menuCheckStrictly: true,
          deptCheckStrictly: true,
          remark: undefined,
        });
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.roleId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "创建开源引接任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids;
      const roleMenu = this.getRoleMenuTreeselect(roleId);
      getRole(roleId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.then((res) => {
            let checkedKeys = res.checkedKeys;
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false);
              });
            });
          });
        });
      });
      this.title = "修改角色";
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.roleId != undefined) {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            updateRole(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.menuIds = this.getMenuAllCheckedKeys();
            addRole(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },
    getFileType(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      if (["xls", "xlsx"].includes(ext)) return "excel";
      if (["zip", "rar", "7z"].includes(ext)) return "zip";
      if (["txt", "json", "csv"].includes(ext)) return "text";
      return "file";
    },
    formatFileSize(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toFixed(2) + " " + sizes[i];
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.roleId || this.ids;
      this.$modal
        .confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？')
        .then(function () {
          return delRole(roleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // this.download(
      //   "system/role/export",
      //   {
      //     ...this.queryParams,
      //   },
      //   `role_${new Date().getTime()}.xlsx`
      // );
    },
    handleDownload(file) {
      // 如果文件有 URL,直接下载
      if (file.url) {
        const link = document.createElement('a')
        link.href = file.url
        link.download = file.name
        link.click()
      } else if (file.raw) {
        // 如果有原始文件对象,创建临时 URL 下载
        const url = URL.createObjectURL(file.raw)
        const link = document.createElement('a')
        link.href = url
        link.download = file.name
        link.click()
        URL.revokeObjectURL(url)
      } else {
        this.$message.warning('文件不可下载')
      }
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.dialogTitle {
  position: absolute;
  top: 35px;
}
.dialogPopover {
  position: absolute;
  top: 15px;
  right: 50px;
}
.dialogText {
  font-size: 14px;
  font-weight: 600;
  color: #1f2f3d;
}

.secondTitle {
  font-size: 12px;
  font-weight: 400;
  color: #1f2f3d59;
}

.notice-container {
  width: 100%;
  margin: 20px auto;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-family: "Microsoft Yahei", sans-serif;
}

.notice-header {
  background-color: #d1e7ff;
  padding: 12px 20px;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.notice-content {
  padding: 20px;
  background-color: #fff;
}

.notice-content h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 12px;
  font-weight: 600;
}

.notice-content ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-bottom: 20px;
}

.notice-content ul li {
  margin-bottom: 10px;
  line-height: 1.6;
  color: #555;
}

.notice-content p {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #555;
}
.my-label {
  background: #e1f3d8;
}

.my-content {
  background: #fde2e2;
}
.file-list {
  margin-top: 20px;
}
</style>
