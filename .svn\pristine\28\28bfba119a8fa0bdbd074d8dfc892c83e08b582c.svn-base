import request from '@/utils/request'

// 人工填报记录分页查询
export function datain_record_list(query) {
  return request({
    url: '/dataaccess/manual/list',
    method: 'get',
    params: query
  })
}

// 人工填报查询单条（详情） 查询详细
export function getrecord(manualId) {
  return request({
    url: '/dataaccess/manual/' + manualId,
    method: 'get'
  })
}

// 人工填报 新增填报记录
export function addrecord(data) {
  return request({
    url: '/dataaccess/manual/add',
    method: 'post',
    data: data
  })
}

//人工填报编辑接口  继续编辑填报记录
export function updaterecord(data) {
  return request({
    url: '/dataaccess/manual/',
    method: 'put',
    data: data
  })
}
// 人工填报删除接口
export function delrecord(manualId) {
  return request({
    url: '/dataaccess/manual/' + manualId,
    method: 'delete'
  })
}
// 导出接口
// export function exports(data) {
//   return request({
//     url: '/dataaccess/manual/export',
//     method: 'post',
//     // ids: data

//   })
// }


// 人工填报暂存接口也调用新增接口，暂存flag是2，提交flag是1
// 人工填报查询单条（详情） 查询详细 