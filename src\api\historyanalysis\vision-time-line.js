// 可视化时间轴接口文件
import request from '@/utils/request'
// 新增阶段接口
export function addjd(data) {
  return request({
    url: '/dataaccess/stageDivision/add',
    method: 'post',
    data: data,
    
  })
}
// 查询接口
export function queryjd(data) {
  return request({
    url: '/dataaccess/stageDivision/list',
    method: 'get',
    params: data,
    
  })
}
// 删除阶段接口
export function deletejd(manualId) {
  return request({
    url: '/dataaccess/stageDivision/' + manualId,
    method: 'delete'
  })
}
// 编辑阶段接口
export function editjd(data) {
  return request({
    url: '/dataaccess/stageDivision/update',
    method: 'post',
    data: data,
    
  })
}
// 获取内容进行编辑
export function getjd(manualId) {
  return request({
    url: '/dataaccess/stageDivision/' + manualId,
    method: 'get'
  })
}
// 提交编辑内容
export function submitjd(data) {
  return request({
    url: '/dataaccess/stageDivision/update',
    method: 'post',
    data: data,
    
  })
}