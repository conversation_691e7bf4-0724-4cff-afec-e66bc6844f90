
 <!-- 可视化时间轴 -->  1 
 <template>
  <div class="app-container">
    <!-- 添加渐变背景和标签导航 -->
    <div class="page-header">
      <h3 class="page-title">堆下水桩土理货分析</h3>
      <div class="page-tabs">
        <span class="tab-item" @click="to_index">趋势总览</span>
        <span class="tab-item active">可视化时间轴</span>
        <span class="tab-item" @click="to_gjjs">高级检索</span>
        <span class="tab-item" @click="to_ztbg">专题报告</span>
      </div>
    </div>

    <!-- 添加事件发展阶段对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="820px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="阶段名称" prop="themeId"
          ><el-input
            v-model="queryParams.eventName"
            placeholder="请输入阶段名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="阶段起止日期" prop="themeId">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker
        ></el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <!-- 暂存和提交传入参数不一 -->
        <!-- 1草、2提交 -->
        <el-button @click="submitForm('1')">取消</el-button>
        <el-button type="primary" @click="submitForm('2')">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import commonUpload from "@/views/components/commonUpload.vue";
import {
  // manual_theme
  // dictCode 不会重复，当下拉框传的键值
  exports,
  datain_record_list,
  // listRole,
  // getRole,
  getrecord,
  delrecord,
  addrecord,
  // 提交
  updaterecord,
  // 暂存
  save_record,
  // dataScope,
  // changeRoleStatus,
  // deptTreeSelect,
} from "@/api/data/datain";
import {
  treeselect as menuTreeselect,
  roleMenuTreeselect,
} from "@/api/system/menu";

export default {
  components: { commonUpload },

  name: "Role",
  dicts: [
    "manual_theme",
    "manual_status",
    "manual_main_sentiment",
    "manual_main_category",
    "manual_main_topic",
    "manual_ct_type",
  ],

  data() {
    return {
      manualstatus: [
        { value: 1, label: "提交" },
        { value: 2, label: "草稿" },
      ],
      // 表单验证规则
      formRules: {
        userName: [
          { required: true, message: "用户名不能为空", trigger: "blur" },
        ],
        passWord: [
          { required: true, message: "密码不能为空", trigger: "blur" },
        ],
        handle_name: [
          { required: true, message: "所在处室不能为空", trigger: "change" },
        ],
      },
      // manual_status:
      fileList: [
        {
          uid: "file-1760678872252-0",
          name: "工作.txt",
          size: 10817,
          raw: {},
          url: "blob:http://localhost/34b5fed2-4632-457b-8ace-a16dda188f36",
        },
        {
          uid: "file-1760678883922-1",
          name: "附件1.xlsx",
          size: 63495,
          raw: {},
          url: "blob:http://localhost/d9e1bbc5-f510-441b-b453-9e81900175ae",
        },
      ],
      openDetailDialog: false,
      open_change: false,
      // open_change
      totalSum: 0,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: "1",
          label: "全部数据权限",
        },
        {
          value: "2",
          label: "自定数据权限",
        },
        {
          value: "3",
          label: "本部门数据权限",
        },
        {
          value: "4",
          label: "本部门及以下数据权限",
        },
        {
          value: "5",
          label: "仅本人数据权限",
        },
      ],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 详情返回并展示的字段
      queryParams_xq: {
        // 主题
        themeId: undefined,
        // 专题
        mainTopicId: undefined,
        //类别
        mainCategoryId: undefined,
        // 情感
        mainSentimentId: undefined,
        // 事件名称
        eventName: undefined,
        // 国家、地区
        countryRegion: undefined,
        // 发生地点
        locltion: undefined,
        // 发生时间
        occurrenceTime: undefined,
        // 持续时间
        duration: undefined,
        // 事件概况
        eventOverview: undefined,
        // 信源
        source: undefined,
        // 我方
        ourPersonnelOrg: undefined,
        // 对方
        opponentPersonnelOrg: undefined,
        // 附件信息
        fj: [
          {
            fjid: "",
            fjName: "",
          },
        ],
      },
      // 显示新增弹窗
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // 在这里改成研判的查询字段
        //
        eventName: undefined,
        themeId: undefined,
        mainSentimentId: undefined,
        mainTopicId: undefined,
        mainCategoryId: undefined,
        status: undefined,
      },
      // 继续编辑记录的id
      changeid: "",
      // 新增表单参数
      form: {
        themeId: "", //主题id
        mainTopicId: "", //专题id
        mainSentimentId: "", //情感id
        mainCategoryId: "", //类别id
        eventName: "", //事件名称
        countryRegion: "", //国家/地区
        localtion: "", //发生地点
        occurrenceTime: "", //发生时间
        duration: "", //持续时间
        eventOverview: "", //事件概况
        conflictType: "", //冲突类型
        conflictScale: "", //冲突规模
        isOpenFire: "", //是否开火
        casualtiesCount: "", //伤亡人数
        source: "", //信源
        ourPersonnelOrg: "", //我方相关人员、组织机构
        opponentPersonnelOrg: "", //对方相关人员、组织机构
        status: "",
        // 继续编辑记录的id

        changeid: "",
      },
      // 查询单条目、修改表单参数
      form_xq: {
        themeName: "", //主题名称
        themeId: "", //主题id
        mainTopicId: "", //专题id
        mainSentimentId: "", //情感id
        mainCategoryId: "", //类别id
        eventName: "", //事件名称
        countryRegion: "", //国家/地区
        localtion: "", //发生地点
        occurrenceTime: "", //发生时间
        duration: "", //持续时间
        eventOverview: "", //事件概况
        conflictType: "", //冲突类型
        conflictScale: "", //冲突规模
        isOpenFire: "", //是否开火
        casualtiesCount: "", //伤亡人数
        source: "", //信源
        ourPersonnelOrg: "", //我方相关人员、组织机构
        opponentPersonnelOrg: "", //对方相关人员、组织机构
        status: "",
        // 继续编辑记录的id

        id: "",
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 表单校验
      rules: {
        themeId: [{ required: true, message: "主题不能为空", trigger: "blur" }],
        mainTopicId: [
          { required: true, message: "专题不能为空", trigger: "blur" },
        ],
        eventName: [
          { required: true, message: "事件名称不能为空", trigger: "blur" },
        ],
        eventOverview: [
          { required: true, message: "事件概况不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.what();
  },
  methods: {
    to_ksh() {
      this.$router.push("/concract/keypage");
    },
    to_gjjs() {
      this.$router.push("/concract/gjjs");
    },
    to_ztbg() {
      this.$router.push("/concract/ztbg");
    },
    to_index() {
      this.$router.push("/concract/document");
    },
    what() {
      console.log(this.dict.type.manual_main_sentiment);
    },
    formatFileSize(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toFixed(2) + " " + sizes[i];
    },
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },
    getFileType(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      if (["xls", "xlsx"].includes(ext)) return "excel";
      if (["zip", "rar", "7z"].includes(ext)) return "zip";
      if (["txt", "json", "csv"].includes(ext)) return "text";
      return "file";
    },
    // 继续编辑
    async handlechange(row) {
      let res = await getrecord(row.id);
      // console.log(res.data)
      this.form_xq = res.data[0];
      this.open_change = true;
      this.form.id = row.id;
      console.log("获取当行id用于编辑");
    },
    // 查看详情
    async handleViewDetails(row) {
      let res = await getrecord(row.id);
      console.log(res.data);
      this.form_xq = res.data[0];

      this.openDetailDialog = true;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "创建报告";
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      datain_record_list(
        this.addDateRange(this.queryParams, this.dateRange)
      ).then((response) => {
        this.roleList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.totalSum = response.totalSum;
      });
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect().then((response) => {
        this.menuOptions = response.data;
      });
    },
    // 所有菜单节点数据
    // getMenuAllCheckedKeys() {
    //   // 目前被选中的菜单节点
    //   let checkedKeys = this.$refs.menu.getCheckedKeys();
    //   // 半选中的菜单节点
    //   let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
    //   checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    //   return checkedKeys;
    // },
    // // 所有部门节点数据
    // getDeptAllCheckedKeys() {
    //   // 目前被选中的部门节点
    //   let checkedKeys = this.$refs.dept.getCheckedKeys();
    //   // 半选中的部门节点
    //   let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();
    //   checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    //   return checkedKeys;
    // },
    /** 根据角色ID查询菜单树结构 */
    getRoleMenuTreeselect(roleId) {
      return roleMenuTreeselect(roleId).then((response) => {
        this.menuOptions = response.menus;
        return response;
      });
    },
    /** 根据角色ID查询部门树结构 */
    getDeptTree(roleId) {
      return deptTreeSelect(roleId).then((response) => {
        this.deptOptions = response.depts;
        return response;
      });
    },
    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.roleName + '"角色吗？')
        .then(function () {
          return changeRoleStatus(row.roleId, row.status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      (this.menuExpand = false),
        (this.menuNodeAll = false),
        (this.deptExpand = true),
        (this.deptNodeAll = false),
        (this.form = {
          themeId: "", //主题id
          mainTopicId: "", //专题id
          mainSentimentId: "", //情感id
          mainCategoryId: "", //类别id
          EventName: "", //事件名称
          CountryRegion: "", //国家/地区
          localtion: "", //发生地点
          occurrenceTime: "", //发生时间
          duration: "", //持续时间
          eventOverview: "", //事件概况
          conflictType: "", //冲突类型
          conflictScale: "", //冲突规模
          isOpenFire: "", //是否开火
          casualtiesCount: "", //伤亡人数
          source: "", //信源
          ourPersonnelOrg: "", //我方相关人员、组织机构
          opponentPersonnelOrg: "", //对方相关人员、组织机构
          status: "",
        });
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleDataScope(row);
          break;
        case "handleAuthUser":
          this.handleAuthUser(row);
          break;
        default:
          break;
      }
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type == "menu") {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
        }
      } else if (type == "dept") {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type == "menu") {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
      } else if (type == "dept") {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions : []);
      }
    },
    // // 树权限（父子联动）
    // handleCheckedTreeConnect(value, type) {
    //   if (type == "menu") {
    //     this.form.menuCheckStrictly = value ? true : false;
    //   } else if (type == "dept") {
    //     this.form.deptCheckStrictly = value ? true : false;
    //   }
    // },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids;
      const roleMenu = this.getRoleMenuTreeselect(roleId);
      getrecord(roleId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.then((res) => {
            let checkedKeys = res.checkedKeys;
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false);
              });
            });
          });
        });
      });
      this.title = "修改角色";
    },
    /** 选择角色权限范围触发 */
    dataScopeSelectChange(value) {
      if (value !== "2") {
        this.$refs.dept.setCheckedKeys([]);
      }
    },
    // /** 分配数据权限操作 */
    // handleDataScope(row) {
    //   this.reset();
    //   const deptTreeSelect = this.getDeptTree(row.roleId);
    //   getRole(row.roleId).then((response) => {
    //     this.form = response.data;
    //     this.openDataScope = true;
    //     this.$nextTick(() => {
    //       deptTreeSelect.then((res) => {
    //         this.$refs.dept.setCheckedKeys(res.checkedKeys);
    //       });
    //     });
    //   });
    //   this.title = "分配数据权限";
    // },
    // /** 分配用户操作 */
    // handleAuthUser: function (row) {
    //   const roleId = row.roleId;
    //   this.$router.push("/system/role-auth/user/" + roleId);
    // },router
    /** 提交按钮 */
    // 新增和暂存
    submitForm: function (c1) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.roleId != undefined) {
            this.form.status = c1;
            addrecord(this.form).then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.status = c1;
            addrecord(this.form).then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // 编辑进去的新增和暂存
    submitForm_change: function (c1) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.roleId != undefined) {
            this.form_xq.status = c1;
            updaterecord(this.form_xq).then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.open_change = false;
              this.getList();
            });
          } else {
            this.form_xq.status = c1;
            updaterecord(this.form_xq).then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.open_change = false;
              this.getList();
            });
          }
        }
      });
    },
    // },
    /** 提交按钮（数据权限） */
    // submitDataScope: function () {
    //   if (this.form.roleId != undefined) {
    //     this.form.deptIds = this.getDeptAllCheckedKeys();
    //     dataScope(this.form).then((response) => {
    //       this.$modal.msgSuccess("修改成功");
    //       this.openDataScope = false;
    //       this.getList();
    //     });
    //   }
    // },

    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除选中的数据项？")
        .then(function () {
          return delrecord(roleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    handleDelete_pl() {
      const roleIds = this.ids;
      // this.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？').then(function() {
      this.$modal
        .confirm("是否确认删除选中的数据项？")
        .then(function () {
          return delrecord(roleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // this.download(s
      //   "dataaccess/manual/export",
      //   {
      //     ids: [this.ids],
      //   },
      //   `填报记录.xlsx`
      // );
      this.download(
        "dataaccess/manual/export",
        {
          ...this.queryParams,
        },
        `填报记录.xlsx`
      );
    },
  },
};
</script>
<style scoped>
/* 美化页面标题，添加渐变效果和标签导航 */
.page-header {
  margin-bottom: 20px;
  background: rgba(255, 255, 255, 0.95);
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 16px 0;
}

.page-tabs {
  display: flex;
  gap: 8px;
}

.tab-item {
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
  background: #f5f7fa;
}

.tab-item:hover {
  background: #e8eaf0;
  color: #409eff;
}

.tab-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 500;
}
</style>