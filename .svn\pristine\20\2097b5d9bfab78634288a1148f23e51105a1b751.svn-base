<template>
  <div class="infrastructure-visualization">
    <!-- 头部统计信息 -->
    <div class="iv_header">
      <!-- <div class="ivh_left">
        <p class="title">基础建设总数（项）</p>
        <p class="nums">{{ totalInfrastructureCount }}</p>
      </div> -->
      <div class="ivh_right" @click="handleAdd">
        <div class="public_button position_add_btn search_btn">
          <i class="mr10 el-icon-circle-plus-outline"></i>添加坐标
        </div>
      </div>
      <div style="clear: both"></div>
    </div>

    <!-- 优化时间范围选择区域，增加时间范围切换效果 -->
    <div class="time-range-section">
      <div class="time-range-item">
        <div class="range-label">
          <i class="el-icon-time"></i> 第一时间范围
        </div>
        <el-date-picker
          v-model="timeRange1"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleTimeRange1Change"
          class="time-picker"
        >
        </el-date-picker>
        <el-tag v-if="timeRange1 && timeRange1.length === 2" type="success" size="small" class="data-count-tag">
          {{ filteredRange1Count }} 项
        </el-tag>
      </div>
      <div class="time-range-item">
        <div class="range-label">
          <i class="el-icon-time"></i> 第二时间范围
        </div>
        <el-date-picker
          v-model="timeRange2"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleTimeRange2Change"
          class="time-picker"
        >
        </el-date-picker>
        <el-tag v-if="timeRange2 && timeRange2.length === 2" type="warning" size="small" class="data-count-tag">
          {{ filteredRange2Count }} 项
        </el-tag>
      </div>
      <div class="time-comparison-switch">
        <el-button 
          :type="showComparison ? 'warning' : 'primary'" 
          size="small" 
          @click="toggleComparison"
          :icon="showComparison ? 'el-icon-view' : 'el-icon-s-data'"
        >
          {{ showComparison ? '对比显示' : '合并显示' }}
        </el-button>
        <el-button 
          type="info" 
          size="small" 
          @click="clearTimeRanges"
          icon="el-icon-refresh-left"
        >
          清空筛选
        </el-button>
      </div>
    </div>

    <!-- 地图区域 -->
    <div class="map-container">
      <!-- 添加地图状态指示器 -->
      <div class="map-status-indicator">
        <div class="status-item" v-if="currentMarkers.length > 0">
          <span class="status-dot building-dot"></span>
          <span class="status-text">建筑: {{ buildingCount }} 个</span>
        </div>
        <div class="status-item" v-if="currentRoutes.length > 0">
          <span class="status-dot road-dot"></span>
          <span class="status-text">道路: {{ roadCount }} 条</span>
        </div>
        <div class="status-item" v-if="showComparison">
          <span class="status-badge">对比模式</span>
        </div>
      </div>
      
      <ChinaMapEnhanced 
        :map-config="mapConfig"
        :markers="currentMarkers"
        :routes="currentRoutes"
      />
    </div>

    <!-- 数据列表 -->
    <div class="data-table-section">
      <div class="table-header">
        <h3>
          <i class="el-icon-s-grid"></i> 基础建设数据列表
        </h3>
        <!-- 添加快速筛选按钮 -->
        <div class="table-filters">
          <el-radio-group v-model="categoryFilter" size="small" @change="handleCategoryFilter">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="building">建筑</el-radio-button>
            <el-radio-button label="road">道路</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      
      <el-table
        v-loading="loading"
        :data="infrastructureList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :row-class-name="tableRowClassName"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="80" align="center" />
        <el-table-column label="标题" prop="title" min-width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="title-with-icon">
              <i :class="scope.row.category === 'building' ? 'el-icon-office-building' : 'el-icon-map-location'"></i>
              {{ scope.row.title }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="坐标" min-width="180" align="center">
          <template slot-scope="scope">
            <div class="coords-display">
              <div class="coord-item">
                <span class="coord-label">起点:</span>
                <span class="coord-value">{{ scope.row.longitude.toFixed(6) }}, {{ scope.row.latitude.toFixed(6) }}</span>
              </div>
              <div class="coord-item" v-if="scope.row.category === 'road'">
                <span class="coord-label">终点:</span>
                <span class="coord-value">{{ scope.row.endLongitude.toFixed(6) }}, {{ scope.row.endLatitude.toFixed(6) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="分类" prop="category" width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.category === 'building' ? 'primary' : 'success'" effect="dark">
              <i :class="scope.row.category === 'building' ? 'el-icon-office-building' : 'el-icon-map-location'"></i>
              {{ scope.row.category === 'building' ? '建筑' : '道路' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="内容" prop="content" min-width="200" show-overflow-tooltip />
        <el-table-column label="基建时间" prop="constructionTime" width="180" align="center" sortable />
        <!-- 添加删除操作 -->
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              class="edit-btn"
            >
              修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加/修改坐标对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="dialogVisible" 
      width="650px" 
      append-to-body
      :show-close="false"
      class="infrastructure-dialog"
    >
      <p class="dialogTitle">
        <i class="el-icon-info"></i> 
        添加基础建设的地理位置和详细信息，支持建筑（点）和道路（线）两种类型
      </p>
      
      <el-form ref="form" :model="form" :rules="rules" label-width="110px" class="dialog_form_style">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题，如：中国尊大厦、京沪高速" />
        </el-form-item>
        
        <el-form-item label="分类" prop="category">
          <el-radio-group v-model="form.category" @change="handleCategoryChange">
            <el-radio label="building" border>
              <i class="el-icon-office-building"></i> 建筑
            </el-radio>
            <el-radio label="road" border>
              <i class="el-icon-map-location"></i> 道路
            </el-radio>
          </el-radio-group>
          <div class="category-hint">
            <span v-if="form.category === 'building'" class="hint-text">
              <i class="el-icon-info"></i> 建筑将以点标记显示在地图上
            </span>
            <span v-else class="hint-text">
              <i class="el-icon-info"></i> 道路将以线条显示在地图上，需要提供起点和终点坐标
            </span>
          </div>
        </el-form-item>

        <div class="coordinate-section">
          <div class="section-title">
            <i class="el-icon-location"></i> {{ form.category === 'road' ? '起点坐标' : '坐标位置' }}
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度" prop="longitude">
                <el-input 
                  v-model.number="form.longitude" 
                  placeholder="116.404"
                  type="number"
                  step="0.000001"
                >
                  <template slot="append">°E</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度" prop="latitude">
                <el-input 
                  v-model.number="form.latitude" 
                  placeholder="39.915"
                  type="number"
                  step="0.000001"
                >
                  <template slot="append">°N</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 优化道路终点坐标输入区域 -->
        <transition name="el-zoom-in-top">
          <div class="coordinate-section" v-if="form.category === 'road'">
            <div class="section-title">
              <i class="el-icon-location-outline"></i> 终点坐标
            </div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="经度" prop="endLongitude">
                  <el-input 
                    v-model.number="form.endLongitude" 
                    placeholder="121.506"
                    type="number"
                    step="0.000001"
                  >
                    <template slot="append">°E</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="终点纬度" prop="endLatitude">
                  <el-input 
                    v-model.number="form.endLatitude" 
                    placeholder="31.245"
                    type="number"
                    step="0.000001"
                  >
                    <template slot="append">°N</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </transition>

        <el-form-item label="内容描述" prop="content">
          <el-input 
            v-model="form.content" 
            type="textarea" 
            :rows="4"
            placeholder="请输入详细内容，如：高度、规模、特色等信息"
            maxlength="500"
          />
        </el-form-item>

        <el-form-item label="基建时间" prop="constructionTime">
          <el-date-picker
            v-model="form.constructionTime"
            type="datetime"
            placeholder="选择基建完成时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <div class="public_button clear_button fr position_btn" @click="cancel">
          <i class="mr10 el-icon-close"></i>取 消
        </div>
        <div class="public_button search_btn fr position_btn" @click="submitForm">
          <i class="mr10 el-icon-finished"></i>确 定
        </div>
        <div style="clear:both"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ChinaMapEnhanced from './ChinaMap.vue'
import Pagination from '@/components/Pagination'

export default {
  name: 'InfrastructureVisualization',
  components: {
    ChinaMapEnhanced,
    Pagination
  },
  data() {
    return {
      mapConfig: {
        mapColor: '#0a1e3a',
        borderColor: '#1e90ff',
        shadowColor: '#1e90ff',
        shadowBlur: 10,
        setIntervalTime: 5000
      },
      
      timeRange1: [],
      timeRange2: [],
      showComparison: false,
      categoryFilter: 'all', // 添加分类筛选

      loading: false,
      infrastructureList: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      selectedIds: [],

      dialogVisible: false,
      dialogTitle: '添加坐标',
      form: {
        id: null,
        title: '',
        category: 'building',
        longitude: null,
        latitude: null,
        endLongitude: null,
        endLatitude: null,
        content: '',
        constructionTime: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ],
        longitude: [
          { required: true, message: '请输入经度', trigger: 'blur' },
          { type: 'number', message: '经度必须为数字', trigger: 'blur' }
        ],
        latitude: [
          { required: true, message: '请输入纬度', trigger: 'blur' },
          { type: 'number', message: '纬度必须为数字', trigger: 'blur' }
        ],
        constructionTime: [
          { required: true, message: '请选择基建时间', trigger: 'change' }
        ]
      },
      mockData: [
  {
    id: 1,
    title: '中国尊大厦',
    category: 'building',
    longitude: 116.479008,
    latitude: 39.937923,
    content: '北京第一高楼，高度528米，108层，位于北京CBD核心区，2018年完工',
    constructionTime: '2018-06-15 10:00:00'
  },
  {
    id: 2,
    title: '上海中心大厦',
    category: 'building',
    longitude: 121.506662,
    latitude: 31.245853,
    content: '中国第一高楼，高度632米，128层，上海地标建筑，世界第二高楼',
    constructionTime: '2016-03-12 09:00:00'
  },
  {
    id: 3,
    title: '深圳平安金融中心',
    category: 'building',
    longitude: 114.046498,
    latitude: 22.539113,
    content: '深圳第一高楼，高度599米，115层，综合性超高层建筑',
    constructionTime: '2017-08-28 14:30:00'
  },
  {
    id: 4,
    title: '广州塔',
    category: 'building',
    longitude: 113.324498,
    latitude: 23.109113,
    content: '广州新电视塔，高度600米，观光塔，昵称"小蛮腰"',
    constructionTime: '2010-09-30 12:00:00'
  },
  {
    id: 5,
    title: '京沪高速公路',
    category: 'road',
    longitude: 116.479008,
    latitude: 39.937923,
    endLongitude: 121.506662,
    endLatitude: 31.245853,
    content: 'G2京沪高速，全长1318公里，双向8车道，连接北京和上海，中国最繁忙的高速公路之一',
    constructionTime: '2000-12-31 00:00:00'
  },
  {
    id: 6,
    title: '京港澳高速',
    category: 'road',
    longitude: 116.479008,
    latitude: 39.937923,
    endLongitude: 113.324498,
    endLatitude: 23.109113,
    content: 'G4京港澳高速，全长2285公里，连接北京和香港，纵贯南北的交通大动脉',
    constructionTime: '2004-12-31 00:00:00'
  },
  {
    id: 7,
    title: '京广高铁',
    category: 'road',
    longitude: 116.479008,
    latitude: 39.937923,
    endLongitude: 113.324498,
    endLatitude: 23.109113,
    content: '全长2298公里，设计时速350公里，世界运营里程最长的高速铁路',
    constructionTime: '2012-12-26 08:00:00'
  },
  {
    id: 8,
    title: '成都天府国际金融中心',
    category: 'building',
    longitude: 104.06783,
    latitude: 30.657876,
    content: '成都地标建筑，高度248米，双子塔设计，成都第一高楼',
    constructionTime: '2019-07-20 11:00:00'
  },
  {
    id: 9,
    title: '西安大雁塔',
    category: 'building',
    longitude: 108.946466,
    latitude: 34.259611,
    content: '唐代佛塔，高64.5米，古都西安的标志性建筑，世界文化遗产',
    constructionTime: '652-01-01 00:00:00'
  },
  {
    id: 10,
    title: '沪蓉高速',
    category: 'road',
    longitude: 121.506662,
    latitude: 31.245853,
    endLongitude: 104.06783,
    endLatitude: 30.657876,
    content: 'G42沪蓉高速，全长1966公里，横贯东西，连接长三角和成渝地区',
    constructionTime: '2007-12-31 00:00:00'
  },
  {
    id: 11,
    title: '杭州阿里巴巴总部',
    category: 'building',
    longitude: 120.219375,
    latitude: 30.259244,
    content: '互联网科技总部，现代化办公园区，中国电子商务的发源地',
    constructionTime: '2013-05-10 15:30:00'
  },
  {
    id: 12,
    title: '南京紫峰大厦',
    category: 'building',
    longitude: 118.796877,
    latitude: 32.060255,
    content: '江苏第一高楼，高度450米，89层，南京地标建筑',
    constructionTime: '2010-04-15 10:30:00'
  },
  // 新增数据开始
  {
    id: 13,
    title: '天津周大福金融中心',
    category: 'building',
    longitude: 117.278914,
    latitude: 39.085823,
    content: '天津第一高楼，高度530米，103层，位于滨海新区',
    constructionTime: '2019-09-26 10:00:00'
  },
  {
    id: 14,
    title: '重庆来福士广场',
    category: 'building',
    longitude: 106.597332,
    latitude: 29.569902,
    content: '重庆地标建筑，高度354.5米，由8栋塔楼组成，2019年开业',
    constructionTime: '2019-09-06 14:00:00'
  },
  {
    id: 15,
    title: '苏州中南中心',
    category: 'building',
    longitude: 120.623324,
    latitude: 31.316443,
    content: '江苏第一高楼，高度499.15米，103层，位于苏州工业园区',
    constructionTime: '2024-06-30 09:00:00'
  },
  {
    id: 16,
    title: '武汉绿地中心',
    category: 'building',
    longitude: 114.302285,
    latitude: 30.593085,
    content: '华中第一高楼，高度475米，101层，位于武昌滨江商务区',
    constructionTime: '2021-06-28 11:00:00'
  },
  {
    id: 17,
    title: '郑州绿地中心千玺广场',
    category: 'building',
    longitude: 113.660634,
    latitude: 34.757264,
    content: '郑州地标建筑，高度280米，因外形酷似玉米被称为"大玉米"',
    constructionTime: '2011-07-15 10:00:00'
  },
  {
    id: 18,
    title: '长沙IFS国金中心',
    category: 'building',
    longitude: 112.982277,
    latitude: 28.19409,
    content: '湖南第一高楼，高度452米，95层，2018年开业',
    constructionTime: '2018-05-07 14:30:00'
  },
  {
    id: 19,
    title: '青岛海天中心',
    category: 'building',
    longitude: 120.332414,
    latitude: 36.067427,
    content: '山东第一高楼，高度369米，75层，位于浮山湾畔',
    constructionTime: '2021-06-25 09:30:00'
  },
  {
    id: 20,
    title: '厦门国际中心',
    category: 'building',
    longitude: 118.101622,
    latitude: 24.543595,
    content: '福建第一高楼，高度339.88米，68层，2019年竣工',
    constructionTime: '2019-12-28 10:00:00'
  },
  {
    id: 21,
    title: '沈阳宝能环球金融中心',
    category: 'building',
    longitude: 123.432733,
    latitude: 41.806233,
    content: '东北第一高楼，高度568米，111层，位于沈阳金融商贸开发区',
    constructionTime: '2025-12-31 08:00:00'
  },
  {
    id: 22,
    title: '大连绿地中心',
    category: 'building',
    longitude: 121.613661,
    latitude: 38.913372,
    content: '辽宁第一高楼，高度518米，108层，位于东港商务区',
    constructionTime: '2023-09-30 11:00:00'
  },
  {
    id: 23,
    title: '合肥恒大中心',
    category: 'building',
    longitude: 117.321335,
    latitude: 31.84055,
    content: '安徽第一高楼，高度518米，102层，位于滨湖新区',
    constructionTime: '2024-05-20 10:00:00'
  },
  {
    id: 24,
    title: '南昌绿地中心',
    category: 'building',
    longitude: 115.893028,
    latitude: 28.681221,
    content: '江西第一高楼，高度303米，68层，位于红谷滩新区',
    constructionTime: '2015-07-28 14:00:00'
  },
  {
    id: 25,
    title: '昆明恒隆广场',
    category: 'building',
    longitude: 102.71563,
    latitude: 25.045212,
    content: '云南第一高楼，高度349米，76层，2019年开业',
    constructionTime: '2019-08-26 09:30:00'
  },
  {
    id: 26,
    title: '贵阳国际金融中心',
    category: 'building',
    longitude: 106.63978,
    latitude: 26.65923,
    content: '贵州第一高楼，高度401米，88层，位于观山湖区',
    constructionTime: '2022-12-31 10:00:00'
  },
  {
    id: 27,
    title: '兰州国际商贸中心',
    category: 'building',
    longitude: 103.835443,
    latitude: 36.061231,
    content: '甘肃第一高楼，高度313米，75层，位于七里河区',
    constructionTime: '2021-05-18 11:00:00'
  },
  {
    id: 28,
    title: '银川绿地中心',
    category: 'building',
    longitude: 106.232934,
    latitude: 38.47222,
    content: '宁夏第一高楼，高度301米，66层，位于阅海湾商务区',
    constructionTime: '2023-06-15 09:00:00'
  },
  {
    id: 29,
    title: '西宁绿地中心',
    category: 'building',
    longitude: 101.778242,
    latitude: 36.623331,
    content: '青海第一高楼，高度280米，62层，位于海湖新区',
    constructionTime: '2022-08-20 10:30:00'
  },
  {
    id: 30,
    title: '乌鲁木齐绿地中心',
    category: 'building',
    longitude: 87.627647,
    latitude: 43.832451,
    content: '新疆第一高楼，高度308米，68层，位于高铁新区',
    constructionTime: '2024-03-30 11:30:00'
  },
  {
    id: 31,
    title: '拉萨圣地天堂洲际大饭店',
    category: 'building',
    longitude: 91.117527,
    latitude: 29.654531,
    content: '西藏最高建筑，高度110米，27层，位于拉萨河畔',
    constructionTime: '2014-09-25 10:00:00'
  },
  {
    id: 32,
    title: '港珠澳大桥',
    category: 'road',
    longitude: 113.542222,
    latitude: 22.174361,
    endLongitude: 113.971963,
    endLatitude: 22.207474,
    content: '世界最长跨海大桥，全长55公里，连接香港、珠海和澳门',
    constructionTime: '2018-10-24 09:00:00'
  },
  {
    id: 33,
    title: '北盘江大桥',
    category: 'road',
    longitude: 104.892315,
    latitude: 26.165557,
    endLongitude: 104.905132,
    endLatitude: 26.153317,
    content: '世界最高桥梁，高度565米，全长1341.4米，位于贵州和云南交界处',
    constructionTime: '2016-12-29 10:00:00'
  },
  {
    id: 34,
    title: '杭州湾跨海大桥',
    category: 'road',
    longitude: 121.271617,
    latitude: 30.338494,
    endLongitude: 121.87428,
    endLatitude: 30.182327,
    content: '全长36公里，连接嘉兴和宁波，是中国最长的跨海大桥之一',
    constructionTime: '2008-05-01 09:00:00'
  },
  {
    id: 35,
    title: '青岛海湾大桥',
    category: 'road',
    longitude: 120.332414,
    latitude: 36.067427,
    endLongitude: 120.035278,
    endLatitude: 35.9375,
    content: '全长36.48公里，连接青岛和黄岛，2011年通车',
    constructionTime: '2011-06-30 10:00:00'
  },
  {
    id: 36,
    title: '舟山跨海大桥',
    category: 'road',
    longitude: 122.116389,
    latitude: 30.016667,
    endLongitude: 122.25,
    endLatitude: 30.133333,
    content: '全长48.16公里，连接舟山本岛和宁波，由5座大桥组成',
    constructionTime: '2009-12-25 09:00:00'
  },
  {
    id: 37,
    title: '京沪高铁',
    category: 'road',
    longitude: 116.479008,
    latitude: 39.937923,
    endLongitude: 121.506662,
    endLatitude: 31.245853,
    content: '全长1318公里，设计时速350公里，连接北京和上海，2011年通车',
    constructionTime: '2011-06-30 15:00:00'
  },
  {
    id: 38,
    title: '沪昆高铁',
    category: 'road',
    longitude: 121.506662,
    latitude: 31.245853,
    endLongitude: 102.71783,
    endLatitude: 30.657876,
    content: '全长2266公里，设计时速350公里，连接上海和昆明',
    constructionTime: '2016-12-28 08:00:00'
  },
  {
    id: 39,
    title: '哈大高铁',
    category: 'road',
    longitude: 126.636881,
    latitude: 45.800002,
    endLongitude: 121.613661,
    endLatitude: 38.913372,
    content: '全长921公里，设计时速350公里，连接哈尔滨、长春和大连',
    constructionTime: '2012-12-01 09:00:00'
  },
  {
    id: 40,
    title: '兰新高铁',
    category: 'road',
    longitude: 103.835443,
    latitude: 36.061231,
    endLongitude: 87.627647,
    endLatitude: 43.832451,
    content: '全长1776公里，设计时速250公里，连接兰州和乌鲁木齐，世界上一次性建成里程最长的高铁',
    constructionTime: '2014-12-26 10:00:00'
  },
  {
    id: 41,
    title: '西成高铁',
    category: 'road',
    longitude: 108.946466,
    latitude: 34.259611,
    endLongitude: 104.06783,
    endLatitude: 30.657876,
    content: '全长658公里，设计时速250公里，连接西安和成都，穿越秦岭',
    constructionTime: '2017-12-06 08:00:00'
  },
  {
    id: 42,
    title: '广深港高铁',
    category: 'road',
    longitude: 113.324498,
    latitude: 23.109113,
    endLongitude: 114.173336,
    endLatitude: 22.320048,
    content: '全长141公里，设计时速350公里，连接广州、深圳和香港',
    constructionTime: '2018-09-23 07:00:00'
  },
  {
    id: 43,
    title: '北京大兴国际机场',
    category: 'building',
    longitude: 116.414487,
    latitude: 39.503373,
    content: '世界最大单体航站楼，总面积140万平方米，2019年通航',
    constructionTime: '2019-09-25 16:00:00'
  },
  {
    id: 44,
    title: '上海浦东国际机场',
    category: 'building',
    longitude: 121.808042,
    latitude: 31.140314,
    content: '中国三大门户复合枢纽之一，2000年通航，拥有全球最大的单体卫星厅',
    constructionTime: '1999-09-16 10:00:00'
  },
  {
    id: 45,
    title: '广州白云国际机场',
    category: 'building',
    longitude: 113.307312,
    latitude: 23.397236,
    content: '中国三大门户复合枢纽之一，2004年通航，2020年扩建完成',
    constructionTime: '2004-08-05 09:00:00'
  },
  {
    id: 46,
    title: '深圳宝安国际机场',
    category: 'building',
    longitude: 113.823443,
    latitude: 22.647091,
    content: '中国南部航空枢纽，2013年新航站楼启用，设计年旅客吞吐量4500万人次',
    constructionTime: '1991-10-01 10:00:00'
  },
  {
    id: 47,
    title: '成都天府国际机场',
    category: 'building',
    longitude: 104.53416,
    latitude: 30.38527,
    content: '成都第二国际机场，总面积71万平方米，2021年通航',
    constructionTime: '2021-06-27 11:00:00'
  },
  {
    id: 48,
    title: '重庆江北国际机场',
    category: 'building',
    longitude: 106.659337,
    latitude: 29.70847,
    content: '中国西部航空枢纽，2017年T3航站楼启用，设计年旅客吞吐量7000万人次',
    constructionTime: '1990-01-22 09:00:00'
  },
  {
    id: 49,
    title: '武汉天河国际机场',
    category: 'building',
    longitude: 114.129753,
    latitude: 30.775422,
    content: '华中地区航空枢纽，2017年T3航站楼启用，设计年旅客吞吐量3500万人次',
    constructionTime: '1995-04-15 10:00:00'
  },
  {
    id: 50,
    title: '西安咸阳国际机场',
    category: 'building',
    longitude: 108.746739,
    latitude: 34.470328,
    content: '中国西北航空枢纽，2020年三期扩建工程开工，设计年旅客吞吐量8300万人次',
    constructionTime: '1991-09-01 09:00:00'
  }
]
    }
  },
  computed: {
    totalInfrastructureCount() {
      return this.mockData.length
    },
    
    currentMarkers() {
      let data = this.mockData.filter(item => item.category === 'building')
      
      // 如果设置了时间范围，进行筛选
      if (this.showComparison && this.timeRange1 && this.timeRange1.length === 2) {
        // 对比模式：只显示第一时间范围
        data = data.filter(item => {
          const time = new Date(item.constructionTime).getTime()
          return time >= new Date(this.timeRange1[0]).getTime() && 
                 time <= new Date(this.timeRange1[1]).getTime()
        })
      } else if (!this.showComparison) {
        // 合并模式：合并两个时间范围
        if (this.timeRange1 && this.timeRange1.length === 2) {
          data = data.filter(item => {
            const time = new Date(item.constructionTime).getTime()
            return time >= new Date(this.timeRange1[0]).getTime() && 
                   time <= new Date(this.timeRange1[1]).getTime()
          })
        }
        if (this.timeRange2 && this.timeRange2.length === 2) {
          const range2Data = this.mockData
            .filter(item => item.category === 'building')
            .filter(item => {
              const time = new Date(item.constructionTime).getTime()
              return time >= new Date(this.timeRange2[0]).getTime() && 
                     time <= new Date(this.timeRange2[1]).getTime()
            })
          // 合并数据并去重
          const mergedData = [...data, ...range2Data]
          const uniqueData = mergedData.filter((item, index, self) =>
            index === self.findIndex((t) => t.id === item.id)
          )
          data = uniqueData
        }
      }
      
      return data.map(item => ({
        name: item.title,
        coords: [item.longitude, item.latitude],
        type: 'building',
        value: 100,
        info: item.content
      }))
    },
    
    currentRoutes() {
      let data = this.mockData.filter(item => item.category === 'road')
      
      // 如果设置了时间范围，进行筛选
      if (this.showComparison && this.timeRange2 && this.timeRange2.length === 2) {
        // 对比模式：只显示第二时间范围
        data = data.filter(item => {
          const time = new Date(item.constructionTime).getTime()
          return time >= new Date(this.timeRange2[0]).getTime() && 
                 time <= new Date(this.timeRange2[1]).getTime()
        })
      } else if (!this.showComparison) {
        // 合并模式：合并两个时间范围
        if (this.timeRange1 && this.timeRange1.length === 2) {
          data = data.filter(item => {
            const time = new Date(item.constructionTime).getTime()
            return time >= new Date(this.timeRange1[0]).getTime() && 
                   time <= new Date(this.timeRange1[1]).getTime()
          })
        }
        if (this.timeRange2 && this.timeRange2.length === 2) {
          const range2Data = this.mockData
            .filter(item => item.category === 'road')
            .filter(item => {
              const time = new Date(item.constructionTime).getTime()
              return time >= new Date(this.timeRange2[0]).getTime() && 
                     time <= new Date(this.timeRange2[1]).getTime()
            })
          // 合并数据并去重
          const mergedData = [...data, ...range2Data]
          const uniqueData = mergedData.filter((item, index, self) =>
            index === self.findIndex((t) => t.id === item.id)
          )
          data = uniqueData
        }
      }
      
      return data.map(item => ({
        name: item.title,
        from: [item.longitude, item.latitude],
        to: [item.endLongitude, item.endLatitude],
        type: 'highway',
        info: item.content
      }))
    },

    buildingCount() {
      return this.currentMarkers.length
    },

    roadCount() {
      return this.currentRoutes.length
    },

    filteredRange1Count() {
      if (!this.timeRange1 || this.timeRange1.length !== 2) return 0
      return this.mockData.filter(item => {
        const time = new Date(item.constructionTime).getTime()
        return time >= new Date(this.timeRange1[0]).getTime() && 
               time <= new Date(this.timeRange1[1]).getTime()
      }).length
    },

    filteredRange2Count() {
      if (!this.timeRange2 || this.timeRange2.length !== 2) return 0
      return this.mockData.filter(item => {
        const time = new Date(item.constructionTime).getTime()
        return time >= new Date(this.timeRange2[0]).getTime() && 
               time <= new Date(this.timeRange2[1]).getTime()
      }).length
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      
      let filteredData = this.mockData
      if (this.categoryFilter !== 'all') {
        filteredData = this.mockData.filter(item => item.category === this.categoryFilter)
      }
      
      // 模拟分页
      const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      const end = start + this.queryParams.pageSize
      this.infrastructureList = filteredData.slice(start, end)
      this.total = filteredData.length
      
      setTimeout(() => {
        this.loading = false
      }, 300)
    },

    handleTimeRange1Change() {
      console.log('[v0] 第一时间范围改变:', this.timeRange1)
      this.$message.success(`第一时间范围已更新，筛选到 ${this.filteredRange1Count} 项数据`)
    },

    handleTimeRange2Change() {
      console.log('[v0] 第二时间范围改变:', this.timeRange2)
      this.$message.success(`第二时间范围已更新，筛选到 ${this.filteredRange2Count} 项数据`)
    },

    toggleComparison() {
      this.showComparison = !this.showComparison
      if (this.showComparison) {
        this.$message.success('已切换到对比显示模式：第一时间范围显示建筑，第二时间范围显示道路')
      } else {
        this.$message.success('已切换到合并显示模式：两个时间范围的数据合并展示')
      }
    },

    clearTimeRanges() {
      this.timeRange1 = []
      this.timeRange2 = []
      this.$message.info('已清空所有时间范围筛选')
    },

    handleCategoryFilter() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    tableRowClassName({ row }) {
      if (row.category === 'building') {
        return 'building-row'
      } else if (row.category === 'road') {
        return 'road-row'
      }
      return ''
    },

    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },

    handleAdd() {
      this.resetForm()
      this.dialogVisible = true
      this.dialogTitle = '添加坐标'
    },

    handleUpdate(row) {
      this.form = { ...row }
      this.dialogVisible = true
      this.dialogTitle = '修改坐标'
    },

    handleDelete(row) {
      this.$confirm(`确定要删除"${row.title}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.mockData.findIndex(item => item.id === row.id)
        if (index !== -1) {
          this.mockData.splice(index, 1)
          this.$message.success('删除成功')
          this.getList()
        }
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    handleCategoryChange(value) {
      if (value === 'building') {
        // 切换到建筑时，清空终点坐标
        this.form.endLongitude = null
        this.form.endLatitude = null
      }
    },

    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.category === 'road' && (!this.form.endLongitude || !this.form.endLatitude)) {
            this.$message.error('道路类型需要填写终点坐标')
            return
          }

          if (this.form.id) {
            const index = this.mockData.findIndex(item => item.id === this.form.id)
            if (index !== -1) {
              this.mockData[index] = { ...this.form }
              this.$message.success('修改成功')
            }
          } else {
            // 新增
            const newItem = {
              ...this.form,
              id: this.mockData.length + 1
            }
            this.mockData.push(newItem)
            this.$message.success('添加成功')
          }

          this.dialogVisible = false
          this.getList()
        }
      })
    },

    cancel() {
      this.dialogVisible = false
      this.resetForm()
    },

    resetForm() {
      this.form = {
        id: null,
        title: '',
        category: 'building',
        longitude: null,
        latitude: null,
        endLongitude: null,
        endLatitude: null,
        content: '',
        constructionTime: ''
      }
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.infrastructure-visualization {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #030b1f 0%, #0a1e3a 50%, #030b1f 100%);
  padding: 20px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 20% 30%, rgba(30, 144, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 70%, rgba(30, 144, 255, 0.08) 0%, transparent 50%);
    animation: pulse 8s ease-in-out infinite;
    pointer-events: none;
  }

  @keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
  }
}

.iv_header {
  width: 98%;
  margin: 0 auto 20px;
  height: 110px;
  background-image: url("../../assets/images/lsdrsj_bg.png");
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;
  z-index: 1;
  
  .ivh_left {
    float: left;
    height: 100%;
    
    .title {
      font-family: SourceHanSansSC-Bold;
      font-size: 16px;
      color: #ffffff;
      letter-spacing: 0;
      padding-left: 30px;
      padding-top: 20px;
    }
    
    .nums {
      font-family: LetsgoDigital-Regular;
      font-size: 36px;
      color: #ffffff;
      letter-spacing: 0;
      text-shadow: 0 2px 5px rgba(2, 0, 70, 0.5);
      font-weight: 700;
      margin-top: 10px;
      padding-left: 30px;
      background-image: linear-gradient(to bottom, #ffffff, #ccd8f2, #3869cc);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      animation: numberGlow 2s ease-in-out infinite;
    }
  }
  
  .ivh_right {
    float: right;
  }
}

@keyframes numberGlow {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.3); }
}

.time-range-section {
  width: 98%;
  margin: 0 auto 20px;
  padding: 25px;
  background: rgba(27, 126, 242, 0.12);
  border: 1px solid rgba(30, 144, 255, 0.4);
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 30px;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 15px rgba(30, 144, 255, 0.15);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #1e90ff, transparent);
  }

  .time-range-item {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;

    .range-label {
      font-size: 14px;
      color: #ffffff;
      white-space: nowrap;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 5px;

      i {
        color: #1e90ff;
      }
    }

    .time-picker {
      flex: 1;
          background: transparent;
    border: 1px solid #ffffff1f;
    }

    .data-count-tag {
      flex-shrink: 0;
      font-weight: 600;
    }
  }

  .time-comparison-switch {
    flex-shrink: 0;
    display: flex;
    gap: 10px;
  }
}

.map-container {
  width: 98%;
  height: 600px;
  margin: 0 auto 30px;
  background: rgba(10, 30, 58, 0.5);
  border: 1px solid rgba(30, 144, 255, 0.4);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(30, 144, 255, 0.25);
  position: relative;
  z-index: 1;

  .map-status-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(10, 30, 58, 0.9);
    border: 1px solid rgba(30, 144, 255, 0.5);
    border-radius: 8px;
    padding: 12px 18px;
    z-index: 10;
    display: flex;
    gap: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);

    .status-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        animation: blink 2s ease-in-out infinite;

        &.building-dot {
          background: #00ffff;
          box-shadow: 0 0 10px #00ffff;
        }

        &.road-dot {
          background: #10b981;
          box-shadow: 0 0 10px #10b981;
        }
      }

      .status-text {
        color: #ffffff;
        font-size: 13px;
        font-weight: 500;
      }

      .status-badge {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: #ffffff;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.4);
      }
    }

    @keyframes blink {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.4; }
    }
  }
}

.data-table-section {
  width: 98%;
  margin: 0 auto;
  background: rgba(27, 126, 242, 0.12);
  border: 1px solid rgba(30, 144, 255, 0.4);
  border-radius: 10px;
  padding: 25px;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 15px rgba(30, 144, 255, 0.15);

  .table-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      font-size: 18px;
      color: #ffffff;
      font-weight: 600;
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #1e90ff;
      }
    }

    .table-filters {
      display: flex;
      gap: 15px;
    }
  }
}

.title-with-icon {
  display: flex;
  align-items: center;
  gap: 6px;

  i {
    color: #1e90ff;
  }
}

.coords-display {
  .coord-item {
    font-size: 12px;
    margin: 2px 0;

    .coord-label {
      color: rgba(255, 255, 255, 0.6);
      margin-right: 4px;
    }

    .coord-value {
      color: #00ffff;
      font-family: monospace;
    }
  }
}

.public_button {
  display: inline-block;
  padding: 8px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  
  &.search_btn {
    background: linear-gradient(135deg, #1e90ff 0%, #0066cc 100%);
    color: #ffffff;
    border: 1px solid #1e90ff;
    
    &:hover {
      background: linear-gradient(135deg, #0066cc 0%, #004d99 100%);
      box-shadow: 0 4px 15px rgba(30, 144, 255, 0.4);
      transform: translateY(-2px);
    }
  }
  
  &.clear_button {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.3);
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.position_add_btn {
  margin-right: 30px;
  margin-top: 32px;
}

.position_btn {
  margin-right: 10px;
  margin-bottom: 10px;
}

.mr10 {
  margin-right: 10px;
}

.fr {
  float: right;
}

.edit-btn {
  color: #1e90ff !important;

  &:hover {
    color: #00a8ff !important;
  }
}

.delete-btn {
  color: #f56c6c !important;

  &:hover {
    color: #ff4444 !important;
  }
}
</style>

<style lang="scss">
.infrastructure-dialog {
  .el-dialog {
    background: linear-gradient(180deg, #0a1e3a 0%, #030b1f 100%);
    border: 1px solid rgba(30, 144, 255, 0.4);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);

    .el-dialog__header {
      border-bottom: 1px solid rgba(30, 144, 255, 0.3);
      padding: 20px;

      .el-dialog__title {
        color: #ffffff;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .el-dialog__body {
      padding: 30px 20px;
    }

    .el-dialog__footer {
      border-top: 1px solid rgba(30, 144, 255, 0.3);
      padding: 15px 20px;
    }
  }

  .dialogTitle {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin-bottom: 25px;
    line-height: 1.6;
    display: flex;
    align-items: flex-start;
    gap: 8px;

    i {
      color: #1e90ff;
      margin-top: 2px;
    }
  }

  .dialog_form_style {
    .el-form-item__label {
      color: #ffffff;
      font-weight: 500;
    }

    .el-input__inner,
    .el-textarea__inner {
      background: rgba(27, 126, 242, 0.12);
      border: 1px solid rgba(30, 144, 255, 0.3);
      color: #ffffff;

      &::placeholder {
        color: rgba(255, 255, 255, 0.3);
      }

      &:focus {
        border-color: #1e90ff;
        box-shadow: 0 0 10px rgba(30, 144, 255, 0.3);
      }
    }

    .el-input-group__append {
      background: rgba(30, 144, 255, 0.2);
      border: 1px solid rgba(30, 144, 255, 0.3);
      color: #1e90ff;
    }

    .el-radio {
      color: #ffffff;

      &.is-bordered {
        border-color: rgba(30, 144, 255, 0.3);
        background: rgba(27, 126, 242, 0.08);
        padding: 12px 20px;
        margin-right: 15px;
        display: flex;
        float: left;

        &:hover {
          border-color: #1e90ff;
        }

        &.is-checked {
          border-color: #1e90ff;
          background: rgba(30, 144, 255, 0.2);
        }
      }
    }

    .el-radio__label {
      color: #ffffff;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .el-radio__input.is-checked + .el-radio__label {
      color: #00ffff;
    }
  }

  .coordinate-section {
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(30, 144, 255, 0.08);
    border: 1px solid rgba(30, 144, 255, 0.2);
    border-radius: 8px;

    .section-title {
      color: #ffffff;
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 8px;

      i {
        color: #1e90ff;
      }
    }

    .el-form-item {
      margin-bottom: 15px;
    }
  }

  .category-hint {
    margin-top: 10px;

    .hint-text {
      color: rgba(255, 255, 255, 0.6);
      font-size: 13px;
      display: flex;
      align-items: center;
      gap: 6px;

      i {
        color: #1e90ff;
      }
    }
  }
}

.data-table-section {
  .el-table {
    background: transparent;
    color: #ffffff;

    &::before {
      display: none;
    }

    th {
      background: rgba(30, 144, 255, 0.18);
      color: #ffffff;
      border-color: rgba(30, 144, 255, 0.25);
      font-weight: 600;
    }

    tr {
      background: transparent;

      &.building-row {
        background: rgba(0, 255, 255, 0.03);
      }

      &.road-row {
        background: rgba(16, 185, 129, 0.03);
      }

      &:hover > td {
        background: rgba(30, 144, 255, 0.12) !important;
      }
    }

    td {
      border-color: rgba(30, 144, 255, 0.15);
    }

    .cell {
      color: #ffffff;
    }
  }

  .el-table__empty-text {
    color: rgba(255, 255, 255, 0.6);
  }

  .el-radio-group {
    .el-radio-button__inner {
      background: rgba(27, 126, 242, 0.12);
      border-color: rgba(30, 144, 255, 0.3);
      color: #ffffff;

      &:hover {
        color: #00ffff;
      }
    }

    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: linear-gradient(135deg, #1e90ff, #0066cc);
      border-color: #1e90ff;
      box-shadow: 0 2px 10px rgba(30, 144, 255, 0.4);
    }
  }
}

.el-date-editor {
  .el-range-separator {
    color: #ffffff;
  }

  .el-range-input {
    background: transparent;
    color: #ffffff;

    &::placeholder {
      color: rgba(255, 255, 255, 0.4);
    }
  }

  .el-range__icon {
    color: #1e90ff;
  }
}

.el-zoom-in-top-enter-active,
.el-zoom-in-top-leave-active {
  opacity: 1;
  transform: scaleY(1);
  transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1), opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
  transform-origin: center top;
}

.el-zoom-in-top-enter,
.el-zoom-in-top-leave-active {
  opacity: 0;
  transform: scaleY(0);
}
</style>
