import request from '@/utils/request'
// ***********************人工填报接口
// 人工填报记录分页查询
export function datain_record_list(query) {
  return request({
    url: '/dataaccess/manual/list',
    method: 'get',
    params: query
  })
}

// 人工填报查询单条（详情） 查询详细
export function getrecord(manualId) {
  return request({
    url: '/dataaccess/manual/' + manualId,
    method: 'get'
  })
}

// 人工填报 新增填报记录
export function addrecord(data) {
  return request({
    url: '/dataaccess/manual/add',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

//人工填报编辑接口  继续编辑填报记录
export function updaterecord(data) {
  return request({
    url: '/dataaccess/manual/',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 人工填报删除接口
export function delrecord(manualId) {
  return request({
    url: '/dataaccess/manual/' + manualId,
    method: 'delete'
  })
}
// 导出接口
// export function exports(data) {
//   return request({
//     url: '/dataaccess/manual/export',
//     method: 'post',
//     // ids: data

//   })
// }
// 人工填报暂存接口也调用新增接口，暂存flag是2，提交flag是1
// ************************数据融合接口
// 分页查询
export function datain_dataFusion_list(query) {
  return request({
    url: '/dataaccess/dataFusion/list',
    method: 'get',
    params: query
  })
}
// 根据id删除记录
export function delrecord_dataFusion(manualId) {
  return request({
    url: '/dataaccess/dataFusion/' + manualId,
    method: 'delete'
  })
}
// 上传文件接口参考开源引接
// 导出已在界面固定
// 新增接口
//dataaccess/manual/add
export function addrecord_dataFusion(data) {
  return request({
    url: '/dataaccess/dataFusion/add',
    method: 'post',
    data: data
  })
}
// 查询单条接口
//  /dataaccess/manual/{id}
export function getrecord_dataFusion(manualId) {
  return request({
    url: '/dataaccess/dataFusion/' + manualId,
    method: 'get'
  })
}
// 编辑接口
export function updaterecord_dataFusion(data) {
  return request({
    url: '/dataaccess/dataFusion/',
    method: 'put',
    data: data
  })
}
// 激活、下线、开始执行接口 传不同flag
// 更新调度接口
// 运行日志接口





