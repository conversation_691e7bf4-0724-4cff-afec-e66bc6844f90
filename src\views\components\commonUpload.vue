<template>
  <div class="file-upload-container">
    <!-- 上传区域  -->
    <div
      class="upload-area"
      :class="{ 'is-dragover': isDragover, 'is-disabled': disabled }"
      @drop.prevent="handleDrop"
      @dragover.prevent="handleDragover"
      @dragleave.prevent="handleDragleave"
      @click="handleClick"
    >
      <input
        ref="fileInput"
        type="file"
        :multiple="multiple"
        :accept="acceptTypes"
        :disabled="disabled"
        @change="handleFileChange"
        style="display: none"
      />

      <div class="upload-content">
        <div class="upload-icon-wrapper">
          <svg
            class="upload-icon"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M7 10L12 5L17 10"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M12 5V15"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
            />
            <path
              d="M20 15V19C20 19.5304 19.7893 20.0391 19.4142 20.4142C19.0391 20.7893 18.5304 21 18 21H6C5.46957 21 4.96086 20.7893 4.58579 20.4142C4.21071 20.0391 4 19.5304 4 19V15"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>

        <div class="upload-text">
          <p class="upload-title">
            {{ isDragover ? "释放文件开始上传" : "点击或拖拽文件到此处上传" }}
          </p>
          <p class="upload-hint">
            支持 {{ fileTypeText }} 格式，单个文件不超过 {{ fileSize }}MB
          </p>
        </div>
      </div>
    </div>

    <p style="color: #fff">已上传文件 （{{ fileList.length }}）</p>
    <transition-group
      name="file-list"
      tag="div"
      class="file-list"
      v-if="fileList.length > 0"
    >
      <div v-for="(file, index) in fileList" :key="file.uid" class="file-item">
        <div class="file-info">
          <div class="file-icon" :class="getFileIconClass(file.name)">
            <svg
              v-if="getFileType(file.name) === 'excel'"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M14 2V8H20"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M10 12L14 16M14 12L10 16"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <svg
              v-else-if="getFileType(file.name) === 'zip'"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M14 2V8H20"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12 11V17M10 13H14M10 15H14"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
            <svg
              v-else
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M14 2V8H20"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>

          <div class="file-details">
            <p class="file-name" :title="file.name">{{ file.name }}</p>
            <p class="file-size">{{ formatFileSize(file.size) }}</p>
          </div>
        </div>

        <div class="file-actions">
          <button
            type="button"
            class="action-btn download-btn"
            @click="handleDownload(file)"
            title="下载"
          >
            <svg
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M7 10L12 15L17 10"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M12 15V3"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>

          <button
            type="button"
            class="action-btn delete-btn"
            @click="handleDelete(index)"
            title="删除"
          >
            <svg
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M3 6H5H21"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
export default {
  name: "FileUpload",
  props: {
    // v-model 绑定的文件列表
    value: {
      type: Array,
      default: () => [],
    },
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: true,
    },
    // 允许的文件类型
    fileType: {
      type: Array,
      default: () => ["xls", "xlsx", "json", "csv", "zip", "txt"],
    },
    // 单个文件大小限制(MB)
    fileSize: {
      type: Number,
      default: 10,
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 上传接口地址
    uploadUrl: {
      type: String,
      default: "/api/upload",
    },
  },
  data() {
    return {
      fileList: [],
      isDragover: false,
      uploadIndex: 0,
    };
  },
  computed: {
    acceptTypes() {
      return this.fileType.map((type) => `.${type}`).join(",");
    },
    fileTypeText() {
      return this.fileType.join("、");
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.fileList = val.map((item, index) => ({
            ...item,
            uid: item.uid || `file-${Date.now()}-${index}`,
          }));
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleClick() {
      if (!this.disabled) {
        this.$refs.fileInput.click();
      }
    },
    handleFileChange(e) {
      const files = Array.from(e.target.files);
      this.processFiles(files);
      e.target.value = "";
    },
    handleDrop(e) {
      if (this.disabled) return;
      this.isDragover = false;
      const files = Array.from(e.dataTransfer.files);
      this.processFiles(files);
    },
    handleDragover() {
      if (!this.disabled) {
        this.isDragover = true;
      }
    },
    handleDragleave() {
      this.isDragover = false;
    },
    processFiles(files) {
      files.forEach((file) => {
        // 验证文件类型
        const fileExt = file.name.split(".").pop().toLowerCase();
        if (!this.fileType.includes(fileExt)) {
          this.$message.error(`不支持 .${fileExt} 格式的文件`);
          return;
        }

        // 验证文件大小
        const fileSizeMB = file.size / 1024 / 1024;
        if (fileSizeMB > this.fileSize) {
          this.$message.error(`文件 ${file.name} 大小超过 ${this.fileSize}MB`);
          return;
        }

        // 添加到文件列表
        const fileObj = {
          uid: `file-${Date.now()}-${this.uploadIndex++}`,
          name: file.name,
          size: file.size,
          raw: file,
          url: URL.createObjectURL(file),
        };

        this.fileList.push(fileObj);
        this.emitChange();
      });
    },
    handleDownload(file) {
      // 如果文件有 URL,直接下载
      if (file.url) {
        const link = document.createElement("a");
        link.href = file.url;
        link.download = file.name;
        link.click();
      } else if (file.raw) {
        // 如果有原始文件对象,创建临时 URL 下载
        const url = URL.createObjectURL(file.raw);
        const link = document.createElement("a");
        link.href = url;
        link.download = file.name;
        link.click();
        URL.revokeObjectURL(url);
      } else {
        this.$message.warning("文件不可下载");
      }
    },
    handleDelete(index) {
      this.$confirm("确定要删除该文件吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.fileList.splice(index, 1);
          this.emitChange();
          this.$message.success("删除成功");
        })
        .catch(() => {});
    },
    emitChange() {
      this.$emit("input", this.fileList);
      this.$emit("change", this.fileList);
    },
    getFileType(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      if (["xls", "xlsx"].includes(ext)) return "excel";
      if (["zip", "rar", "7z"].includes(ext)) return "zip";
      if (["txt", "json", "csv"].includes(ext)) return "text";
      return "file";
    },
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },
    formatFileSize(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toFixed(2) + " " + sizes[i];
    },
  },
};
</script>

<style scoped>
.file-upload-container {
  width: 100%;
}

.upload-area {
  position: relative;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  /* background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%); */
  /* background: rgba(1,35,102,0.70);
border: 1px solid rgba(17,80,154,1); */
  /* box-shadow: inset 0px 0px 12px 0px rgba(13,187,236,0.4); */
  /* border-radius: 10px; */

  background: rgba(27, 126, 242, 0.1);
  border: 1px solid rgba(17, 80, 154, 1);
  border-radius: 10px;

  padding: 48px 24px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.upload-area::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(64, 158, 255, 0.05) 0%,
    rgba(103, 194, 58, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-area:hover {
  border-color: #409eff;
  background: rgba(1, 35, 102, 0.7);
  transform: translateY(-2px);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
}

.upload-area:hover::before {
  opacity: 1;
}

.upload-area.is-dragover {
  border-color: #67c23a;
  background: linear-gradient(135deg, #f0f9ff 0%, #e8f8f5 100%);
  transform: scale(1.02);
  box-shadow: 0 12px 32px rgba(103, 194, 58, 0.2);
}

.upload-area.is-dragover::before {
  opacity: 1;
  background: linear-gradient(
    135deg,
    rgba(103, 194, 58, 0.08) 0%,
    rgba(64, 158, 255, 0.08) 100%
  );
}

.upload-area.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: #f5f7fa;
}

.upload-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.upload-icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.25);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.upload-icon {
  width: 40px;
  height: 40px;
  color: white;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.upload-area:hover .upload-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 32px rgba(64, 158, 255, 0.35);
}

.upload-text {
  text-align: center;
}

.upload-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
  letter-spacing: 0.3px;
}

.upload-hint {
  font-size: 13px;
  color: #909399;
  margin: 0;
  line-height: 1.6;
}

.file-list {
  /* margin-top: 24px; */
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 20px;
  /* background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border: 1px solid #e4e7ed;
  border-radius: 10px; */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  background: rgba(27, 126, 242, 0.1);
  border: 1px solid rgba(17, 80, 154, 1);
  border-radius: 10px;
}

.file-item:hover {
  background: rgba(27, 126, 242, 0.1) !important;
  border-color: #409eff;
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.file-icon svg {
  width: 24px;
  height: 24px;
}

.file-icon-excel {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.file-icon-zip {
  background: linear-gradient(135deg, #e6a23c 0%, #f0c78a 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.file-icon-text {
  background: linear-gradient(135deg, #409eff 0%, #79bbff 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.file-icon-file {
  background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.3);
}

.file-item:hover .file-icon {
  transform: scale(1.1) rotate(5deg);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #ffffff;
  margin: 0;
}

.file-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #f5f7fa;
}

.action-btn svg {
  width: 18px;
  height: 18px;
}

.download-btn {
  color: #409eff;
}

.download-btn:hover {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.delete-btn {
  color: #f56c6c;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #f56c6c 0%, #f89898 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}

/* 列表动画 */
.file-list-enter-active {
  animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-list-leave-active {
  animation: slideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(20px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-area {
    padding: 32px 16px;
  }

  .upload-icon-wrapper {
    width: 64px;
    height: 64px;
  }

  .upload-icon {
    width: 32px;
    height: 32px;
  }

  .file-item {
    padding: 12px 16px;
  }

  .file-icon {
    width: 36px;
    height: 36px;
  }

  .file-icon svg {
    width: 20px;
    height: 20px;
  }
}
</style>