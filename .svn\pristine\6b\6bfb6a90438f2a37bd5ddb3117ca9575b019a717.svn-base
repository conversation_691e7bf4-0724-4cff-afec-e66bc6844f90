<template>
  <div class="app-container">
    <el-row :gutter="20" class="main-row">
      <el-col :span="6" class="left-panel">
        <el-card shadow="always" class="left-card">
          <div class="history_log">请选择主题</div>
          <el-select v-model="currentTopicId" placeholder="请选择主题" @change="handleTopicChange" style="width:356px"
            class="blue">
            <el-option v-for="topic in topicOptions" :key="topic.id" :label="topic.categoryName" :value="topic.id" />
          </el-select>
          <h4 class="history_log">专题列表</h4>
          <el-input v-model="searchTopic" placeholder="搜索专题名称" clearable size="small" prefix-icon="el-icon-search"
            class="blue" @keyup.enter="fetchThematicList" @input="handleSearchInput" />
          <div class="topic-list-wrapper">
            <div v-for="topic in thematicList" :key="topic.id" class="topic-item"
              :class="{ 'topic-item-active': currentThematicId === topic.id }" @click="handleThematicSelect(topic)">
              <div class="topic-info">
                <div class="topic-name">{{ topic.categoryName }}</div>
                <div class="topic-count">{{ topic.count || 0 }} 条数据</div>
              </div>
            </div>
            <div class="topic-empty" v-if="thematicList.length === 0">
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="18" class="right-panel">
        <div class="page-header">
          <!-- <h3 class="page-title">{{ topic.categoryName }}</h3> -->
          <h3 class="page-title">{{ topicOptions[0].categoryName }}</h3>
          <div class="page-tabs">
            <span class="tab-item" @click="to_index">趋势总览</span>
            <span class="tab-item active">可视化时间轴</span>
            <!-- <span class="tab-item" @click="to_gjjs">高级检索</span> -->
            <!-- <span class="tab-item" @click="to_ztbg">专题报告</span> -->
          </div>
        </div>

        <div class="vision_time_line">
          <div class="timeline-container">
            <div class="timeline-header">
              <!-- <h3>项目时间轴</h3> -->

              <div class="button-container">
                <div @click="openAddDialog" class="public_button search_btn position_btn">
                  <i class="mr10 el-icon-plus"></i>添加阶段
                </div>
                <div class="public_button clear_button position_btn" @click="autoGenerateNodes">
                  <i class="mr10 el-icon-refresh"></i>重新汇聚
                </div>
              </div>
              <div class="custom-radio-group">
                <div @click="lineChartType = '展示全部', getnodes(1)"
                  :class="['custom-radio-button', { 'is-active': lineChartType === '展示全部' }]">
                  展示全部
                </div>
                <div @click="lineChartType = '展示主要事件', getnodes(2)"
                  :class="['custom-radio-button', { 'is-active': lineChartType === '展示主要事件' }]">

                  展示主要事件
                </div>
              </div>

            </div>
            <!-- <div class="custom-radio-group">
              <div @click="lineChartType = '展示全部'"
                :class="['custom-radio-button', { 'is-active': lineChartType === '展示全部' }]">
                展示全部
              </div>
              <div @click="lineChartType = '展示主要事件'"
                :class="['custom-radio-button', { 'is-active': lineChartType === '展示主要事件' }]">

                展示主要事件
              </div>
            </div> -->
            <!-- <el-radio-group v-model="lineChartType" size="mini">
                      <el-radio-button label="展示全部">展示全部</el-radio-button>
                      <el-radio-button label="展示主要事件">展示主要事件</el-radio-button>
                    </el-radio-group> -->

            <!-- 新版时间线组件 - 节点居中在线上 -->
            <div class="phase-timeline">
              <div v-for="(phase, phaseIndex) in phaseData" :key="phaseIndex" class="phase-section">
                <div class="phase-header" :style="{ borderLeftColor: phase.color }">
                  <div class="phase-title">{{ phase.name }} ({{ phase.period }})</div>
                  <div v-if="phase.mark" class="stage-indicator">{{ phase.mark }}</div>
                </div>

                <div class="timeline" v-if="phase.events && phase.events.length > 0">
                  <div v-for="(event, eventIndex) in phase.events" :key="eventIndex" class="timeline-item"
                    :class="eventIndex % 2 === 0 ? 'left' : 'right'">
                    <!-- 节点始终在中间线上 -->
                    <div class="timeline-node-container">
                      <div class="timeline-node" :style="{ backgroundColor: phase.color }"></div>
                    </div>
                    <div class="timeline-content" :style="{ borderColor: phase.color }">
                      <el-card class="timeline-card" shadow="hover">
                        <div class="timeline-header-content">
                          <div class="event-date-type">
                            <span class="timeline-date">{{ event.date }}</span>
                            <span class="event-type">{{ event.type }}</span>
                          </div>

                        </div>
                        <h4 class="event-name">{{ event.name }}</h4>
                        <p class="timeline-desc">{{ event.description }}</p>
                      </el-card>
                    </div>
                  </div>
                </div>

                <div v-else class="empty-phase">
                  <i class="el-icon-time empty-icon"></i>
                  <p>暂无事件数据</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 添加时间轴节点对话框 -->
      <el-dialog :title="nodeDialogTitle" :visible.sync="nodeDialogVisible" width="600px" height="340px" append-to-body>
        <el-form ref="nodeForm" :model="nodeForm" :rules="nodeRules" label-width="100px">
          <el-form-item label="阶段名称" prop="type">
            <el-input v-model="nodeForm.type" placeholder="请输入阶段名称" />
          </el-form-item>
          <el-form-item label="起止日期" prop="dateRange">
            <el-date-picker style="width: 440px;" v-model="nodeForm.dateRange" value-format="yyyy-MM-dd"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>

        </el-form>
        <div style="height:50px;">
          <!-- <el-button @click="nodeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitNodeForm">确定</el-button> -->
          <div class="public_button clear_button fr position_btn" @click="nodeDialogVisible = false">
            <i class="mr10 el-icon-close"></i>取 消
          </div>

          <div @click="submitNodeForm" class="public_button search_btn fr position_btn">
            <i class="mr10 el-icon-finished"></i>确定
          </div>
        </div>
      </el-dialog>
    </el-row>
  </div>
</template>

<script>
import commonUpload from "@/views/components/commonUpload.vue";
import {
  selectListBySystemType, // 获取所有主题
  selectThematicListById, // 根据主题查找专题
  selectTopicDetailById, // 根据主题ID获取专题详情
} from "@/api/historyanalysis/index";
// addjd
import {
  addjd,
  queryjd,
} from "@/api/historyanalysis/vision-time-line";
export default {
  components: { commonUpload },
  name: "Role",


  data() {
    return {
      lineChartType: "展示全部",

      // 新版时间轴数据 - 按阶段分组
      phaseData: [
      ],
      // 时间轴对话框相关
      nodeDialogVisible: false,
      nodeDialogTitle: '添加事件发展阶段',
      editingPhaseIndex: -1,
      editingEventIndex: -1,
      nodeForm: {

        type: '',
        dateRange: [],

      },
      nodeRules: {
        dateRange: [{ required: true, message: '请选择起止日期', trigger: 'change' }],
        type: [{ required: true, message: '请输入阶段名称', trigger: 'blur' }],

      },

      // 原有数据保持不变
      datain_record_list: [],
      isExist: true,
      topicOptions: [{
        "id": "",  //专题id
        "categoryName": "暂无",   //专题名称
        "description": "暂无",  //专题描述
        "daily": 2,            // 舆情日报
        "event": 4,                   // 事件信息
        "openSource": 5,              // 开源咨询
        "research": 3,                // 理论研究成果
        "special": 4,                // 舆情专报
        "timespan": "暂无",            //时间跨度
        "timespanStartEnd": "暂无",  //时间跨度开始到结束
        "peakTime": "暂无",             //峰值时间
        "peakTimeCount": "暂无",   //峰值月份事件数量
        "keyPositiveEvents": "暂无",   //主要正面事件名称
        "keyPositiveEventsTime": "暂无",//主要正面事件发生事件
        "keyPositiveEventsPopularity": 123.8,   //主要正面事件热度
        "keyNegativeEvents": "暂无",   //主要负面事件名称
        "keyNegativeEventsTime": "暂无",   //主要负面事件发生事件
        "keyNegativeEventsPopularity": 123.8,  //主要负面事件热度
        // }

      }],
      thematicList: [],
      searchTopic: '',
      currentTopicId: null,
      currentThematicId: null,
      currentThematicName: '',
      typeCount: {
        openSource: 0,
        event: 0,
        daily: 0,
        special: 0,
        research: 0
      },
      activeTab: 'openSource',
      drawer: false,
      previewData: [],
      previewTotal: 0,
      ruleForm: {
        ruleConfigs: [
          {
            fieldName: '',
            operator: '',
            valueList: '',
            id: ''
          }
        ],
        ruleSchedule: {
          frequency: '',
          dayOfWeek: '',
          executeHour: '',
          thematicId: '',
          ruleType: 1,
        }
      },
      fieldOptions: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      operatorOptions: [],
      frequencyTypeOptions: [],
      weekdayOptions: [],
      executeHourOptions: [],
      tableData: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      loading: {
        openSource: false,
        event: false,
        daily: false,
        special: false,
        theory: false
      },
      total: {
        openSource: 0,
        event: 0,
        daily: 0,
        special: 0,
        theory: 0
      },




      openDetailDialog: false,
      open_change: false,
      totalSum: 0,
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      roleList: [],
      title: "",
      open: false,
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      dateRange: [],

      menuOptions: [],
      deptOptions: [],
      queryParams_xq: {
        themeId: undefined,
        mainTopicId: undefined,
        mainCategoryId: undefined,
        mainSentimentId: undefined,
        eventName: undefined,
        countryRegion: undefined,
        locltion: undefined,
        occurrenceTime: undefined,
        duration: undefined,
        eventOverview: undefined,
        source: undefined,
        ourPersonnelOrg: undefined,
        opponentPersonnelOrg: undefined,
        fj: [
          {
            fjid: "",
            fjName: "",
          },
        ],
      },
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        eventName: undefined,
        themeId: undefined,
        mainSentimentId: undefined,
        mainTopicId: undefined,
        mainCategoryId: undefined,
        status: undefined,
      },
      changeid: "",

      defaultProps: {
        children: "children",
        label: "label",
      },

    };
  },
  created() {
    // this.what();
    this.fetchTopicOptions();
    this.fetchFieldOptions();
    this.$nextTick(() => {
      // this.initCharts();
      window.addEventListener("resize", this.handleResize);
    });
  },
  mounted() {

    // this.getnodes(1)
  },
  methods: {
    getnodes(c1) {
      let params = {
        flag: c1,
        id: this.currentThematicId,
      }
      queryjd(params).then(res => {
        console.log(res)
        this.phaseData = res.data
      }
      )

    },
    // 时间轴相关方法
    openAddDialog() {
      this.editingPhaseIndex = -1;
      this.editingEventIndex = -1;
      this.nodeDialogTitle = '添加事件发展阶段';
      this.nodeForm = {
        dateRange: [],
        type: '',

      };
      this.nodeDialogVisible = true;
    },

    submitNodeForm() {
      this.$refs.nodeForm.validate((valid) => {
        if (valid) {
          let params = {
            stageName: this.nodeForm.type,

          };
          addjd(
            this.addDateRange(params, this.nodeForm.dateRange)
          ).then(res => {
            if (res.code === 200) {
              this.$message.success('添加成功');
              this.nodeDialogVisible = false;
              // this.getnodes(1)
            } else {
              this.$message.error('添加失败');
            }
          })
        }

      });
      // this.nodeDialogVisible = false;
    },
    autoGenerateNodes() {
      if (this.lineChartType === '展示全部') {
        this.getnodes(1)
      }
      else {
        this.getnodes(2)
      }

    },



    loadTimelineData(thematicId) {
      // 根据专题ID加载对应的时间轴数据
      console.log('加载专题时间轴数据:', thematicId);
    },

    fetchFieldOptions() {
      // 原有实现
    },

    fetchTopicOptions() {
      const params = { systemType: 1 };
      selectListBySystemType(params)
        .then(response => {
          if (response?.code === 200) {
            this.topicOptions = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名主题'
            }));
            if (this.topicOptions.length > 0) {
              this.currentTopicId = this.topicOptions[0].id;
              this.fetchThematicList();
            }
          } else {
            this.topicOptions = [];
          }
        })
        .catch(error => {
          console.error('获取主题列表失败:', error);
          this.$message.error('获取主题列表失败，请稍后重试');
          this.topicOptions = [];
        });
    },

    handleSearchInput() {
      this.fetchThematicList();
    },

    fetchThematicList() {
      const params = {
        id: this.currentTopicId,
        searchName: this.searchTopic,
        ruleType: ''
      };
      selectThematicListById(params)
        .then(response => {
          if (response?.code === 200) {
            this.thematicList = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名专题',
              count: item.count || 0
            }));
            if (this.thematicList.length > 0) {
              this.currentThematicId = this.thematicList[0].id;
              this.currentThematicName = this.thematicList[0].categoryName;
              this.ruleForm.ruleSchedule.thematicId = this.currentThematicId;
              this.handleThematicSelect(this.thematicList[0]);

            } else {
              this.currentThematicId = null;
              this.currentThematicName = '';
              this.ruleForm.ruleSchedule.thematicId = '';
              this.typeCount = { openSource: 0, event: 0, daily: 0, special: 0, research: 0 };
            }
          } else {
            this.thematicList = [];
            this.currentThematicId = null;
            this.currentThematicName = '';
            this.ruleForm.ruleSchedule.thematicId = '';
          }
        })
        .catch(error => {
          console.error('获取专题列表失败:', error);
          this.$message.error('获取专题列表失败，请稍后重试');
          this.thematicList = [];
          this.currentThematicId = null;
          this.currentThematicName = '';
          this.ruleForm.ruleSchedule.thematicId = '';
        });
    },

    handleTopicChange() {
      this.fetchThematicList();
    },

    async handleThematicSelect(data) {
      this.currentThematicId = data.id;
      this.getnodes(1);

      // const params = {
      //   id: this.currentThematicId,
      // };
      // let response = await selectTopicDetailById(params);
      // if (response.code === 200) {
      //   this.description = response.data.description;
      //   this.daily = response.data.daily;
      //   this.event = response.data.event;
      //   this.openSource = response.data.openSource;
      //   this.research = response.data.research;
      //   this.special = response.data.special;
      //   this.timespan = response.data.timespan;
      //   this.timespanStartEnd = response.data.timespanStartEnd;
      //   this.peakTime = response.data.peakTime;
      //   this.peakTimeCount = response.data.peakTimeCount;
      //   this.keyPositiveEvents = response.data.keyPositiveEvents;
      //   this.keyPositiveEventsTime = response.data.keyPositiveEventsTime;
      //   this.keyPositiveEventsPopularity = response.data.keyPositiveEventsPopularity;
      //   this.keyNegativeEvents = response.data.keyNegativeEvents;
      //   this.keyNegativeEventsTime = response.data.keyNegativeEventsTime;
      //   this.keyNegativeEventsPopularity = response.data.keyNegativeEventsPopularity;
      // }

    },

    to_ksh() {
      this.$router.push("/concract/keypage");
    },
    to_gjjs() {
      this.$router.push("/concract/gjjs");
    },
    to_ztbg() {
      this.$router.push("/concract/ztbg");
    },
    to_index() {
      this.$router.push("/history/index");
    },
    // what() {
    //   console.log(this.dict.type.manual_main_sentiment);
    // },

    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>

<style scoped>
/* 新版时间轴样式 - 左右分布 */
.phase-timeline {
  position: relative;
}

.phase-section {
  margin-bottom: 40px;
}

.phase-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding: 15px 20px;
  /* background: #f8fafc; */
  background: rgba(27, 126, 242, 0.10);
  color: #FFFFFF;

  border-radius: 6px;
  border-left: 4px solid #409EFF;
}

.phase-title {
  font-size: 18px;
  font-weight: bold;
  /* color: #303133; */
  color: #FFFFFF;

}

.stage-indicator {
  display: inline-block;
  background: #f0f7ff;
  color: #409EFF;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 15px;
}

.timeline {
  position: relative;
  padding: 20px 0;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e6e9f0;
  /* background: #66CCFF; */
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.timeline-item.left {
  justify-content: flex-start;
}

.timeline-item.right {
  justify-content: flex-end;
}

.timeline-content {
  width: 45%;
  position: relative;
}


.timeline-item.left .timeline-node {
  right: -8px;
}

.timeline-item.right .timeline-node {
  left: -8px;
}

.timeline-card {
  /* background: white; */

  border: none;
  padding: 15px;
  transition: all 0.3s ease;
  background: rgba(27, 126, 242, 0.10);
  border-radius: 10px;
}



.timeline-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.timeline-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.event-date-type {
  display: flex;
  align-items: center;
  gap: 10px;
}




.vision_time_line {
  padding: 20px;
  /* background:page-header; */
  background: rgba(27, 126, 242, 0.10);
  border-radius: 4px;
  min-height: 500px;
}

.timeline-container {
  max-width: 100%;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  /* border-bottom: 1px solid #ebeef5; */
  margin-left: 600px;
}

.timeline-header h3 {
  margin: 0;
  color: #303133;
  font-size: 20px;
}

.left-card {
  height: 100%;
  background: rgba(27, 126, 242, 0.10);
  border: none;

}

.topic-list-wrapper {
  max-height: 400px;
  overflow-y: auto;
  margin-top: 15px;
}

.topic-item {
  padding: 12px;
  /* border: 1px solid #ebeef5; */
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;

  border: 1px solid rgba(27, 126, 242, 0.5);
    background: rgba(27, 126, 242, 0.10);
}

.topic-item:hover {
  border: 1px solid rgba(27, 126, 242, 0.8); /* 加深边框颜色 */
  background: rgba(27, 126, 242, 0.20); /* 加深背景颜色 */
}

.topic-item-active {
  border: 1px solid rgba(27, 126, 242, 0.8); /* 加深边框颜色 */
  background: rgba(27, 126, 242, 0.20); /* 加深背景颜色 */
}

.topic-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.topic-name {
  /* font-weight: 500; */
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.topic-count {
  font-family: LetsgoDigital-Regular;
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 700;
}

.topic-empty {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline::before {
    left: 30px;
  }

  .timeline-item {
    justify-content: flex-start !important;
  }

  .timeline-content {
    width: calc(100% - 60px);
    margin-left: 40px;
  }



  .phase-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

}

/* 节点容器 - 始终居中 */
.timeline-node-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.timeline-node {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #409EFF;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-content {
  width: 45%;
  position: relative;
}


.timeline-item.left .timeline-card {
  /* border-left: 3px solid #409EFF; */
  border-top: none;
}

.timeline-item.right .timeline-card {
  border-right: 3px solid #409EFF;
  border-top: none;
}

.timeline-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.timeline-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.event-date-type {
  display: flex;
  align-items: center;
  gap: 10px;
}

.timeline-date {
  font-weight: bold;
  color: #409EFF;
  font-size: 14px;
}

.event-type {
  background: #e6f7ff;
  color: #1890ff;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.timeline-actions {
  display: flex;
  gap: 8px;
}

.event-name {
  /* font-weight: bold;
  margin-bottom: 8px;
  font-size: 16px;
  color: #303133; */
  font-family: SourceHanSansSC-Bold;
  font-size: 18px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 700;
}

.timeline-desc {
  /* color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin: 0; */
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #A9C7EA;
  letter-spacing: 0;
  font-weight: 400;
}

.empty-phase {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  background: #fafafa;
  border-radius: 6px;
  border: 1px dashed #dcdfe6;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
  display: block;
}

.left-panel {
  /* background: rgba(27, 126, 242, 0.10); */
  height: 1000px;

}

.history_log {
  font-family: YouSheBiaoTiHei;
  font-size: 20px;
  color: #FFFFFF;
  letter-spacing: 0;
  /* text-shadow: 0 2px 4px #1B1A85; */
  font-weight: 400;
  /*  */
  font-family: SourceHanSansSC-Bold;
  font-size: 20px;
  letter-spacing: 0;
  font-weight: 700;
  background: -webkit-gradient(linear, left top, left bottom, from(#FFFFFF), to(#0091FF));
  background: linear-gradient(to bottom, #FFFFFF, #0091FF);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 5px;
  margin-top: 25px
}

.blue {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  height: 50px !important;
  line-height: 50px !important;
  color: #ffffff !important;
  border: none !important;
}

::v-deep .el-input__inner {

  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  height: 50px !important;
  line-height: 50px !important;
  color: #ffffff !important;
}

/* 美化页面标题，添加渐变效果和标签导航 */
.page-header {
  margin-bottom: 20px;
  /* background: rgba(255, 255, 255, 0.95); */
  padding: 20px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.page-title {

  font-family: SourceHanSansSC-Bold;
  font-size: 24px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 700;
  /* margin: 0 0 16px 0; */
}

.page-tabs {
  display: flex;
  gap: 8px;
  height: 50px;
  /* width:132px; */
}

.tab-item {

  font-family: SourceHanSansSC-Regular;
  font-size: 18px;
  color: #A9C7EA;
  letter-spacing: 0;
  font-weight: 400;
  /* align-items: center; */
  /* text-align: center; */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 20px;
  /* 根据需要调整内边距 */
  cursor: pointer;
}

.tab-item:hover {
  background-image: linear-gradient(179deg, rgba(27, 126, 242, 0.00) 0%, rgba(27, 126, 242, 0.20) 100%);
  border-bottom: #66CCFF 2px solid;

}

.tab-item.active {
  background-image: linear-gradient(179deg, rgba(27, 126, 242, 0.00) 0%, rgba(27, 126, 242, 0.20) 100%);
  border-bottom: #66CCFF 2px solid;

}

.button-container {
  display: flex;
  justify-content: flex-end;
  /* 将子元素推到右侧 */
}

.public_button {

  padding: 10px 20px;
  /* 根据需要设置内边距 */
  margin-left: 10px;
  /* 根据需要设置左边距 */
  cursor: pointer;
  /* 添加鼠标指针样式 */
}

.mr10 {
  margin-right: 10px;
  /* 设置图标右边距 */
}

.custom-radio-group {
  display: inline-flex;
  justify-content: flex-end;
  /* 将子元素推到右侧 */
}

.custom-radio-button {
  padding: 8px 16px;
  /* 根据需要调整内边距 */
  border: 1px solid #dcdcdc;
  /* 设置边框颜色 */

  background-color: #fff;
  /* 设置背景颜色 */
  /* color: #606266; */
  /* 设置文本颜色 */
  cursor: pointer;
  /* 设置鼠标指针样式 */

  /*  */
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  color: #fff;

  /* border-radius: 0px 10px 10px 0px;   */
}

.custom-radio-button.is-active {
  background-color: #409eff;
  /* 设置激活时的背景颜色 */
  color: #fff;
  /* 设置激活时的文本颜色 */
  border-color: #409eff;
  /* 设置激活时的边框颜色 */
  background-image: linear-gradient(180deg, #78B8FF 0%, #002BA9 100%);
  /* border-radius: 10px 0px 0px 10px; */
}

/* 左侧面板 - 固定定位 */
.left-panel {
  position: fixed;
  top: 100px;

  left: 280px;
  width: 22%;
  /* 减小宽度 */
  height: calc(100vh - 60px);
  /* 调整高度以适应新的顶部位置 */
  overflow-y: auto;
  z-index: 1000;
  overflow-y: scroll;

}


/* 右侧面板 - 添加左边距 */
.right-panel {
  margin-left: 25%;
  /* 与左侧宽度相同 */
  width: 75%;
  /* 与原span=18对应 */
  height: 100%;
}
.public_button
 {
    -webkit-box-shadow: inset 0px 0px 12px 0px rgba(204, 237, 247, 0.4);
    box-shadow: inset 0px 0px 12px 0px rgba(204, 237, 247, 0.4);
    border-radius: 10px;
    padding: 10px 17px;
    font-family: SourceHanSansSC-Regular;
    font-size: 16px;
    color: #ffffff;
    letter-spacing: 0;
    font-weight: 400;
    cursor: pointer;
 }
</style>