<template>
  <!-- 人工填报界面 -->
  <div class="app-container">
    <div>历史填报记录</div>
    <div>
      <el-row :gutter="24" type="flex" justify="space-between">
        <el-col :span="12">
          <span>共：{{ totalSum }} 条数据</span>
        </el-col>
        <el-col :span="12" style="text-align: right">
          <!-- <el-button
            type="text"
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
          >
            新增填报
          </el-button> -->
        </el-col>
      </el-row>
    </div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-row :gutter="24">
        <el-col :span="6">
          <el-form-item label="事件名称" prop="eventName">
            <el-input
              v-model="queryParams.eventName"
              placeholder="请输入事件名称"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="事件主题" prop="themeId">
            <el-select
              v-model="queryParams.themeId"
              placeholder="请选择主题"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in dict.type.manual_theme"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="事件专题" prop="mainTopicId">
            <el-select
              v-model="queryParams.mainTopicId"
              placeholder="请选择专题"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in dict.type.manual_main_topic"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="事件类别" prop="mainCategoryId">
            <el-select
              v-model="queryParams.mainCategoryId"
              placeholder="请选择类别"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in dict.type.manual_main_category"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="6">
          <el-form-item label="事件情感" prop="mainSentimentId">
            <el-select
              v-model="queryParams.mainSentimentId"
              placeholder="请选择情感"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in dict.type.manual_main_sentiment"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="填报状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="请选择状态"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in dict.type.manual_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="填报时间">
            <el-date-picker
              v-model="dateRange"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >应用筛选</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="24" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >
          新增填报
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleExport"
          >批量导出</el-button
        >
      </el-col>
      <el-col :span="18">
        <el-button
          type="danger"
          plain
          icon="el-icon-circle-close"
          size="mini"
          :disabled="multiple"
          @click="handleDelete_pl()"
          v-hasPermi="['system:role:remove']"
          >批量删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button
          type="danger"
          plain
          icon="el-icon-circle-close"
          size="mini"
          :disabled="multiple"
          @click="handleDelete_pl()"
          v-hasPermi="['system:role:remove']"
          >排序规则</el-button
        >
        <el-button
          type="danger"
          plain
          icon="el-icon-circle-close"
          size="mini"
          :disabled="multiple"
          @click="handleDelete_pl()"
          v-hasPermi="['system:role:remove']"
          >顺序</el-button
        > -->
        <!-- 排序组件 -->
        <!-- 两个排序  分页条件查询接口加两个字段 查询规则 grouby： sort：-->
        <!-- 可以分俩 -->
      </el-col>
      <!-- <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar> -->
    </el-row>
    <el-table
      v-loading="loading"
      :data="roleList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="120" />
      <el-table-column
        label="事件名称"
        prop="eventName"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column
        label="主题"
        prop="themeId"
        :show-overflow-tooltip="true"
        width="150"
      />
      <el-table-column label="专题" prop="mainTopicId" width="100" />
      <el-table-column label="类别" prop="mainCategoryId" width="100" />
      <!-- 正面、中立、负面。绿蓝红 -->
      <el-table-column label="情感" prop="mainSentimentId" width="100">
        <!-- <el-button>{{mainSentimentId}}</el-button> -->
      </el-table-column>
      <el-table-column label="状态" prop="status" width="100">
        <template slot-scope="scope">
          <el-button type="primary">{{ scope.row.status }}</el-button>
          <!-- <el-button type="danger">{{ scope.row.status }}</el-button> -->
        </template>
        <!-- </el-table-column> -->
        <!-- 文字不一样、颜色不一样  -->
        <!-- <el-button type="primary">{{status}}</el-button> -->
      </el-table-column>
      <el-table-column label="填报人" prop="createByName" width="100" />
      <el-table-column
        label="填报时间"
        align="center"
        prop="create_time"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope" v-if="scope.row.roleId !== 1">
          <!-- v-if 已提交-->
          <el-button
            v-if="scope.row.status == '已提交'"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleViewDetails(scope.row)"
            v-hasPermi="['system:role:edit']"
            >查看</el-button
          >
          <!-- v-if 草稿 -->
          <el-button
            v-if="scope.row.status == '草稿'"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handlechange(scope.row)"
            v-hasPermi="['system:role:edit']"
            >编辑</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:role:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 新增填报任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="820px" append-to-body>
      <p class="dialogTitle">
        人工填报事件信息、舆情日报、舆情专报、理论研究成果等信息
      </p>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <p class="dialogText">分类信息</p>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="主题" prop="themeId">
              <el-select
                v-model="form.themeId"
                placeholder="请选择主题"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_theme"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专题" prop="mainTopicId">
              <el-select
                v-model="form.mainTopicId"
                placeholder="请选择专题"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_main_topic"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="类别" prop="mainCategoryId">
              <el-select
                v-model="form.mainCategoryId"
                placeholder="请选择类别"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_main_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="情感" prop="mainSentimentId">
              <el-select
                v-model="form.mainSentimentId"
                placeholder="请选择情感"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_main_sentiment"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <p class="dialogText">基本信息</p>

        <el-form-item label="事件名称">
          <el-input v-model="form.eventName" placeholder="事件名称"></el-input>
        </el-form-item>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="国家/地区">
              <el-input
                v-model="form.countryRegion"
                placeholder="国家/地区"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生地点">
              <el-input
                v-model="form.localtion"
                placeholder="发生地点"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="发生时间">
              <!-- <el-input
                v-model="form.occurrenceTime"
                placeholder="发生地点"
              ></el-input> -->
              <el-date-picker
                v-model="form.occurrenceTime"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                placeholder="发生时间"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="持续时间">
              <el-input
                v-model="form.duration"
                placeholder="持续时间"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="事件概况">
          <el-input
            v-model="form.eventOverview"
            type="textarea"
            placeholder="事件的起因、经过、结果等关键信息"
          ></el-input>
        </el-form-item>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="冲突类型" prop="mainTopicId">
              <el-select
                v-model="queryParams.conflictType"
                placeholder="请选择冲突类型"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_ct_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="冲突规模">
              <el-input
                v-model="form.conflictScale"
                placeholder="冲突规模"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <!-- 0是1否 -->
            <el-form-item label="是否开火" prop="isOpenFire">
              <el-radio-group v-model="form.isOpenFire">
                <el-radio :label="0">是</el-radio>
                <el-radio :label="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- <el-radio v-model="isOpenFire" :valule="0"> 是 </el-radio>
            <el-radio v-model="isOpenFire" :valule="1"> 否 </el-radio> -->
          </el-col>

          <el-col :span="12">
            <el-form-item label="伤亡人数">
              <el-input
                v-model="form.casualtiesCount"
                placeholder="伤亡人数"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="信源">
          <el-input
            v-model="form.source"
            type="textarea"
            placeholder="数据来源"
          ></el-input>
        </el-form-item>
        <p class="dialogText">相关实体</p>
        <el-form-item label="我方">
          <el-input
            v-model="form.ourPersonnelOrg"
            placeholder="我方相关人员、组织机构"
          ></el-input>
        </el-form-item>
        <el-form-item label="对方">
          <el-input
            v-model="form.opponentPersonnelOrg"
            placeholder="对方相关人员、组织机构"
          ></el-input>
        </el-form-item>
        <p class="dialogText">附件</p>
        <p class="secondTitle">
          请上传开源信息监测软件系统的数据文件，支持一次批量导入多个文件
        </p>
        <el-form-item label-width="0" label="" prop="file">
          <commonUpload
            v-model="form.file"
            :file-size="10"
            :file-type="['xls', 'xlsx', 'zip', 'txt']"
            upload-url="/api/upload"
          />
        </el-form-item>
      </el-form>

      <el-popover
        placement="left"
        width="400"
        class="dialogPopover"
        trigger="hover"
      >
        <div class="notice-container">
          <div class="notice-header">填报须知</div>
          <div class="notice-content">
            <h3>填写规范</h3>
            <ul>
              <li>事件标题应简明扼要，准确反映事件核心内容</li>
              <li>确保时间信息的准确性</li>
              <li>事件概括尽量详细，包含事件的起因、经过、结果等关键信息</li>
              <li>相关实体不可遗漏</li>

              <!-- 相关实体不可遗漏 -->
            </ul>
            <h3>常见问题</h3>
            <p>Q：如何修改已提交的数据？</p>
            <p>A：已提交的数据可在数据资源-xx进行修改。</p>
          </div>
        </div>
        <el-tag slot="reference" type="success">填报须知</el-tag>
      </el-popover>
      <div slot="footer" class="dialog-footer">
        <!-- 暂存和提交传入参数不一 -->
        <!-- 1草、2提交 -->
        <el-button @click="submitForm('1')">暂存草稿</el-button>
        <el-button type="primary" @click="submitForm('2')">提交</el-button>
      </div>
    </el-dialog>
    <!-- 编辑填报任务对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open_change"
      width="820px"
      append-to-body
    >
      <p class="dialogTitle">
        人工填报事件信息、舆情日报、舆情专报、理论研究成果等信息
      </p>
      <el-form ref="form" :model="form_xq" :rules="rules" label-width="150px">
        <p class="dialogText">分类信息</p>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="主题" prop="themeId">
              <el-select
                v-model="form_xq.themeId"
                placeholder="请选择主题"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_theme"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专题" prop="mainTopicId">
              <el-select
                v-model="form.mainTopicId"
                placeholder="请选择专题"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_main_topic"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="类别" prop="mainCategoryId">
              <el-select
                v-model="form.mainCategoryId"
                placeholder="请选择类别"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_main_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="情感" prop="mainSentimentId">
              <el-select
                v-model="form.mainSentimentId"
                placeholder="请选择情感"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_main_sentiment"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <p class="dialogText">基本信息</p>

        <el-form-item label="事件名称">
          <el-input v-model="form.eventName" placeholder="事件名称"></el-input>
        </el-form-item>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="国家/地区">
              <el-input
                v-model="form.countryRegion"
                placeholder="国家/地区"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生地点">
              <el-input
                v-model="form.localtion"
                placeholder="发生地点"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="发生时间">
              <!-- <el-input
                v-model="form.occurrenceTime"
                placeholder="发生地点"
              ></el-input> -->
              <el-date-picker
                v-model="form.occurrenceTime"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                placeholder="发生时间"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="持续时间">
              <el-input
                v-model="form.duration"
                placeholder="持续时间"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="事件概况">
          <el-input
            v-model="form.eventOverview"
            type="textarea"
            placeholder="事件的起因、经过、结果等关键信息"
          ></el-input>
        </el-form-item>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="冲突类型" prop="mainTopicId">
              <el-select
                v-model="queryParams.conflictType"
                placeholder="请选择冲突类型"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_ct_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="冲突规模">
              <el-input
                v-model="form.conflictScale"
                placeholder="冲突规模"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <!-- 0是1否 -->
            <el-form-item label="是否开火" prop="isOpenFire">
              <el-radio-group v-model="form.isOpenFire">
                <el-radio :label="0">是</el-radio>
                <el-radio :label="1">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- <el-radio v-model="isOpenFire" :valule="0"> 是 </el-radio>
            <el-radio v-model="isOpenFire" :valule="1"> 否 </el-radio> -->
          </el-col>

          <el-col :span="12">
            <el-form-item label="伤亡人数">
              <el-input
                v-model="form.casualtiesCount"
                placeholder="伤亡人数"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="信源">
          <el-input
            v-model="form.source"
            type="textarea"
            placeholder="数据来源"
          ></el-input>
        </el-form-item>
        <p class="dialogText">相关实体</p>
        <el-form-item label="我方">
          <el-input
            v-model="form.ourPersonnelOrg"
            placeholder="我方相关人员、组织机构"
          ></el-input>
        </el-form-item>
        <el-form-item label="对方">
          <el-input
            v-model="form.opponentPersonnelOrg"
            placeholder="对方相关人员、组织机构"
          ></el-input>
        </el-form-item>
        <p class="dialogText">附件</p>
        <p class="secondTitle">
          请上传开源信息监测软件系统的数据文件，支持一次批量导入多个文件
        </p>
        <el-form-item label-width="0" label="" prop="file">
          <commonUpload
            v-model="form.file"
            :file-size="10"
            :file-type="['xls', 'xlsx', 'zip', 'txt']"
            upload-url="/api/upload"
          />
        </el-form-item>
      </el-form>

      <el-popover
        placement="left"
        width="400"
        class="dialogPopover"
        trigger="hover"
      >
        <div class="notice-container">
          <div class="notice-header">填报须知</div>
          <div class="notice-content">
            <h3>填写规范</h3>
            <ul>
              <li>事件标题应简明扼要，准确反映事件核心内容</li>
              <li>确保时间信息的准确性</li>
              <li>事件概括尽量详细，包含事件的起因、经过、结果等关键信息</li>
              <li>相关实体不可遗漏</li>

              <!-- 相关实体不可遗漏 -->
            </ul>
            <h3>常见问题</h3>
            <p>Q：如何修改已提交的数据？</p>
            <p>A：已提交的数据可在数据资源-xx进行修改。</p>
          </div>
        </div>
        <el-tag slot="reference" type="success">填报须知</el-tag>
      </el-popover>
      <div slot="footer" class="dialog-footer">
        <!-- 暂存和提交传入参数不一 -->
        <!-- 1草、2提交 -->
        <el-button @click="submitForm_change('1')">暂存草稿</el-button>
        <el-button type="primary" @click="submitForm_change('2')"
          >提交</el-button
        >
      </div>
    </el-dialog>
    <!-- 查看详情弹框 -->
    <el-dialog
      title="任务详细信息"
      :visible.sync="openDetailDialog"
      width="820px"
      append-to-body
    >
      <el-form ref="form" :model="form_xq" :rules="rules" label-width="150px">
        <p class="dialogText">分类信息</p>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="主题" prop="form_xq.themeId">
              <el-select
                v-model="form_xq.themeId"
                placeholder="请选择主题"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_theme"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专题" prop="form_xq.mainTopicId">
              <el-select
                v-model="form_xq.mainTopicId"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_main_topic"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="类别" prop="form_xq.mainCategoryId">
              <!-- <el-input
                v-model="form_xq.mainCategoryName"
                placeholder="类别"
              ></el-input> -->
              <el-select
                v-model="form_xq.mainCategoryId"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_main_category"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="情感" prop="form_xq.mainSentimentId">
              <!-- <el-input
                v-model="form_xq.mainSentimentName"
                placeholder="情感"
              ></el-input> -->
              <el-select
                v-model="form_xq.mainSentimentId"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in dict.type.manual_main_sentiment"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <p class="dialogText">基本信息</p>

        <el-form-item label="事件名称">
          <el-input
            v-model="form_xq.eventName"
            placeholder="事件名称"
          ></el-input>
        </el-form-item>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="国家/地区">
              <el-input
                v-model="form_xq.countryRegion"
                placeholder="国家/地区"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生地点">
              <el-input
                v-model="form_xq.localtion"
                placeholder="发生地点"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="发生时间">
              <el-input
                v-model="form_xq.occurrenceTime"
                placeholder="发生时间"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="持续时间">
              <el-input
                v-model="form_xq.duration"
                placeholder="持续时间"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="事件概况">
          <el-input
            v-model="form_xq.eventOverview"
            type="textarea"
            placeholder="事件的起因、经过、结果等关键信息"
          ></el-input>
        </el-form-item>

        <el-form-item label="信源">
          <el-input
            v-model="form_xq.source"
            type="textarea"
            placeholder="数据来源"
          ></el-input>
        </el-form-item>
        <p class="dialogText">相关实体</p>
        <el-form-item label="我方">
          <el-input
            v-model="form_xq.ourPersonnelOrg"
            placeholder="我方相关人员、组织机构"
          ></el-input>
        </el-form-item>
        <el-form-item label="对方">
          <el-input
            v-model="form_xq.opponentPersonnelOrg"
            placeholder="对方相关人员、组织机构"
          ></el-input>
        </el-form-item>
        <p class="dialogText">附件</p>
      </el-form>
      <!-- 文件列表 可下载 -->
      <transition-group
        name="file-list"
        tag="div"
        class="file-list"
        v-if="fileList.length > 0"
      >
        <div
          v-for="(file, index) in fileList"
          :key="file.uid"
          class="file-item"
        >
          <div class="file-info">
            <div class="file-icon" :class="getFileIconClass(file.name)">
              <svg
                v-if="getFileType(file.name) === 'excel'"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M10 12L14 16M14 12L10 16"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <svg
                v-else-if="getFileType(file.name) === 'zip'"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12 11V17M10 13H14M10 15H14"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <svg
                v-else
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14 2V8H20"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>

            <div class="file-details">
              <p class="file-name" :title="file.name">{{ file.name }}</p>
              <p class="file-size">{{ formatFileSize(file.size) }}</p>
            </div>
          </div>

          <div class="file-actions">
            <button
              type="button"
              class="action-btn download-btn"
              @click="handleDownload(file)"
              title="下载"
            >
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M7 10L12 15L17 10"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M12 15V3"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </transition-group>
    </el-dialog>
  </div>
</template>

<script>
import commonUpload from "@/views/components/commonUpload.vue";
import {
  // manual_theme
  // dictCode 不会重复，当下拉框传的键值
  exports,
  datain_record_list,
  // listRole,
  // getRole,
  getrecord,
  delrecord,
  addrecord,
  // 提交
  updaterecord,
  // 暂存
  save_record,
  // dataScope,
  // changeRoleStatus,
  // deptTreeSelect,
} from "@/api/data/datain";
import {
  treeselect as menuTreeselect,
  roleMenuTreeselect,
} from "@/api/system/menu";

export default {
  components: { commonUpload },

  name: "Role",
  // dicts: ["sys_normal_disable"],
  dicts: [
    "manual_theme",
    "manual_status",
    "manual_main_sentiment",
    "manual_main_category",
    "manual_main_topic",
    "manual_ct_type",
  ],

  data() {
    return {
      // manual_status:
      fileList: [
        {
          uid: "file-1760678872252-0",
          name: "工作.txt",
          size: 10817,
          raw: {},
          url: "blob:http://localhost/34b5fed2-4632-457b-8ace-a16dda188f36",
        },
        {
          uid: "file-1760678883922-1",
          name: "附件1.xlsx",
          size: 63495,
          raw: {},
          url: "blob:http://localhost/d9e1bbc5-f510-441b-b453-9e81900175ae",
        },
      ],
      openDetailDialog: false,
      open_change: false,
      // open_change
      totalSum: 0,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      // 日期范围
      dateRange: [],
      // 数据范围选项
      dataScopeOptions: [
        {
          value: "1",
          label: "全部数据权限",
        },
        {
          value: "2",
          label: "自定数据权限",
        },
        {
          value: "3",
          label: "本部门数据权限",
        },
        {
          value: "4",
          label: "本部门及以下数据权限",
        },
        {
          value: "5",
          label: "仅本人数据权限",
        },
      ],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [],
      // 详情返回并展示的字段
      queryParams_xq: {
        // 主题
        themeId: undefined,
        // 专题
        mainTopicId: undefined,
        //类别
        mainCategoryId: undefined,
        // 情感
        mainSentimentId: undefined,
        // 事件名称
        eventName: undefined,
        // 国家、地区
        countryRegion: undefined,
        // 发生地点
        locltion: undefined,
        // 发生时间
        occurrenceTime: undefined,
        // 持续时间
        duration: undefined,
        // 事件概况
        eventOverview: undefined,
        // 信源
        source: undefined,
        // 我方
        ourPersonnelOrg: undefined,
        // 对方
        opponentPersonnelOrg: undefined,
        // 附件信息
        fj: [
          {
            fjid: "",
            fjName: "",
          },
        ],
      },
      // 显示新增弹窗
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // 在这里改成研判的查询字段
        //
        eventName: undefined,
        themeId: undefined,
        mainSentimentId: undefined,
        mainTopicId: undefined,
        mainCategoryId: undefined,
        status: undefined,
      },
      // 继续编辑记录的id
      changeid: "",
      // 新增表单参数
      form: {
        themeId: "", //主题id
        mainTopicId: "", //专题id
        mainSentimentId: "", //情感id
        mainCategoryId: "", //类别id
        EventName: "", //事件名称
        CountryRegion: "", //国家/地区
        localtion: "", //发生地点
        occurrenceTime: "", //发生时间
        duration: "", //持续时间
        eventOverview: "", //事件概况
        conflictType: "", //冲突类型
        conflictScale: "", //冲突规模
        isOpenFire: "", //是否开火
        casualtiesCount: "", //伤亡人数
        source: "", //信源
        ourPersonnelOrg: "", //我方相关人员、组织机构
        opponentPersonnelOrg: "", //对方相关人员、组织机构
        status: "",
        // 继续编辑记录的id

        changeid: "",
      },
      // 查询单条目、修改表单参数
      form_xq: {
        themeName: "", //主题名称
        themeId: "", //主题id
        mainTopicId: "", //专题id
        mainSentimentId: "", //情感id
        mainCategoryId: "", //类别id
        EventName: "", //事件名称
        CountryRegion: "", //国家/地区
        localtion: "", //发生地点
        occurrenceTime: "", //发生时间
        duration: "", //持续时间
        eventOverview: "", //事件概况
        conflictType: "", //冲突类型
        conflictScale: "", //冲突规模
        isOpenFire: "", //是否开火
        casualtiesCount: "", //伤亡人数
        source: "", //信源
        ourPersonnelOrg: "", //我方相关人员、组织机构
        opponentPersonnelOrg: "", //对方相关人员、组织机构
        status: "",
        // 继续编辑记录的id

        changeid: "",
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: "角色名称不能为空", trigger: "blur" },
        ],
        roleKey: [
          { required: true, message: "权限字符不能为空", trigger: "blur" },
        ],
        roleSort: [
          { required: true, message: "角色顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.what();
  },
  methods: {
    what() {
      console.log(this.dict.type.manual_main_sentiment);
    },
    formatFileSize(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toFixed(2) + " " + sizes[i];
    },
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },
    getFileIconClass(fileName) {
      const type = this.getFileType(fileName);
      return `file-icon-${type}`;
    },
    getFileType(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      if (["xls", "xlsx"].includes(ext)) return "excel";
      if (["zip", "rar", "7z"].includes(ext)) return "zip";
      if (["txt", "json", "csv"].includes(ext)) return "text";
      return "file";
    },
    // 继续编辑
    async handlechange(row) {
      let res = await getrecord(row.id);
      // console.log(res.data)
      this.form_xq = res.data[0];
      this.open_change = true;
      this.form.changeid = row.id;
      console.log("获取当行id用于编辑");
    },
    // 查看详情
    async handleViewDetails(row) {
      let res = await getrecord(row.id);
      console.log(res.data);
      this.form_xq = res.data[0];

      this.openDetailDialog = true;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增填报任务";
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true;
      datain_record_list(
        this.addDateRange(this.queryParams, this.dateRange)
      ).then((response) => {
        this.roleList = response.rows;
        this.total = response.total;
        this.loading = false;
        this.totalSum = response.totalSum;
      });
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      menuTreeselect().then((response) => {
        this.menuOptions = response.data;
      });
    },
    // 所有菜单节点数据
    // getMenuAllCheckedKeys() {
    //   // 目前被选中的菜单节点
    //   let checkedKeys = this.$refs.menu.getCheckedKeys();
    //   // 半选中的菜单节点
    //   let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();
    //   checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    //   return checkedKeys;
    // },
    // // 所有部门节点数据
    // getDeptAllCheckedKeys() {
    //   // 目前被选中的部门节点
    //   let checkedKeys = this.$refs.dept.getCheckedKeys();
    //   // 半选中的部门节点
    //   let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();
    //   checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    //   return checkedKeys;
    // },
    /** 根据角色ID查询菜单树结构 */
    getRoleMenuTreeselect(roleId) {
      return roleMenuTreeselect(roleId).then((response) => {
        this.menuOptions = response.menus;
        return response;
      });
    },
    /** 根据角色ID查询部门树结构 */
    getDeptTree(roleId) {
      return deptTreeSelect(roleId).then((response) => {
        this.deptOptions = response.depts;
        return response;
      });
    },
    // 角色状态修改
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$modal
        .confirm('确认要"' + text + '""' + row.roleName + '"角色吗？')
        .then(function () {
          return changeRoleStatus(row.roleId, row.status);
        })
        .then(() => {
          this.$modal.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false;
      this.reset();
    },
    // 表单重置
    reset() {
      if (this.$refs.menu != undefined) {
        this.$refs.menu.setCheckedKeys([]);
      }
      (this.menuExpand = false),
        (this.menuNodeAll = false),
        (this.deptExpand = true),
        (this.deptNodeAll = false),
        (this.form = {
          themeId: "", //主题id
          mainTopicId: "", //专题id
          mainSentimentId: "", //情感id
          mainCategoryId: "", //类别id
          EventName: "", //事件名称
          CountryRegion: "", //国家/地区
          localtion: "", //发生地点
          occurrenceTime: "", //发生时间
          duration: "", //持续时间
          eventOverview: "", //事件概况
          conflictType: "", //冲突类型
          conflictScale: "", //冲突规模
          isOpenFire: "", //是否开火
          casualtiesCount: "", //伤亡人数
          source: "", //信源
          ourPersonnelOrg: "", //我方相关人员、组织机构
          opponentPersonnelOrg: "", //对方相关人员、组织机构
          status: "",
        });
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleDataScope":
          this.handleDataScope(row);
          break;
        case "handleAuthUser":
          this.handleAuthUser(row);
          break;
        default:
          break;
      }
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type == "menu") {
        let treeList = this.menuOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;
        }
      } else if (type == "dept") {
        let treeList = this.deptOptions;
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type == "menu") {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : []);
      } else if (type == "dept") {
        this.$refs.dept.setCheckedNodes(value ? this.deptOptions : []);
      }
    },
    // // 树权限（父子联动）
    // handleCheckedTreeConnect(value, type) {
    //   if (type == "menu") {
    //     this.form.menuCheckStrictly = value ? true : false;
    //   } else if (type == "dept") {
    //     this.form.deptCheckStrictly = value ? true : false;
    //   }
    // },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roleId = row.roleId || this.ids;
      const roleMenu = this.getRoleMenuTreeselect(roleId);
      getrecord(roleId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.$nextTick(() => {
          roleMenu.then((res) => {
            let checkedKeys = res.checkedKeys;
            checkedKeys.forEach((v) => {
              this.$nextTick(() => {
                this.$refs.menu.setChecked(v, true, false);
              });
            });
          });
        });
      });
      this.title = "修改角色";
    },
    /** 选择角色权限范围触发 */
    dataScopeSelectChange(value) {
      if (value !== "2") {
        this.$refs.dept.setCheckedKeys([]);
      }
    },
    // /** 分配数据权限操作 */
    // handleDataScope(row) {
    //   this.reset();
    //   const deptTreeSelect = this.getDeptTree(row.roleId);
    //   getRole(row.roleId).then((response) => {
    //     this.form = response.data;
    //     this.openDataScope = true;
    //     this.$nextTick(() => {
    //       deptTreeSelect.then((res) => {
    //         this.$refs.dept.setCheckedKeys(res.checkedKeys);
    //       });
    //     });
    //   });
    //   this.title = "分配数据权限";
    // },
    // /** 分配用户操作 */
    // handleAuthUser: function (row) {
    //   const roleId = row.roleId;
    //   this.$router.push("/system/role-auth/user/" + roleId);
    // },router
    /** 提交按钮 */
    // 新增和暂存
    submitForm: function (c1) {
      this.form.status = c1;
      // this.$refs["form"].validate((valid) => {
      // if (valid) {
      // if (this.form.roleId != undefined) {
      // this.form.menuIds = this.getMenuAllCheckedKeys();
      addrecord(this.form).then((response) => {
        this.$modal.msgSuccess("操作成功");
        this.open = false;
        this.getList();
      });
      //     } else {
      //       // this.form.menuIds = this.getMenuAllCheckedKeys();
      //       addrecord(this.form, c1).then((response) => {
      //         this.$modal.msgSuccess("新增成功");
      //         this.open = false;
      //         this.getList();
      //       });
      //     }
      //   }
      // });
    },
    // 编辑进去的新增和暂存
    submitForm_change: function (c1) {
      this.form.status = c1;
      // this.$refs["form"].validate((valid) => {
      // if (valid) {
      // if (this.form.roleId != undefined) {
      // this.form.menuIds = this.getMenuAllCheckedKeys();
      updaterecord(this.form).then((response) => {
        this.$modal.msgSuccess("操作成功");
        this.open = false;
        this.getList();
      });
    },
    // },
    /** 提交按钮（数据权限） */
    // submitDataScope: function () {
    //   if (this.form.roleId != undefined) {
    //     this.form.deptIds = this.getDeptAllCheckedKeys();
    //     dataScope(this.form).then((response) => {
    //       this.$modal.msgSuccess("修改成功");
    //       this.openDataScope = false;
    //       this.getList();
    //     });
    //   }
    // },

    /** 删除按钮操作 */
    handleDelete(row) {
      const roleIds = row.id || this.ids;
      this.$modal
        .confirm("是否确认删除选中的数据项？")
        .then(function () {
          return delrecord(roleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    handleDelete_pl() {
      const roleIds = this.ids;
      // this.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项？').then(function() {
      this.$modal
        .confirm("是否确认删除选中的数据项？")
        .then(function () {
          return delrecord(roleIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "dataaccess/manual/export",
        {
          ids: [this.ids],
        },
        `填报记录.xlsx`
      );
    },
  },
};
</script>