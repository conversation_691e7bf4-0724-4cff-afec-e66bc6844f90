<template>
  <div class="cool-world-map-wrapper" :style="wrapperStyle">
    <!-- Animated background -->
    <div class="animated-background">
      <div class="stars-layer"></div>
      <div class="grid-layer"></div>
      <div class="gradient-overlay"></div>
    </div>
    
    <!-- Map container -->
    <div ref="mapContainer" class="map-container" :style="{ height: height }"></div>
    
    <!-- Custom legend if enabled -->
    <div v-if="showLegend" class="map-legend" :style="legendStyle">
      <div v-for="(item, index) in legendData" :key="index" class="legend-item">
        <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
        <span class="legend-label">{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import world from './world2.js'

export default {
  name: 'CoolWorldMap',
  props: {
    // 基础配置
    height: {
      type: String,
      default: '600px'
    },
    backgroundColor: {
      type: String,
      default: 'transparent'
    },
    
    // 地图样式配置
    mapStyle: {
      type: Object,
      default: () => ({
        areaColor: '#1a1f3a',
        borderColor: '#00d9ff',
        borderWidth: 1,
        shadowBlur: 15,
        shadowColor: 'rgba(0, 217, 255, 0.5)'
      })
    },
    
    // 高亮样式
    emphasisStyle: {
      type: Object,
      default: () => ({
        areaColor: '#2c3557',
        borderColor: '#00ffff',
        borderWidth: 2,
        shadowBlur: 20,
        shadowColor: 'rgba(0, 255, 255, 0.8)'
      })
    },
    
    // 数据点配置
    data: {
      type: Array,
      default: () => []
    },
    
    // 散点样式
    scatterStyle: {
      type: Object,
      default: () => ({
        symbolSize: 12,
        color: '#00ffff',
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowBlur: 10,
        shadowColor: 'rgba(0, 255, 255, 0.8)'
      })
    },
    
    // 动画配置
    enableAnimation: {
      type: Boolean,
      default: true
    },
    animationDuration: {
      type: Number,
      default: 1000
    },
    
    // 图例配置
    showLegend: {
      type: Boolean,
      default: false
    },
    legendData: {
      type: Array,
      default: () => []
    },
    legendStyle: {
      type: Object,
      default: () => ({})
    },
    
    // 视觉映射配置
    visualMap: {
      type: Object,
      default: null
    },
    
    // 涟漪特效
    enableRipple: {
      type: Boolean,
      default: true
    },
    rippleColor: {
      type: String,
      default: '#00ffff'
    },
    
    // 标签配置
    showLabel: {
      type: Boolean,
      default: false
    },
    labelStyle: {
      type: Object,
      default: () => ({
        color: '#00d9ff',
        fontSize: 12,
        fontWeight: 'normal'
      })
    }
  },
  
  data() {
    return {
      chart: null,
      world: null
    }
  },
  
  computed: {
    wrapperStyle() {
      return {
        backgroundColor: this.backgroundColor,
        position: 'relative',
        overflow: 'hidden'
      }
    }
  },
  
  mounted() {
    this.loadWorldMap()
  },
  
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  
  methods: {
    async loadWorldMap() {
        this.world = world
        echarts.registerMap('world', this.world)
        this.initChart()
    },
    
    initChart() {
      if (this.chart) {
        this.chart.dispose()
      }
      
      this.chart = echarts.init(this.$refs.mapContainer)
      const option = this.getChartOption()
      this.chart.setOption(option)
      
      window.addEventListener('resize', this.handleResize)
      
      // 触发自定义事件
      this.chart.on('click', (params) => {
        this.$emit('map-click', params)
      })
      
      this.chart.on('mouseover', (params) => {
        this.$emit('map-hover', params)
      })
    },
    
    getChartOption() {
      const series = [
        {
          type: 'map',
          map: 'world',
          roam: false,
          scaleLimit: {
            min: 1,
            max: 5
          },
          itemStyle: {
            areaColor: this.mapStyle.areaColor,
            borderColor: this.mapStyle.borderColor,
            borderWidth: this.mapStyle.borderWidth,
            shadowBlur: this.mapStyle.shadowBlur,
            shadowColor: this.mapStyle.shadowColor
          },
          emphasis: {
            itemStyle: {
              areaColor: this.emphasisStyle.areaColor,
              borderColor: this.emphasisStyle.borderColor,
              borderWidth: this.emphasisStyle.borderWidth,
              shadowBlur: this.emphasisStyle.shadowBlur,
              shadowColor: this.emphasisStyle.shadowColor
            },
            label: this.showLabel ? {
              show: true,
              color: this.labelStyle.color,
              fontSize: this.labelStyle.fontSize,
              fontWeight: this.labelStyle.fontWeight
            } : {
              show: false
            }
          },
          label: {
            show: false
          }
        }
      ]
      
      // 添加散点图层
      if (this.data && this.data.length > 0) {
        const scatterSeries = {
          type: this.enableRipple ? 'effectScatter' : 'scatter',
          coordinateSystem: 'geo',
          data: this.data,
          symbolSize: (val) => {
            return val[2] || this.scatterStyle.symbolSize
          },
          itemStyle: {
            color: this.scatterStyle.color,
            borderColor: this.scatterStyle.borderColor,
            borderWidth: this.scatterStyle.borderWidth,
            shadowBlur: this.scatterStyle.shadowBlur,
            shadowColor: this.scatterStyle.shadowColor
          },
          label: {
            show: false
          },
          emphasis: {
            scale: true,
            label: {
              show: true,
              formatter: (params) => {
                return params.data.name || params.name
              },
              color: '#ffffff',
              fontSize: 14,
              fontWeight: 'bold'
            }
          }
        }
        
        if (this.enableRipple) {
          scatterSeries.rippleEffect = {
            brushType: 'stroke',
            color: this.rippleColor,
            scale: 3,
            period: 4
          }
        }
        
        series.push(scatterSeries)
      }
      
      const option = {
        backgroundColor: 'transparent',
        geo: {
          map: 'world',
          roam: false,
          scaleLimit: {
            min: 1,
            max: 5
          },
          itemStyle: {
            areaColor: this.mapStyle.areaColor,
            borderColor: this.mapStyle.borderColor,
            borderWidth: this.mapStyle.borderWidth,
            shadowBlur: this.mapStyle.shadowBlur,
            shadowColor: this.mapStyle.shadowColor
          },
          emphasis: {
            itemStyle: {
              areaColor: this.emphasisStyle.areaColor,
              borderColor: this.emphasisStyle.borderColor,
              borderWidth: this.emphasisStyle.borderWidth,
              shadowBlur: this.emphasisStyle.shadowBlur,
              shadowColor: this.emphasisStyle.shadowColor
            }
          },
          silent: false
        },
        series: series,
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00d9ff',
          borderWidth: 1,
          textStyle: {
            color: '#00ffff',
            fontSize: 14
          },
          formatter: (params) => {
            if (params.componentSubType === 'map' || params.componentSubType === 'scatter' || params.componentSubType === 'effectScatter') {
              if (params.data && params.data.tooltip) {
                return params.data.tooltip
              }
              return params.name
            }
            return params.name
          }
        },
        animation: this.enableAnimation,
        animationDuration: this.animationDuration,
        animationEasing: 'cubicOut'
      }
      
      // 添加视觉映射
      if (this.visualMap) {
        option.visualMap = {
          min: this.visualMap.min || 0,
          max: this.visualMap.max || 100,
          text: this.visualMap.text || ['High', 'Low'],
          realtime: false,
          calculable: true,
          inRange: {
            color: this.visualMap.colors || ['#0a2f51', '#0e4d92', '#2e8cca', '#00d9ff', '#00ffff']
          },
          textStyle: {
            color: '#00d9ff'
          },
          ...this.visualMap
        }
      }
      
      return option
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    
    // 公共方法：更新数据
    updateData(newData) {
      if (this.chart) {
        const option = this.getChartOption()
        this.chart.setOption(option)
      }
    },
    
    // 公共方法：更新配置
    updateConfig() {
      if (this.chart) {
        const option = this.getChartOption()
        this.chart.setOption(option)
      }
    }
  },
  
  watch: {
    data: {
      handler() {
        this.updateData()
      },
      deep: true
    },
    mapStyle: {
      handler() {
        this.updateConfig()
      },
      deep: true
    },
    emphasisStyle: {
      handler() {
        this.updateConfig()
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.cool-world-map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  font-family: 'Arial', sans-serif;
}

.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  pointer-events: none;
}

/* 星空背景 */
.stars-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(2px 2px at 20% 30%, #ffffff, transparent),
              radial-gradient(2px 2px at 60% 70%, #00d9ff, transparent),
              radial-gradient(1px 1px at 50% 50%, #ffffff, transparent),
              radial-gradient(1px 1px at 80% 10%, #00ffff, transparent),
              radial-gradient(2px 2px at 90% 60%, #ffffff, transparent),
              radial-gradient(1px 1px at 33% 80%, #00d9ff, transparent);
  background-size: 200% 200%;
  background-position: 0% 0%;
  animation: stars-move 20s ease-in-out infinite;
  opacity: 0.6;
}

@keyframes stars-move {
  0%, 100% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
}

/* 网格背景 */
.grid-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(0, 217, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 217, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 15s linear infinite;
  opacity: 0.3;
}

@keyframes grid-move {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

/* 渐变遮罩 */
.gradient-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, transparent 0%, rgba(10, 15, 30, 0.8) 100%);
  pointer-events: none;
}

.map-container {
  position: relative;
  z-index: 1;
  width: 100%;
}

/* 图例样式 */
.map-legend {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 2;
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid #00d9ff;
  border-radius: 8px;
  padding: 15px;
  backdrop-filter: blur(10px);
  box-shadow: 0 0 20px rgba(0, 217, 255, 0.3);
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #00ffff;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-color {
  width: 20px;
  height: 12px;
  margin-right: 10px;
  border-radius: 2px;
  box-shadow: 0 0 8px currentColor;
}

.legend-label {
  font-weight: 300;
  letter-spacing: 0.5px;
}
</style>
