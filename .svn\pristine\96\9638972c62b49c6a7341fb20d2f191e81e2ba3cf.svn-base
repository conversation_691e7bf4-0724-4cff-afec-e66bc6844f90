<template>
  <div>
    <el-row :gutter="20">
      <!-- {{ deptOptions }} -->
      <splitpanes
        :horizontal="this.$store.getters.device === 'mobile'"
        class="default-theme"
      >
        <pane size="20">
          <el-col>
            <div class="flex">
              <div class="flex-item">
                <div class="flex-title">
                  {{ evaluate.totality }} <span>人</span>
                </div>
                <div class="flex-content">需要评价</div>
              </div>
              <div class="flex-item">
                <div class="flex-title flex-title1">
                  {{ evaluate.rated }}<span>人</span>
                </div>
                <div class="flex-content">已评价</div>
              </div>
              <div class="flex-itemNoBorder">
                <div class="flex-title flex-title2">
                  {{ evaluate.unrated }}<span>人</span>
                </div>
                <div class="flex-content">未评价</div>
              </div>
            </div>
            <div class="flex-bottom">
              <div class="head-container">
                <el-input
                  v-model="deptName"
                  placeholder="请输入部门名称"
                  clearable
                  size="small"
                  prefix-icon="el-icon-search"
                  style="margin-bottom: 10px"
                />
                <!-- {{deptOptions}} -->
                <el-tree
                  :data="deptOptions"
                  :props="defaultProps"
                  :filter-node-method="filterNode"
                  ref="tree"
                  node-key="id"
                  default-expand-all
                  highlight-current
                  @node-click="handleNodeClick"
                >
                  <template #default="{ node, data }">
                    <span class="custom-tree-node">
                      <span v-if="isFirstRootNode(node)" class="prefix-img"
                        ><img src="./img/elTree.png" alt=""
                      /></span>
                      <span
                        v-if="!isFirstRootNode(node) && data.sfpj == 2"
                        class="prefix-text"
                        >已评价</span
                      >
                      <span
                        v-if="!isFirstRootNode(node) && data.sfpj == 1"
                        class="prefix-text1"
                        >未评价</span
                      >
                      <span v-if="isFirstRootNode(node)" class="eltree-text">{{
                        node.label
                      }}</span>
                      <span v-if="!isFirstRootNode(node)" class="eltree-con">{{
                        node.label
                      }}</span>
                    </span>
                  </template>
                </el-tree>
              </div>
            </div>
          </el-col>
        </pane>
        <pane size="80">
          <!-- <div slot="header" class="card-header">
              <span class="header-title">{{ title }}</span>
            </div> -->
          <div class="card-body">
            <el-form
              ref="form"
              :model="formData"
              style="
                border-top: 1px solid rgba(215, 218, 224, 1);
                border-left: 1px solid rgba(215, 218, 224, 1);
                border-right: 1px solid rgba(215, 218, 224, 1);
              "
              :rules="formRules"
              label-width="191px"
              label-position="left"
              class="evaluation-form"
            >
              <!-- 基础信息部分 -->
              <div v-if="assessmentType === 1">
                <el-form-item label="姓名" prop="name" class="elHeight">
                  <el-input
                    v-model="formData.name"
                    placeholder="请输入姓名"
                  ></el-input>
                </el-form-item>
                <el-form-item label="处室" prop="department" class="elHeight">
                  <el-input
                    v-model="formData.department"
                    placeholder="请输入处室"
                  ></el-input>
                </el-form-item>
                <el-form-item label="职务职级" prop="position" class="elHeight">
                  <el-input
                    v-model="formData.position"
                    placeholder="请输入职务职级"
                  ></el-input>
                </el-form-item>
              </div>
              <div v-if="assessmentType === 2">
                <el-form-item
                  label="机构名称"
                  prop="nameOfInstitution"
                  class="elHeight"
                >
                  <el-input
                    v-model="formData.nameOfInstitution"
                    placeholder="请输入机构名称"
                  ></el-input>
                </el-form-item>
              </div>

              <template v-for="(section, index) in evaluationSections">
                <div :key="index">
                  <el-form-item
                    :label="section.label"
                    :prop="'sections[' + index + '].rating'"
                    class="elHeightBig"
                  >
                    <el-radio-group
                      v-model="formData.sections[index].rating"
                      @change="(val) => handleRatingChange(val, index)"
                    >
                      <el-radio
                        v-for="(grade, gIndex) in grades"
                        :key="gIndex"
                        :label="grade.value"
                        :class="{
                          'active-radio':
                            formData.sections[index].rating === grade.value,
                        }"
                      >
                        {{ grade.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </div>
              </template>
            </el-form>

            <!-- 提交按钮 -->
            <div class="form-buttons">
              <!-- <el-button @click="resetForm">临时保存</el-button> -->
              <el-button
                type="primary"
                :loading="submitting"
                @click="submitForm"
                >提 交</el-button
              >
            </div>
          </div>
        </pane>
      </splitpanes>
    </el-row>
  </div>
</template>

<script>
import { deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
import EvaluationForm from "@/components/EvaluationForm";
export default {
  name: "EvaluationForm",
  props: {
    title: {
      type: String,
      default: "评价表",
    },
    evaluationSections: {
      type: Array,
      required: true,
    },
    grades: {
      type: Array,
      required: true,
    },
    deptOptions: {
      type: Array,
      required: true,
    },
    evaluate: {
      type: Object,
      required: true,
    },
    assessmentType: {
      type: Number,
      required: true,
    },
  },
  components: { Treeselect, Splitpanes, Pane, EvaluationForm },

  data() {
    return {
      // 部门名称
      deptName: undefined,
      defaultProps: {
        children: "children",
        label: "label",
      },
      submitting: false,
      formData: {
        name: "",
        department: "",
        position: "",
        nameOfInstitution: "",
        sections: [],
      },
      // grades: [
      //   { value: "优秀", label: "优秀 (90分-100分)", min: 90, max: 100 },
      //   { value: "称职", label: "称职 (70分-89分)", min: 70, max: 89 },
      //   { value: "基本称职", label: "基本称职 (60分-69分)", min: 60, max: 69 },
      //   { value: "不称职", label: "不称职 (59分以下)", min: 0, max: 59 },
      // ],
      formRules: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        department: [
          { required: true, message: "请输入处室", trigger: "blur" },
        ],
        position: [
          { required: true, message: "请输入职务职级", trigger: "blur" },
        ],
        // rating: [
        //   { required: true, message: "请选择评分", trigger: "change" },
        // ],
        "sections[0].rating": [
          { required: true, message: "请选择评分", trigger: "change" },
        ],
        "sections[1].rating": [
          { required: true, message: "请选择评分", trigger: "change" },
        ],
        "sections[2].rating": [
          { required: true, message: "请选择评分", trigger: "change" },
        ],
        "sections[3].rating": [
          { required: true, message: "请选择评分", trigger: "change" },
        ],
        "sections[4].rating": [
          { required: true, message: "请选择评分", trigger: "change" },
        ],
        nameOfInstitution: [
          { required: true, message: "请输入机构名称", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    // 初始化sections数据
    this.formData.sections = this.evaluationSections.map(() => ({
      rating: "",
      score: null,
      explanation: "",
    }));

    // 动态添加验证规则
    this.evaluationSections.forEach((section, index) => {
      this.formRules[`sections.${index}.rating`] = [
        {
          required: true,
          message: `请选择${section.label}等级`,
          trigger: "change",
        },
      ];
      this.formRules[`sections.${index}.score`] = [
        {
          required: true,
          message: `请输入${section.label}具体分数`,
          trigger: "blur",
        },
      ];
    });
  },
  methods: {
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      console.log(data, "111111111111111111");
      this.formData.name = data.name;
      this.formData.department = data.department;
      this.formData.position = data.position;
      this.formData.nameOfInstitution = data.label;
      // this.$emit("handleNodeClick", data);
    },

    getScoreRange(rating) {
      const grade = this.grades.find((g) => g.value === rating);
      return grade ? { min: grade.min, max: grade.max } : { min: 0, max: 100 };
    },

    handleRatingChange(rating, index) {
      const range = this.getScoreRange(rating);
      const currentScore = this.formData.sections[index].score;

      if (
        currentScore === null ||
        currentScore < range.min ||
        currentScore > range.max
      ) {
        this.formData.sections[index].score = range.min;
      }
    },
    // 判断是否是第一个根节点
    isFirstRootNode(node) {
      // 假设 deptOptions 是根节点的数组
      // 检查 node 是否是 deptOptions 中的第一个元素
      return node.level === 1 && node.data.id === this.deptOptions[0].id;
    },
    validateScore(value, index) {
      const range = this.getScoreRange(this.formData.sections[index].rating);
      if (value < range.min) {
        this.formData.sections[index].score = range.min;
        this.$message.warning(`分数不能低于${range.min}分`);
      } else if (value > range.max) {
        this.formData.sections[index].score = range.max;
        this.$message.warning(`分数不能高于${range.max}分`);
      }
    },

    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm("提交后不能修改，是否确认提交？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.submitting = true;
              this.$emit("submit", this.formData);
            })
            .catch(() => {});
        } else {
          this.$message.error("请完善所有必填项");
          return false;
        }
      });
    },

    resetForm() {
      this.$confirm("是否确认重置所有内容？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$refs.form.resetFields();
          this.$message({
            type: "success",
            message: "重置成功！",
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped>
.default-theme {
  padding: 11px;
}
.app-container {
  padding: 20px;
  /* background-color: #f5f7fa; */
  min-height: calc(100vh - 84px);
}
.card-body {
  padding-left: 50px;
  padding-right: 45px;
  /* border-left: 1px solid #eee; */
}
.flex {
  margin-left: 11px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.flex-title {
  text-align: center;
  font-family: SourceHanSansSC-Bold;
  font-size: 18px;
  color: #1b5dd8;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 700;
}
.flex-title1 {
  color: #09b72b;
}
.flex-title2 {
  color: #ff7204;
}
.flex-title span {
  font-family: SourceHanSansSC-Regular;
  font-size: 12px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 400;
}
.flex-content {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #666666;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 400;
}
.flex-item {
  width: 33%;
  border-right: 1px solid rgba(232, 232, 232, 1);
}

.flex-itemNoBorder {
  width: 33%;
}
.flex-bottom {
  background: #ffffff;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}
.head-container {
  padding: 14px 10px;
}

::v-deep .el-tree > .el-tree-node > .el-tree-node__content {
  /* 这里是第一个根节点的样式 */
  height: 32px;
  background: #ebf0f8;
  border-radius: 2px;
  border: none;
}

::v-deep .el-tree-node__content {
  height: 35px;
  border-bottom: 1px solid rgba(233, 233, 233, 1);
  padding-left: 10px !important;
}
::v-deep .el-form-item {
  margin-bottom: 0px;
}
/* ::v-deep .el-icon-caret-right:before {
  content: none;
}
::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
  padding: 0;
} */
.elHeight {
  height: 42px;
}
.elHeightBig {
  height: 138px;
}
::v-deep .el-form-item--medium .el-form-item__label {
  height: 100%;
  background: #f6f8fe;
  font-family: SourceHanSansSC-Regular;
  border-right: 1px solid rgba(215, 218, 224, 1);
  border-bottom: 1px solid rgba(215, 218, 224, 1);
  font-size: 14px;
  color: #203f78;
  letter-spacing: 0;
  font-weight: 400;
  padding-left: 20px;
  display: flex;
  align-items: center;
}
::v-deep .el-form-item--medium .el-form-item__content {
  height: 100%;
  background: #ffffff;
  border-bottom: 1px solid rgba(215, 218, 224, 1);
  display: flex;
  align-items: center;
}
::v-deep .el-input__inner {
  border: none;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 400;
}
.custom-tree-node {
  display: flex;
  align-items: center;
}

.prefix-text {
  font-family: SourceHanSansSC-Regular;
  padding: 2.5px 2px;
  margin-right: 7px;
  font-size: 8px;
  color: #09b72b;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
  background: #f3fff0;
  border: 1px solid rgba(9, 183, 43, 1);
  border-radius: 2px;
}
.prefix-text1 {
  font-family: SourceHanSansSC-Regular;
  padding: 2.5px 2px;
  margin-right: 7px;
  font-size: 8px;
  color: #ff7204;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
  background: #fff7f0;
  border: 1px solid rgba(9, 183, 43, 1);
  border-radius: 2px;
}
.prefix-img {
  color: white;
  display: flex;
  align-items: center;
  margin-right: 8px;
}
.eltree-text {
  font-family: SourceHanSansSC-Bold;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
  line-height: 24px;
  font-weight: 500;
}
.eltree-con {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  line-height: 24px;
  font-weight: 400;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
}

.header-title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
  margin: 20px 0;
  padding-left: 10px;
  border-left: 4px solid #409eff;
  background-color: #ecf5ff;
  line-height: 40px;
}

.evaluation-section {
  background-color: #fafafa;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.section-label {
  font-size: 16px;
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.el-icon-question {
  color: #909399;
  cursor: pointer;
}

.rating-section {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

/* .el-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-right: 20px;
} */

.el-radio {
  margin-right: 0;
  padding: 8px 15px;
  border-radius: 4px;
  transition: all 0.3s;
}
::v-deep .el-radio {
  display: block;
  padding: 8px 21px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 400;
}
.el-radio.active-radio {
  background-color: #ecf5ff;
}

.score-input-container {
  display: flex;
  align-items: center;
}

.score-input {
  width: 120px;
}

.score-label {
  margin-left: 5px;
  color: #606266;
}

.explanation-section {
  margin-bottom: 0;
}

.form-buttons {
  margin-top: 30px;
  padding-bottom: 18px;
  text-align: right;
}
::v-deep .el-button {
  padding: 6px 12px;
  background: #ffffff;
  border: 1px solid rgba(27, 93, 216, 1);
  border-radius: 6px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #1b5dd8;
  letter-spacing: 0;
  line-height: 20px;
  font-weight: 400;
}
::v-deep .el-button--primary {
  padding: 6px 26px;
  background: #1b5dd8;
  border-radius: 6px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #ffffff;
  letter-spacing: 0;
  line-height: 20px;
  font-weight: 400;
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .el-radio-group {
    flex-direction: column;
  }

  .rating-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .score-input-container {
    margin-top: 15px;
  }
}
</style> 