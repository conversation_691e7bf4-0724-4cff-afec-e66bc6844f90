<template>
  <div class="line-chart" :style="{ width, height }">
    <div ref="chartDom" class="chart-inner"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "LineChart",
  props: {
    // 图表基础数据配置
    chartData: {
      type: Object,
      required: true,
      validator: (value) => {
        return value.seriesData && Array.isArray(value.seriesData);
      },
    },
    // tooltip 展示模式：决定展示斗争+基建 / 斗争+兵力 / 斗争+舆论
    tooltipMode: {
      type: String,
      default: "struggle-infra", // struggle-infra | struggle-troops | struggle-opinion
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "400px",
    },
  },
  data() {
    return {
      chartInstance: null,
    };
  },
  watch: {
    chartData: {
      handler() {
        this.updateChart();
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart();
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    initChart() {
      if (this.$refs.chartDom) {
        this.chartInstance = echarts.init(this.$refs.chartDom);
        this.updateChart();
      }
    },
    updateChart() {
      if (!this.chartInstance) return;
      const series = this.formatSeries();
      const option = this.getOption(series);
      this.chartInstance.setOption(option);
    },
    formatSeries() {
      const {
        seriesData,
        showArea,
        showShadow,
        showSymbol,
        symbolSize,
        smooth,
      } = this.chartData;
      const result = [];

      seriesData.forEach((item) => {
        const lineItem = {
          name: item.name,
          type: "line",
          smooth: smooth || false,
          symbol: "circle",
          showSymbol: showSymbol || false,
          symbolSize: symbolSize || 5,
          itemStyle: {
            color: `rgb(${item.color})`,
            borderWidth: 2,
            borderColor: "#fff",
          },
          lineStyle: {
            color: `rgb(${item.color})`,
            width: item.width || 2,
            type: "solid",
          },
          data: item.data,
        };

        if (showArea) {
          lineItem.areaStyle = {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: `rgba(${item.color}, 0.5)` },
                { offset: 0.8, color: `rgba(${item.color}, 0.02)` },
              ],
            },
          };
        }

        result.push(lineItem);

        if (showShadow) {
          result.push({
            name: "",
            type: "line",
            symbolSize: 0,
            smooth: smooth || false,
            itemStyle: { color: `rgba(${item.color}, 0.25)` },
            lineStyle: { width: 10 },
            tooltip: { show: false },
            data: item.data,
          });
        }
      });

      return result;
    },
    getOption(series) {
      const { xAxisData, yAxisName } = this.chartData;
      const self = this;
      return {
        grid: {
          left: "5%",
          right: "5%",
          top: "15%",
          bottom: "5%",
          containLabel: true,
        },
        legend: {
          width: "100%",
          right: "center",
          top: 0,
          textStyle: { color: "#fff", padding: [0, 0, 0, 5] },
          itemStyle: { borderWidth: 0 },
          icon: "rect",
          itemWidth: 24,
          itemHeight: 4,
          itemGap: 35,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: { type: "line" },
          backgroundColor: "rgba(0,0,0,0.7)",
          borderColor: "rgba(255,255,255,0.2)",
          textStyle: {
            fontSize: 12,
            color: "#ffffff",
          },
          extraCssText:
            "box-shadow: 0 0 12px rgba(0,0,0,0.5);border-radius:6px;padding:10px 14px;",
          formatter(params) {
            return self.buildTooltipContent(params);
          },
        },
        xAxis: {
          type: "category",
          data: xAxisData,
          axisTick: { show: false },
          axisLine: {
            symbol: ["none", "arrow"],
            symbolSize: [12, 12],
            symbolOffset: [0, 12],
            lineStyle: { color: "#0B5EA0" },
          },
          axisLabel: {
            margin: 20,
            textStyle: { color: "rgb(183,227,252)" },
          },
        },
        yAxis: [
          {
            type: "value",
            name: yAxisName?.[0] || "单位:KWH",
            nameGap: 20,
            nameTextStyle: { color: "#78C0E6" },
            axisTick: { show: false },
            axisLine: { show: true, lineStyle: { color: "#0C5B9B" } },
            splitLine: { show: true, lineStyle: { color: "#11456F" } },
            splitArea: { show: true, areaStyle: { color: "rgba(36,85,134,.2)" } },
            axisLabel: { margin: 20, textStyle: { color: "#78C0E6" } },
          },
          {
            type: "value",
            nameGap: 40,
            nameTextStyle: { color: "#78C0E6" },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLine: { show: false },
            axisLabel: { margin: 20, textStyle: { color: "#78C0E6" } },
          },
        ],
        series,
      };
    },
    // 根据不同模式构建 tooltip 内容
    buildTooltipContent(params) {
      if (!params || !params.length) return "";
      const year = params[0].axisValue || params[0].name || "";

      let header = `<div style="font-size:14px;color:#fff;margin-bottom:6px;">${year} 年</div>`;
      let struggleBlock = `
        <div style="margin-top:4px;">
          <div style="font-size:13px;color:#4FC3F7;margin-bottom:2px;">斗争</div>
          <div style="font-size:12px;line-height:1.6;color:#E0F7FA;">
            冲突次数：20 次<br/>
            涉及兵力：35,400 人次<br/>
            涉及地点：15 处<br/>
            物资总量：20 吨<br/>
            装备使用量：50,000 件次<br/>
            火药使用量：30,500 公斤
          </div>
        </div>`;

      let secondBlock = "";

      if (this.tooltipMode === "struggle-infra") {
        // 斗争 + 基建
        secondBlock = `
          <div style="margin-top:8px;border-top:1px dashed rgba(120,192,230,0.3);padding-top:6px;">
            <div style="font-size:13px;color:#A5D6A7;margin-bottom:2px;">基建</div>
            <div style="font-size:12px;line-height:1.6;color:#E8F5E9;">
              道路长度：30,000 公里<br/>
              道路密度：2,000 公里/平方千米<br/>
              能源通道：45,000 公里<br/>
              管道密度：3,000 公里/平方千米<br/>
              基站数量：3,000 个<br/>
              信号覆盖率：87%
            </div>
          </div>`;
      } else if (this.tooltipMode === "struggle-troops") {
        // 斗争 + 兵力
        secondBlock = `
          <div style="margin-top:8px;border-top:1px dashed rgba(120,192,230,0.3);padding-top:6px;">
            <div style="font-size:13px;color:#FFCC80;margin-bottom:2px;">兵力</div>
            <div style="font-size:12px;line-height:1.6;color:#FFF3E0;">
              总兵力：15,000 人<br/>
              技术人数：5,000 人<br/>
              装备覆盖率：250%<br/>
              机动性能：150 公里/天<br/>
              补给半径：5 公里<br/>
              士兵训练度：97%
            </div>
          </div>`;
      } else if (this.tooltipMode === "struggle-opinion") {
        // 斗争 + 舆论（含“态度”可视化条形）
        secondBlock = `
          <div style="margin-top:8px;border-top:1px dashed rgba(120,192,230,0.3);padding-top:6px;">
            <div style="font-size:13px;color:#FFAB91;margin-bottom:2px;">舆论</div>
            <div style="font-size:12px;line-height:1.6;color:#FFECE3;">
              舆论总数：20,000 条<br/>
              正面比例：80%<br/>
              国内舆论数：17,000 条<br/>
              国际舆论数：3,000 条
            </div>
            <div style="margin-top:6px;">
              <div style="font-size:12px;color:#FFF;margin-bottom:2px;">民众态度</div>
              <div style="width:180px;height:8px;background:rgba(255,255,255,0.1);border-radius:4px;overflow:hidden;">
                <div style="width:78%;height:100%;background:linear-gradient(90deg,#4CAF50,#8BC34A);"></div>
              </div>
              <div style="font-size:11px;color:#C8E6C9;margin-top:2px;">支持占比约 78%</div>
            </div>
            <div style="margin-top:6px;">
              <div style="font-size:12px;color:#FFF;margin-bottom:2px;">国际态度</div>
              <div style="width:180px;height:8px;background:rgba(255,255,255,0.1);border-radius:4px;overflow:hidden;">
                <div style="width:55%;height:100%;background:linear-gradient(90deg,#29B6F6,#81D4FA);"></div>
              </div>
              <div style="font-size:11px;color:#B3E5FC;margin-top:2px;">关注 / 中性占比约 55%</div>
            </div>
          </div>`;
      }

      return header + struggleBlock + secondBlock;
    },
    handleResize() {
      this.chartInstance?.resize();
    },
  },
};
</script>

<style scoped>
.line-chart {
  position: relative;
}
.chart-inner {
  width: 100%;
  height: calc(100% - 60px);
}
</style>