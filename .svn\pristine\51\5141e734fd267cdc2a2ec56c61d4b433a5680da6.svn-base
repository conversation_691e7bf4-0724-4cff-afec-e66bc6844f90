<template>
  <div class="old-device-chart">
    <!-- 标题放在图表容器上方 -->
    <div class="chart-title">{{ title }}</div>
    <!-- ECharts 容器 -->
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'OldDeviceChart',
  props: {
    chartData: {
      type: Array,
      default: () => [
        { value: 2356, name: '0-10' },
        { value: 2356, name: '10-20' },
        { value: 2356, name: '20-30' },
        { value: 2356, name: '30-40' },
        { value: 2356, name: '40-50' },
        { value: 2356, name: '50以上' }
      ]
    },
    title: {
      type: String,
      default: '老旧设备'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      chartInstance: null,
      colors: [
        ['#42f4ff', '#07bbd0'],
        ['#f9d55c', '#f9d55c00'],
        ['#e3a207', '#e3a20700'],
        ['#e37107', '#e3710700'],
        ['#4ff0b0', '#4ff0b000'],
        ['#2d8dff', '#3d7ffe00'],
        ['#16bfff', '#1278c400']
      ]
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chartInstance) this.chartInstance.dispose()
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    chartData: {
      handler(newVal) {
        if (this.chartInstance) {
          this.chartInstance.setOption({
            series: [...this.getSeriesConfig(newVal)]
          })
        }
      },
      deep: true
    }
  },
  methods: {
    initChart() {
      const container = this.$refs.chartContainer
      container.style.width = this.width
      container.style.height = this.height
      this.chartInstance = echarts.init(container)
      this.chartInstance.setOption(this.getChartOption(this.chartData))
    },

    getChartOption(data) {
      return {
        color: this.getGradientColors(),
        grid: { top: 40, left: 40, right: 40, bottom: 20, containLabel: true },
        tooltip: { show: true },
        series: this.getSeriesConfig(data)
      }
    },

    getSeriesConfig(data) {
      return [
        {
          name: '外圆背景',
          type: 'pie',
          z: 0,
          silent: true,
          radius: ['35%', '70%'],
          label: { show: false },
          itemStyle: { color: '#0A3994' },
          data: [1]
        },
        {
          name: '数据',
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['55%', '70%'],
          z: 3,
          label: {
            show: true,
            color: '#fff',
            fontSize: 14,
            lineHeight: 20,
            formatter: '{c}\t\t{d}%\n{b}'
          },
          labelLine: { length: 30, length2: 15 },
          tooltip: { show: false },
          data: data
        },
        {
          name: '內园虚线',
          type: 'pie',
          z: 0,
          silent: true,
          radius: ['30%', '30%'],
          label: { show: false },
          itemStyle: {
            color: 'none',
            borderColor: '#1C8AA1',
            borderType: [5, 8],
            borderDashOffset: 20
          },
          data: [1]
        }
      ]
    },

    getGradientColors() {
      return this.colors.map(color => ({
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 1, color: color[1] },
          { offset: 0, color: color[0] }
        ],
        global: false
      }))
    },

    handleResize() {
      this.chartInstance && this.chartInstance.resize()
    }
  }
}
</script>

<style scoped>
.old-device-chart {
  padding: 10px;
}

/* 标题样式：居中、白色、黑体、18px */
.chart-title {
  width: 100%;
  text-align: center; /* 居中 */
  color: #ffffff; /* 白色 */
  font-weight: bold; /* 黑体（粗体） */
  font-size: 18px; /* 大小18px */
  margin-bottom: 10px; /* 与图表间距 */
}

.chart-container {
  width: 100%;
  height: 100%;
  min-width: 500px;
  min-height: 300px;
}
</style>