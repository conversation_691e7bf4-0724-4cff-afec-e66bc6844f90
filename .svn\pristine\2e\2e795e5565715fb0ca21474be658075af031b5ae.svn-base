import request from '@/utils/request'

// 开源集成分类列表查询
export function getOpenSourceIntegrationList(query) {
  return request({
    url: '/dataaccess/classificationSystem/list',
    method: 'get',
    params: query
  })
}

// 开源集成分类树查询
export function getOpenSourceIntegrationTree() {
  return request({
    url: '/dataaccess/classificationSystem/tree',
    method: 'get'
  })
}

// 开源集成分类详情查询
export function getOpenSourceIntegrationDetail(id) {
  return request({
    url: '/dataaccess/openSourceIntegration/' + id,
    method: 'get'
  })
}

// 开源集成分类新增
export function addOpenSourceIntegration(data) {
  return request({
    url: '/dataaccess/classificationSystem',
    method: 'post',
    data: data
  })
}

// 开源集成分类编辑
export function updateOpenSourceIntegration(data) {
  return request({
    url: '/dataaccess/classificationSystem',
    method: 'put',
    data: data
  })
}

// 开源集成分类删除（单条和批量删除共用）
export function deleteOpenSourceIntegration(ids) {
  return request({
    url: '/dataaccess/classificationSystem/' + ids,
    method: 'delete'
  })
}

// 开源集成分类导出
export function exportOpenSourceIntegration(query) {
  return request({
    url: '/dataaccess/classificationSystem/export',
    method: 'post',
    data: query
  })
}

// 获取分类编码
export function getClassificationCode() {
  return request({
    url: '/dataaccess/classificationSystem/getCode',
    method: 'get'
  })
}
