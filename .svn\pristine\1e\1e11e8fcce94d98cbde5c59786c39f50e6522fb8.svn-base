import request from '@/utils/request'

// 资产概览-数据总量
export function getMonthSituation() {
  return request({
    url: '/data/resource/getMonthSituation',
    method: 'get'
  })
}

// 资产概览-今日新增
export function getTodaySituation() {
  return request({
    url: '/data/resource/getTodaySituation',
    method: 'get'
  })
}

// 资产概览-专题数量
export function selectMonthlyClassificationSystem() {
  return request({
    url: '/data/resource/selectMonthlyClassificationSystem',
    method: 'get'
  })
}

// 资产概览-开源引接数量
export function selectOpenSourceDailyTrend() {
  return request({
    url: '/data/resource/selectOpenSourceDailyTrend',
    method: 'get'
  })
}

// 按资源类别分析饼状图
export function selectResourceCategory() {
  return request({
    url: '/data/resource/selectResourceCategory',
    method: 'get'
  })
}

// 按资源来源分析饼状图
export function selectDataSourceDistribution() {
  return request({    
    url: 'data/resource/selectDataSourceDistribution',
    method: 'get'
  })
}

// 按资源主题分析饼状图
export function selectThemeDistribution() {
  return request({
    url: '/data/resource/selectThemeDistribution',
    method: 'get'
  })
}

// 资源入库趋势折线图
export function selectMonthlyTotal() {
  return request({
    url: '/data/resource/selectMonthlyTotal',
    method: 'get'
  })
}

// 资源检索获取发布主体
export function getPublisherLabelList(data) {
  return request({
    url: '/api/search/getPublisherLabelList',
    method: 'get',
    params: data
  })
}

// 资源检索获取主体标签
export function getMediatags(data) {
  return request({
    url: '/api/search/getMediatags',
    method: 'get',
    params: data
  })
}

// 资源检索获取资源类别
export function data_resource_category(data) {
  return request({
    url: '/system/dict/data/type/data_resource_category',
    method: 'get',
    params: data
  })
}

// 资源检索获取主题/专题下拉框
export function selectListBySystemType(systemType) {
  return request({
    url: '/datamanger/asset/construction/selectListBySystemType',
    method: 'get',
    params: { systemType }
  })
}

// 资源检索获取来源下拉框
export function data_resource_sources(data) {
  return request({
    url: 'system/dict/data/type/data_resource_sources',
    method: 'get',
    params: data
  })
}

// 资源检索获取情感下拉框
export function manual_main_sentiment(data) {
  return request({
    url: 'system/dict/data/type/manual_main_sentiment',
    method: 'get',
    params: data
  })
}

// 资源检索搜索
export function search(data) {
  return request({
    url: '/api/search/query',
    method: 'post',
    data
  })
}

// 资源检索翻译
export function translate(params) {
  return request({
    url: '/api/search/translate',
    method: 'get',
    params
  })
}

// 资源检索批量导出
// export function exportExcel(id) {
//   return request({
//     url: '/api/search/export',
//     method: 'post',
//     params: id,
//     responseType: 'blob'
//   })
// }
