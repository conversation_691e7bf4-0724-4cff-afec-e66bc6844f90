import request from '@/utils/request'

// 查询文件导入任务列表
export function getFileImportList(query) {
  return request({
    url: '/dataaccess/fileimport/list',
    method: 'get',
    params: query
  })
}

// 获取文件导入任务详情
export function getFileImportDetail(taskId) {
  return request({
    url: '/dataaccess/fileimport/detail',
    method: 'get',
    params: { taskId }
  })
}

// 获取任务运行日志
export function getDialogRunLogCounts(taskId) {
  return request({
    url: '/dataaccess/fileimport/statisticsByTaskAsMap',
    method: 'get',
    params: { taskId }
  })
}
// 根据文件ID查询事件名称
export function getFileIdFindNames(id) {
  return request({
    url: '/dataaccess/fileimport/getManualWordEntriesByFileId',
    method: 'get',
    params: { id }
  })
}


// 获取任务运行日志列表
export function getDialogRunLog(query) {
  return request({
    url: '/dataaccess/fileimport/getFilesByStatus',
    method: 'get',
    params: query
  })
}

// 上传文件和新增任务
export function uploadFileImport(formData) {
  return request({
    url: '/dataaccess/fileimport/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 编辑任务
export function editFileImport(formData) {
  return request({
    url: '/dataaccess/fileimport/edit',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除文件导入任务（批量删除）
export function deleteFileImport(ids) {
  return request({
    url: `/dataaccess/fileimport/${ids}`,
    method: 'delete'
  })
}

// 批量导出
export function exportFileImport(query) {
  return request({
    url: '/dataaccess/fileimport/export',
    method: 'post',
    data: query,
    responseType: 'blob'
  })
}

// 下载模板
export function downloadTemplate(category) {
  return request({
    url: `/dataaccess/fileimport/download/template`,
    method: 'get',
    params: { category },
    responseType: 'blob'
  })
}

// 终止运行任务
export function stopFileImportTask(taskId) {
  return request({
    url: `/dataaccess/fileimport/stop`,
    method: 'get',
    params: { taskId }
  })
}

// 重新运行任务
export function rerunFileImportTask(taskId) {
  return request({
    url: `/dataaccess/fileimport/retry`,
    method: 'get',
    params: { taskId }
  })
}

// 获取任务运行日志
export function getFileImportRunLog(id) {
  return request({
    url: `/dataaccess/fileimport/runlog/${id}`,
    method: 'get'
  })
}

// 下载任务文件
export function downloadFileImportFile(id) {
  return request({
    url: `/dataaccess/fileimport/downloadFile`,
    method: 'get',
    responseType: 'blob',
    params: { id }
  })
}

export function getDataCountsInfo(query) {
  return request({
    url: '/dataaccess/fileimport/getdataCount',
    method: 'get',
    params: query
  })
}
// getDataCountsInfo