<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes
        :horizontal="this.$store.getters.device === 'mobile'"
        class="default-theme"
      >
        <!--左侧树-->
        <pane size="16" class="treeDiv">
          <el-col>
            <el-button
              class="addClass"
              size="small"
              type="success"
              @click="handleAddGroup"
              v-hasPermi="['data:datamanage:dataLabelAdd']"
              >创建标签组</el-button
            >
            <div class="head-container">
              <el-input
                v-model="tagName"
                placeholder="搜索标签名称"
                clearable
                size="small"
                prefix-icon="el-icon-search"
                style="margin-bottom: 20px"
              />
            </div>
            <div class="head-container">
              <el-tree
                :data="deptOptions"
                :props="defaultProps"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="tree"
                node-key="id"
                default-expand-all
                highlight-current
                @node-click="handleNodeClick"
              >
                <span class="custom-tree-node" slot-scope="{ data }">
                  <span>
                    <dict-tag
                      class="dictTagClass"
                      size="mini"
                      :options="dict.type.data_resources_system"
                      :value="data.systemType"
                    />
                    {{ data.label }}
                  </span>
                </span>
              </el-tree>
            </div>
          </el-col>
        </pane>
        <!--右侧内容区-->
        <pane size="84">
          <el-col>
            <!-- 列表模式（默认） -->
            <template v-if="viewMode === 'list'">
              <el-form
                :model="queryParams"
                ref="queryForm"
                size="small"
                :inline="true"
                v-show="showSearch"
                label-width="68px"
              >
                <el-form-item label="标签名称" prop="tagName">
                  <el-input
                    v-model="queryParams.tagName"
                    placeholder="请输入标签名称"
                    clearable
                    style="width: 240px"
                    @keyup.enter.native="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="所属组" prop="groupId">
                  <el-select
                    v-model="queryParams.groupId"
                    placeholder="所属组"
                    clearable
                    style="width: 240px"
                  >
                    <el-option
                      v-for="group in firstLevelGroups"
                      :key="group.id"
                      :label="group.label"
                      :value="group.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                  <el-select
                    v-model="queryParams.status"
                    placeholder="状态"
                    clearable
                    style="width: 240px"
                  >
                    <el-option label="启用" value="1" />
                    <el-option label="禁用" value="2" />
                  </el-select>
                </el-form-item>
                <el-form-item label="创建时间">
                  <el-date-picker
                    v-model="dateRange"
                    style="width: 240px"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="daterange"
                    range-separator="-"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-search"
                    size="mini"
                    @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button
                    icon="el-icon-refresh"
                    size="mini"
                    @click="resetQuery"
                    >重置</el-button
                  >
                </el-form-item>
              </el-form>

              <!-- <el-row :gutter="10" class="mb8">
                <right-toolbar
                  :showSearch.sync="showSearch"
                  @queryTable="getList"
                  :columns="columns"
                ></right-toolbar>
              </el-row> -->

              <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                  <el-button
                    type="success"
                    plain
                    icon="el-icon-upload2"
                    size="mini"
                    :disabled="multiple"
                    @click="handleExport"
                    v-hasPermi="[
                      'data:datamanage:dataLabelExport',
                    ]"
                    >批量导出</el-button
                  >
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="danger"
                    plain
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleBatchDelete"
                    v-hasPermi="[
                      'data:datamanage:dataLabelDelete',
                    ]"
                    >批量删除</el-button
                  >
                </el-col>
                <right-toolbar
                  :showSearch.sync="showSearch"
                  @queryTable="getList"
                  :columns="columns"
                ></right-toolbar>
              </el-row>

              <el-table
                v-loading="loading"
                :data="categoryList"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  label="序号"
                  type="index"
                  width="60"
                  align="center"
                />
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="标签名称" prop="labelName" sortable />
                <el-table-column label="所属组" prop="parentCode" sortable>
                  <template slot-scope="scope">
                    {{
                      scope.row.parentCode ||
                      getGroupNameById(scope.row.parentId)
                    }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态">
                  <template slot-scope="scope">
                    <el-tag
                      :key="scope.row.status"
                      size="small"
                      :type="getTagType(scope.row.status)"
                      effect="dark"
                    >
                      {{ getTagLabel(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  label="创建人"
                  prop="createByName"
                  :show-overflow-tooltip="true"
                />
                <el-table-column
                  label="创建时间"
                  align="center"
                  sortable
                  prop="createTime"
                  width="180"
                >
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.createTime) }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="操作"
                  align="center"
                  class-name="small-padding fixed-width"
                >
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      @click="handleUpdate(scope.row)"
                      v-hasPermi="[
                        'data:datamanage:dataLabelUpdate',
                      ]"
                      >编辑</el-button
                    >
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-delete"
                      @click="handleDelete(scope.row)"
                      v-hasPermi="[
                        'data:datamanage:dataLabelDelete',
                      ]"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>

              <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getList"
              />
            </template>

            <!-- 标签组详情模式 -->
            <template v-if="viewMode === 'groupDetail'">
              <div class="detail-container">
                <div class="detail-header">
                  <h3>标签组详情</h3>
                  <div class="detail-actions">
                    <el-button
                      type="primary"
                      size="mini"
                      icon="el-icon-plus"
                      @click="handleAddTag"
                      >新增标签</el-button
                    >
                    <el-button
                      type="primary"
                      size="mini"
                      icon="el-icon-edit"
                      @click="handleEditGroup"
                      >编辑</el-button
                    >
                    <el-button
                      type="danger"
                      size="mini"
                      icon="el-icon-delete"
                      @click="handleDeleteGroup"
                      >删除</el-button
                    >
                    <el-button
                      size="mini"
                      icon="el-icon-back"
                      @click="handleBackUp"
                      >返回</el-button
                    >
                  </div>
                </div>

                <el-descriptions :column="2" border>
                  <el-descriptions-item
                    label="标签组名称"
                    label-class-name="my-label"
                    content-class-name="my-content"
                    >{{ groupDetail.groupName }}</el-descriptions-item
                  >
                  <el-descriptions-item label="状态">
                    <el-tag
                      :key="groupDetail.status"
                      size="small"
                      :type="getTagType(groupDetail.status)"
                      effect="dark"
                    >
                      {{ getTagLabel(groupDetail.status) }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="创建人">
                    <el-tag size="small">{{ groupDetail.createByName }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="创建时间">{{
                    parseTime(groupDetail.createTime)
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="描述"
                    :contentStyle="{ 'text-align': 'left' }"
                    >{{ groupDetail.description }}</el-descriptions-item
                  >
                </el-descriptions>

                <el-form
                  v-if="isGroupEditing"
                  ref="groupDetailForm"
                  :model="groupDetail"
                  label-width="120px"
                  :disabled="!isGroupEditing"
                  class="tag-detail-form"
                >
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="标签组名称">
                        <el-input
                          v-model="groupDetail.groupName"
                          placeholder="标签组名称"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="状态">
                        <el-radio-group v-model="groupDetail.status">
                          <el-radio label="1">启用</el-radio>
                          <el-radio label="2">禁用</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="描述">
                        <el-input
                          v-model="groupDetail.description"
                          type="textarea"
                          :rows="3"
                          placeholder="描述"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="创建人">
                        <span>{{ groupDetail.createByName }}</span>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="创建时间">
                        <span>{{ parseTime(groupDetail.createTime) }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-if="isGroupEditing">
                    <el-col :span="24">
                      <el-form-item>
                        <el-button
                          type="primary"
                          @click="submitGroupEdit"
                          size="mini"
                          >保存</el-button
                        >
                        <el-button @click="cancelGroupEdit" size="mini"
                          >取消</el-button
                        >
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>

                <!-- 新增标签表单 -->
                <div v-if="showAddTagForm" class="add-tag-form">
                  <h4>新增标签</h4>
                  <el-form
                    ref="addTagForm"
                    :model="tagForm"
                    :rules="tagRules"
                    label-width="120px"
                  >
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="标签名称" prop="tagName">
                          <el-input
                            v-model="tagForm.tagName"
                            placeholder="请输入标签名称"
                            maxlength="50"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="所属组" prop="groupId">
                          <el-input
                            v-model="groupDetail.groupName"
                            disabled
                            placeholder="所属组"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="24">
                        <el-form-item label="描述" prop="description">
                          <el-input
                            v-model="tagForm.description"
                            type="textarea"
                            placeholder="请输入描述"
                            :rows="3"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="状态" prop="status">
                          <el-radio-group v-model="tagForm.status">
                            <el-radio label="1">启用</el-radio>
                            <el-radio label="2">禁用</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="24">
                        <el-form-item>
                          <el-button
                            type="primary"
                            @click="submitTag"
                            size="mini"
                            >发布</el-button
                          >
                          <el-button @click="cancelAddTag" size="mini"
                            >取消</el-button
                          >
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </div>
              </div>
            </template>

            <!-- 标签详情模式 -->
            <template v-if="viewMode === 'tagDetail'">
              <div class="detail-container">
                <div class="detail-header">
                  <h3>标签详情</h3>
                  <div class="detail-actions">
                    <el-button
                      type="primary"
                      size="mini"
                      icon="el-icon-edit"
                      @click="handleEditTag"
                      >编辑</el-button
                    >
                    <el-button
                      type="danger"
                      size="mini"
                      icon="el-icon-delete"
                      @click="handleDeleteTag"
                      >删除</el-button
                    >
                    <el-button
                      size="mini"
                      icon="el-icon-back"
                      @click="handleBackUp"
                      >返回</el-button
                    >
                  </div>
                </div>

                <el-descriptions :column="2" border>
                  <el-descriptions-item
                    label="标签名称"
                    label-class-name="my-label"
                    content-class-name="my-content"
                    >{{ tagDetail.tagName }}</el-descriptions-item
                  >
                  <el-descriptions-item
                    label="所属组"
                    label-class-name="my-label"
                    content-class-name="my-content"
                    >{{ tagDetail.groupName }}</el-descriptions-item
                  >
                  <el-descriptions-item label="状态">
                    <el-tag
                      :key="tagDetail.status"
                      size="small"
                      :type="getTagType(tagDetail.status)"
                      effect="dark"
                    >
                      {{ getTagLabel(tagDetail.status) }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="创建人">
                    <el-tag size="small">{{ tagDetail.createByName }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="创建时间">{{
                    parseTime(tagDetail.createTime)
                  }}</el-descriptions-item>
                  <el-descriptions-item
                    label="描述"
                    :contentStyle="{ 'text-align': 'left' }"
                    >{{ tagDetail.description }}</el-descriptions-item
                  >
                </el-descriptions>

                <el-form
                  v-if="isTagEditing"
                  ref="tagDetailForm"
                  :model="tagDetail"
                  label-width="120px"
                  :disabled="!isTagEditing"
                  class="tag-detail-form"
                >
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="标签名称">
                        <el-input
                          v-model="tagDetail.tagName"
                          placeholder="标签名称"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="所属组">
                        <el-input
                          v-model="tagDetail.groupName"
                          disabled
                          placeholder="所属组"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item label="描述">
                        <el-input
                          v-model="tagDetail.description"
                          type="textarea"
                          :rows="3"
                          placeholder="描述"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="状态">
                        <el-radio-group v-model="tagDetail.status">
                          <el-radio label="1">启用</el-radio>
                          <el-radio label="2">禁用</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="创建人">
                        <span>{{ tagDetail.createByName }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="创建时间">
                        <span>{{ parseTime(tagDetail.createTime) }}</span>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row v-if="isTagEditing">
                    <el-col :span="24">
                      <el-form-item>
                        <el-button
                          type="primary"
                          @click="submitTagEdit"
                          size="mini"
                          >保存</el-button
                        >
                        <el-button @click="cancelTagEdit" size="mini"
                          >取消</el-button
                        >
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
            </template>
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 创建标签组对话框 -->
    <el-dialog
      title="创建标签组"
      :visible.sync="groupDialogOpen"
      width="600px"
      append-to-body
    >
      <el-form
        ref="groupForm"
        :model="groupForm"
        :rules="groupRules"
        label-width="120px"
      >
        <el-form-item label="标签组名称" prop="groupName">
          <el-input
            v-model="groupForm.groupName"
            placeholder="请输入标签组名称"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="groupForm.description"
            type="textarea"
            placeholder="请输入描述"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="groupForm.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGroupForm" size="mini"
          >确 定</el-button
        >
        <el-button @click="cancelGroupDialog" size="mini">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDataLabelList,
  getDataLabelTree,
  getTagGroupList,
  addDataLabel,
  updateDataLabel,
  deleteDataLabel,
  getDataLabelDetail,
  exportDataLabel,
} from "@/api/data/dataRemark";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";

export default {
  name: "DataManage",
  dicts: ["data_resources_system", "data_resources_status"],
  components: { Splitpanes, Pane },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分类表格数据（标签列表）
      categoryList: [],
      // 视图模式：list-列表，groupDetail-标签组详情，tagDetail-标签详情
      viewMode: "list",
      // 所有部门树选项
      deptOptions: undefined,
      // 一级标签组列表（用于搜索下拉框）
      firstLevelGroups: [],
      // 标签名称（左侧搜索）
      tagName: undefined,
      // 当前选中的节点
      currentNode: null,
      // 当前选中的节点层级（1-标签组，2-标签）
      currentNodeLevel: null,
      // 日期范围
      dateRange: [],
      // 是否显示新增标签表单
      showAddTagForm: false,
      // 标签组编辑状态
      isGroupEditing: false,
      // 标签编辑状态
      isTagEditing: false,
      // 标签组详情
      groupDetail: {
        id: undefined,
        groupName: "",
        description: "",
        status: "1",
        createByName: "",
        createTime: "",
      },
      // 标签详情
      tagDetail: {
        id: undefined,
        tagName: "",
        groupName: "",
        groupId: undefined,
        description: "",
        status: "1",
        createByName: "",
        createTime: "",
      },
      // 标签表单
      tagForm: {
        tagName: "",
        groupId: undefined,
        description: "",
        status: "1",
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tagName: undefined,
        groupId: undefined,
        status: undefined,
        kssj: undefined,
        jssj: undefined,
      },
      // 列信息
      columns: [
        { key: 0, label: `序号`, visible: true },
        { key: 1, label: `标签名称`, visible: true },
        { key: 2, label: `所属组`, visible: true },
        { key: 3, label: `状态`, visible: true },
        { key: 4, label: `创建人`, visible: true },
        { key: 5, label: `创建时间`, visible: true },
      ],
      // 标签组表单
      groupForm: {
        groupName: "",
        description: "",
        status: "1",
      },
      // 标签组弹窗开关
      groupDialogOpen: false,
      // 标签组表单校验
      groupRules: {
        groupName: [
          { required: true, message: "标签组名称不能为空", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "标签组名称长度必须介于 2 和 50 之间",
            trigger: "blur",
          },
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
      },
      // 标签表单校验
      tagRules: {
        tagName: [
          { required: true, message: "标签名称不能为空", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "标签名称长度必须介于 2 和 50 之间",
            trigger: "blur",
          },
        ],
        groupId: [
          { required: true, message: "所属组不能为空", trigger: "change" },
        ],
        status: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    // 根据名称筛选树
    tagName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getList();
    this.getDeptTree();
  },
  methods: {
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "dataaccess/dataLabel/export",
        {
          ids: this.ids.join(","),
        },
        `数据标签_${new Date().getTime()}.xlsx`
      );
    },
    /** 批量删除按钮操作 */
    handleBatchDelete() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请选择要删除的数据");
        return;
      }
      this.$modal
        .confirm("是否确认删除选中的" + this.ids.length + "条数据项？")
        .then(() => {
          const ids = this.ids.join(",");
          deleteDataLabel(ids).then((response) => {
            this.getList();
            this.getDeptTree();
            this.$modal.msgSuccess("删除成功");
          });
        })
        .catch(() => {});
    },
    handleBackUp() {
      this.viewMode = "list";
      this.getList();
      this.getDeptTree();
    },
    /**
     * 根据status匹配字典，获取Tag的type（颜色）
     */
    getTagType(status) {
      // 新API：1-启用(绿色)，2-禁用(灰色)
      if (status === "1") return "success";
      if (status === "2") return "info";
      const matchItem = this.dict.type.data_resources_status.find(
        (item) => item.value === status
      );
      return matchItem ? matchItem.raw.listClass : "";
    },

    /**
     * 根据status匹配字典，获取Tag显示的文字
     */
    getTagLabel(status) {
      // 新API：1-启用，2-禁用
      if (status === "1") return "启用";
      if (status === "2") return "禁用";
      const matchItem = this.dict.type.data_resources_status.find(
        (item) => item.value === status
      );
      return matchItem ? matchItem.label : "未知";
    },
    /** 查询标签列表 */
    getList() {
      this.loading = true;
      // 处理日期范围参数和字段映射
      const params = {
        labelName: this.queryParams.tagName || "",
        parentId: this.queryParams.groupId || "",
        status: this.queryParams.status || "",
      };
      if (this.dateRange && this.dateRange.length === 2) {
        params.kssj = this.dateRange[0];
        params.jssj = this.dateRange[1];
      }

      getDataLabelList(params)
        .then((response) => {
          if (response.code === 200) {
            this.categoryList = response.rows || [];
            this.total = response.total || 0;
          } else {
            this.categoryList = [];
            this.total = 0;
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /** 查询树结构 */
    getDeptTree() {
      getDataLabelTree()
        .then((response) => {
          if (response.code === 200) {
            // 转换数据格式，适配el-tree组件
            this.deptOptions = this.transformDeptTreeData(response.data || []);
          } else {
            this.deptOptions = [];
          }
        })
        .catch(() => {
          this.deptOptions = [];
        });

      // 获取标签组列表（用于搜索下拉框）
      getTagGroupList()
        .then((response) => {
          if (response.code === 200) {
            this.firstLevelGroups = (response.data || []).map((item) => ({
              id: item.id,
              label: item.labelName,
            }));
          } else {
            this.firstLevelGroups = [];
          }
        })
        .catch(() => {
          this.firstLevelGroups = [];
        });
    },

    /** 转换树数据格式 */
    transformDeptTreeData(data) {
      return data.map((item) => ({
        id: item.id,
        label: item.labelName,
        systemType: item.systemType,
        disabled: false,
        level: item.parentId === null ? 1 : 2,
        parentId: item.parentId,
        children: item.children
          ? this.transformDeptTreeData(item.children)
          : [],
      }));
    },

    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 节点单击事件
    handleNodeClick(data) {
      this.currentNode = data;
      this.currentNodeLevel = data.level;

      if (data.level === 1) {
        // 点击一级节点（标签组）
        this.loadGroupDetail(data.id);
        this.viewMode = "groupDetail";
      } else if (data.level === 2) {
        // 点击二级节点（标签）
        this.loadTagDetail(data.id);
        this.viewMode = "tagDetail";
      }
    },

    /** 加载标签组详情 */
    loadGroupDetail(id) {
      getDataLabelDetail(id)
        .then((response) => {
          if (response.code === 200) {
            this.groupDetail = {
              id: response.data.id,
              groupName: response.data.labelName || "",
              description: response.data.description || "",
              status: String(response.data.status || "1"),
              createByName: response.data.createByName || "",
              createTime: response.data.createTime || "",
            };
            this.isGroupEditing = false;
            this.showAddTagForm = false;
          }
        })
        .catch(() => {
          this.$modal.msgError("获取标签组详情失败");
        });
    },

    /** 加载标签详情 */
    loadTagDetail(id) {
      getDataLabelDetail(id)
        .then((response) => {
          if (response.code === 200) {
            // 获取所属组名称
            const groupName =
              response.data.parentCode ||
              this.getGroupNameById(response.data.parentId);
            this.tagDetail = {
              id: response.data.id,
              tagName: response.data.labelName || "",
              groupName: groupName,
              groupId: response.data.parentId,
              description: response.data.description || "",
              status: String(response.data.status || "1"),
              createByName: response.data.createByName || "",
              createTime: response.data.createTime || "",
            };
            this.isTagEditing = false;
          }
        })
        .catch(() => {
          this.$modal.msgError("获取标签详情失败");
        });
    },

    /** 根据ID获取组名称 */
    getGroupNameById(groupId) {
      if (!groupId || !this.deptOptions) return "";
      const findGroup = (data) => {
        for (const item of data) {
          if (item.id === groupId) {
            return item.label || item.categoryName;
          }
          if (item.children && item.children.length > 0) {
            const found = findGroup(item.children);
            if (found) return found;
          }
        }
        return null;
      };
      return findGroup(this.deptOptions) || "";
    },

    /** 创建标签组 */
    handleAddGroup() {
      this.groupForm = {
        groupName: "",
        description: "",
        status: "1",
      };
      this.groupDialogOpen = true;
    },

    /** 提交标签组表单 */
    submitGroupForm() {
      this.$refs["groupForm"].validate((valid) => {
        if (valid) {
          // 标签组是一级节点，parentId为null或undefined
          const formData = {
            labelName: this.groupForm.groupName,
            parentId: null,
            description: this.groupForm.description || "",
            status: this.groupForm.status,
          };
          addDataLabel(formData)
            .then((response) => {
              if (response.code === 200) {
                this.$modal.msgSuccess("创建成功");
                this.groupDialogOpen = false;
                this.resetGroupForm();
                this.getDeptTree();
                // 如果当前在列表模式，刷新列表
                if (this.viewMode === "list") {
                  this.getList();
                }
              } else {
                this.$modal.msgError(response.msg || "创建失败");
              }
            })
            .catch(() => {
              this.$modal.msgError("创建失败");
            });
        }
      });
    },

    /** 取消标签组弹窗 */
    cancelGroupDialog() {
      this.groupDialogOpen = false;
      this.resetGroupForm();
    },

    /** 重置标签组表单 */
    resetGroupForm() {
      this.groupForm = {
        groupName: "",
        description: "",
        status: "1",
      };
      this.resetForm("groupForm");
    },

    /** 编辑标签组 */
    handleEditGroup() {
      this.isGroupEditing = true;
      this.showAddTagForm = false;
    },

    /** 提交标签组编辑 */
    submitGroupEdit() {
      const formData = {
        id: this.groupDetail.id,
        labelName: this.groupDetail.groupName,
        description: this.groupDetail.description || "",
        status: this.groupDetail.status,
        parentId: null,
      };
      updateDataLabel(formData)
        .then((response) => {
          if (response.code === 200) {
            this.$modal.msgSuccess("修改成功");
            this.isGroupEditing = false;
            this.getDeptTree();
            this.loadGroupDetail(this.groupDetail.id);
          } else {
            this.$modal.msgError(response.msg || "修改失败");
          }
        })
        .catch(() => {
          this.$modal.msgError("修改失败");
        });
    },

    /** 取消标签组编辑 */
    cancelGroupEdit() {
      this.isGroupEditing = false;
      this.loadGroupDetail(this.groupDetail.id);
    },

    /** 删除标签组 */
    handleDeleteGroup() {
      this.$modal
        .confirm('是否确认删除标签组"' + this.groupDetail.groupName + '"？')
        .then(() => {
          deleteDataLabel(this.groupDetail.id)
            .then((response) => {
              if (response.code === 200) {
                this.$modal.msgSuccess("删除成功");
                this.getDeptTree();
                this.viewMode = "list";
                this.getList();
              } else {
                this.$modal.msgError(response.msg || "删除失败");
              }
            })
            .catch(() => {
              this.$modal.msgError("删除失败");
            });
        })
        .catch(() => {});
    },

    /** 新增标签 */
    handleAddTag() {
      this.showAddTagForm = true;
      this.isGroupEditing = false;
      this.tagForm = {
        tagName: "",
        groupId: this.groupDetail.id,
        description: "",
        status: "1",
      };
    },

    /** 提交标签 */
    submitTag() {
      this.$refs["addTagForm"].validate((valid) => {
        if (valid) {
          const formData = {
            labelName: this.tagForm.tagName,
            parentId: this.tagForm.groupId,
            description: this.tagForm.description || "",
            status: this.tagForm.status,
          };
          addDataLabel(formData)
            .then((response) => {
              if (response.code === 200) {
                this.$modal.msgSuccess("新增成功");
                this.showAddTagForm = false;
                this.resetTagForm();
                this.getDeptTree();
                this.loadGroupDetail(this.groupDetail.id);
              } else {
                this.$modal.msgError(response.msg || "新增失败");
              }
            })
            .catch(() => {
              this.$modal.msgError("新增失败");
            });
        }
      });
    },

    /** 取消新增标签 */
    cancelAddTag() {
      this.showAddTagForm = false;
      this.resetTagForm();
    },

    /** 重置标签表单 */
    resetTagForm() {
      this.tagForm = {
        tagName: "",
        groupId: undefined,
        description: "",
        status: "1",
      };
      if (this.$refs.addTagForm) {
        this.$refs.addTagForm.resetFields();
      }
    },

    /** 编辑标签 */
    handleEditTag() {
      this.isTagEditing = true;
    },

    /** 提交标签编辑 */
    submitTagEdit() {
      const formData = {
        id: this.tagDetail.id,
        labelName: this.tagDetail.tagName,
        parentId: this.tagDetail.groupId,
        description: this.tagDetail.description || "",
        status: this.tagDetail.status,
      };
      updateDataLabel(formData)
        .then((response) => {
          if (response.code === 200) {
            this.$modal.msgSuccess("修改成功");
            this.isTagEditing = false;
            this.getDeptTree();
            this.loadTagDetail(this.tagDetail.id);
          } else {
            this.$modal.msgError(response.msg || "修改失败");
          }
        })
        .catch(() => {
          this.$modal.msgError("修改失败");
        });
    },

    /** 取消标签编辑 */
    cancelTagEdit() {
      this.isTagEditing = false;
      this.loadTagDetail(this.tagDetail.id);
    },

    /** 删除标签 */
    handleDeleteTag() {
      this.$modal
        .confirm('是否确认删除标签"' + this.tagDetail.tagName + '"？')
        .then(() => {
          deleteDataLabel(this.tagDetail.id)
            .then((response) => {
              if (response.code === 200) {
                this.$modal.msgSuccess("删除成功");
                this.getDeptTree();
                this.viewMode = "list";
                this.getList();
              } else {
                this.$modal.msgError(response.msg || "删除失败");
              }
            })
            .catch(() => {
              this.$modal.msgError("删除失败");
            });
        })
        .catch(() => {});
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.viewMode = "list";
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.groupId = undefined;
      this.queryParams.tagName = undefined;
      this.queryParams.status = undefined;
      this.$refs.tree.setCurrentKey(null);
      this.currentNode = null;
      this.viewMode = "list";
      this.handleQuery();
    },

    /** 修改按钮操作（列表中的编辑） */
    handleUpdate(row) {
      // 跳转到标签详情页
      this.loadTagDetail(row.id);
      this.viewMode = "tagDetail";
      // 选中对应的树节点
      this.$nextTick(() => {
        if (this.$refs.tree) {
          this.$refs.tree.setCurrentKey(row.id);
        }
      });
    },

    /** 删除按钮操作（列表中的删除） */
    handleDelete(row) {
      this.$modal
        .confirm(
          '是否确认删除标签"' +
            (row.labelName || row.tagName || row.categoryName) +
            '"？'
        )
        .then(() => {
          deleteDataLabel(row.id)
            .then((response) => {
              if (response.code === 200) {
                this.getList();
                this.getDeptTree();
                this.$modal.msgSuccess("删除成功");
              } else {
                this.$modal.msgError(response.msg || "删除失败");
              }
            })
            .catch(() => {
              this.$modal.msgError("删除失败");
            });
        })
        .catch(() => {});
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.treeDiv {
  position: relative;
}
.addClass {
  width: 100%;
  margin-bottom: 10px;
}
.dictTagClass {
  float: left;
  margin-right: 5px;
  ::v-deep .el-tag {
    height: 20px;
    line-height: 20px;
    font-size: 12px;
  }
}
.detail-container {
  padding: 20px;
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
    .detail-actions {
      button {
        margin-left: 10px;
      }
    }
  }
}
.add-tag-form {
  margin-top: 30px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  h4 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
  }
}

.tag-detail-form {
  padding-top: 30px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-top: 30px;
}
</style>