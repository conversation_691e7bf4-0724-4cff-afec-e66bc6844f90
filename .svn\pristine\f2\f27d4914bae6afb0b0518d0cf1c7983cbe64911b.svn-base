<template>
  <analysis-comparison
    :filter-config="filterConfig"
    :comparison-types="comparisonTypes"
    :table-columns="tableColumns"
    :chart-indicators="chartIndicators"
    :treedisabled="treedisabled"
    :showComparisonType="showComparisonType"
    :deptConfig="deptConfig"
    :tableData="tableData"
    @search="handleSearch"
    @treeclick="handleTreeClick"
    @radiochange="handleRadiochange"
  />
</template>

<script>
import AnalysisComparison from "@/components/AnalysisComparison";

export default {
  name: "ImportantAnalysis",
  components: {
    AnalysisComparison,
  },
  data() {
    return {
      treedisabled: true,
      showComparisonType: true,
      orgName: "黑龙江省自然资源厅",
      filterConfig: [
        // {
        //   id: 1,
        //   type: "select",
        //   key: "cs",
        //   placeholder: "处室",
        //   className: "rank-select",
        //   options: [{ label: "全部", value: "all" }],
        // },
        // {
        //   id: 2,
        //   type: "input",
        //   key: "name",
        //   placeholder: "姓名",
        //   className: "rank-select",
        //   // options: [
        //   //   { label: '全部', value: 'all' }
        //   // ]
        // },
        {
          id: 1,
          type: "select",
          key: "rank",
          placeholder: "机构类型",
          className: "rank-select",
          options: [
            { label: "全部", value: "all" },
            { label: "职能部门", value: "ejxcy" },
            { label: "非职能部门", value: "lzg" },
            { label: "事业单位", value: "cz" },
          ],
        },
      ],
      deptConfig: {
        data: [
          {
            id: 100,
            label: "黑龙江省自然资源厅",
            disabled: false,
            children: [
              {
                id: 101,
                label: "内设机构",
                disabled: false,
                children: [
                  { id: 103, label: "厅领导", disabled: false },
                  { id: 104, label: "两总师", disabled: false },
                  { id: 105, label: "调查处", disabled: false },
                  { id: 106, label: "登记局", disabled: false },
                  { id: 107, label: "权益处", disabled: false },
                  { id: 108, label: "利用处", disabled: false },
                  { id: 109, label: "规划局", disabled: false },
                  { id: 110, label: "管制处", disabled: false },
                  { id: 111, label: "修复处", disabled: false },
                  { id: 112, label: "耕保处", disabled: false },
                  { id: 113, label: "地勘处", disabled: false },
                  { id: 114, label: "矿权处", disabled: false },
                  { id: 115, label: "矿保处", disabled: false },
                  { id: 116, label: "执法局", disabled: false },
                  { id: 117, label: "督察处", disabled: false },
                  { id: 118, label: "办公室", disabled: false },
                  { id: 119, label: "综改处", disabled: false },
                  { id: 120, label: "法规处", disabled: false },
                  { id: 121, label: "科外处", disabled: false },
                  { id: 122, label: "信访处", disabled: false },
                  { id: 123, label: "财审处", disabled: false },
                  { id: 124, label: "人事处", disabled: false },
                  { id: 125, label: "机关党委", disabled: false },
                ],
              },
              {
                id: 102,
                label: "直属单位",
                disabled: false,
                children: [
                  { id: 126, label: "黑龙江省地质矿产局", disabled: false },
                  {
                    id: 127,
                    label: "黑龙江省国土空间规划研究院",
                    disabled: false,
                  },
                  {
                    id: 128,
                    label: "黑龙江省自然资源权益调查监测院",
                    disabled: false,
                  },
                  {
                    id: 129,
                    label: "黑龙江省自然资源生态保护修复监测中心",
                    disabled: false,
                  },
                  {
                    id: 130,
                    label: "黑龙江省自然资源和不动产登记中心",
                    disabled: false,
                  },
                  {
                    id: 131,
                    label: "黑龙江省自然资源技术保障中心",
                    disabled: false,
                  },
                  {
                    id: 132,
                    label: "黑龙江省地质环境监测总站",
                    disabled: false,
                  },
                  { id: 133, label: "黑龙江省地质资料档案馆", disabled: false },
                ],
              },
            ],
          },
        ],
        props: {
          children: "children",
          label: "label",
        },
      },
      comparisonTypes: [
        { value: 1, label: "与同类机构平均分比较分析" },
        { value: 2, label: "与厅内设机构平均分比较分析" },
      ],
      staffList: [
        { id: "1", name: "张老师", department: "调查处" },
        { id: "2", name: "王老师", department: "登记局" },
        { id: "3", name: "李老师", department: "权益处" },
      ],
      tableColumns: [
        { prop: "category", label: "类别" },
        { prop: "personalScore", label: "张老师（调查处）" },
        { prop: "averageScore", label: "全部重要岗位平均分" },
      ],
      tableData: [
        { category: "政治思想建设", personalScore: 80, averageScore: 85 },
        { category: "领导能力", personalScore: 70, averageScore: 75 },
        { category: "工作实绩", personalScore: 90, averageScore: 95 },
        { category: "党风廉政建设", personalScore: 85, averageScore: 90 },
        { category: "作风建设", personalScore: 75, averageScore: 80 },
      ],
      chartIndicators: [
        { name: "政治思想建设", max: 100 },
        { name: "领导能力", max: 100 },
        { name: "工作实绩", max: 100 },
        { name: "党风廉政建设", max: 100 },
        { name: "作风建设", max: 100 },
      ],
    };
  },
  methods: {
    handleSearch(filterValues) {
      console.log("搜索条件：", filterValues);
      // 实现搜索逻辑
    },
    handleStaffSelect(staffId) {
      console.log("选中的人员ID：", staffId);
      // 实现人员选择逻辑
    },
    handleTreeClick(data) {
      this.tableColumns[1].label = data.label;
      this.tableData = [
        { category: "政治思想建设", personalScore: 88, averageScore: 85 },
        { category: "领导能力", personalScore: 77, averageScore: 75 },
        { category: "工作实绩", personalScore: 99, averageScore: 95 },
        { category: "党风廉政建设", personalScore: 10, averageScore: 90 },
        { category: "作风建设", personalScore: 20, averageScore: 80 },
      ];
    },
    handleRadiochange(value) {
      if (value.value == 1) {
        this.tableColumns[2].label = "全部重要岗位平均分";
      } else if (value.value == 2) {
        this.tableColumns[2].label = "同处室其他公务员平均分";
      }
      this.tableData = [
        { category: "政治思想建设", personalScore: 88, averageScore: 85 },
        { category: "领导能力", personalScore: 77, averageScore: 75 },
        { category: "工作实绩", personalScore: 99, averageScore: 95 },
        { category: "党风廉政建设", personalScore: 10, averageScore: 90 },
        { category: "作风建设", personalScore: 20, averageScore: 20 },
      ];
    },
  },
};
</script>
