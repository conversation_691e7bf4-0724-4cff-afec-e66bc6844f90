<template>
  <div class="app-container home">
    <!-- <div class="appTitle">
      <div class="title">首页</div>
    </div> -->
    <div class="content-wrapper">
      <!-- <div class="info-row">
        <div class="info-box">
          <div class="col-wz">
            <span class="col_fk"></span>
            考核日期为2025年2月5日至2025年2月15日，尚未开始！
          </div>
          <div class="col-border"></div>
          <div class="col-wz">
            <span class="col_fk"></span>
            考核日期为2025年2月5日至2025年2月15日，尚未开始！
          </div>
        </div>
        <div class="tbWk">
          <div class="tbImg"></div>
        </div>
      </div> -->

      <div>
        <div class="hysy"><img src="./img/hysy.png" alt="" /></div>
        <div class="gwykhglxt">开源信息研判软件系统</div>
        <div class="tpImg2">
          <img src="./img/img02.png" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "3.8.9",
    };
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  height: 100%;
  background: url("./img/bg00.png") no-repeat center center fixed;
  background-size: cover;
  display: flex;
  flex-direction: column;

  .content-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }

  .info-row {
    width: 100%;
    max-width: 1200px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
  }

  .info-box {
    flex: 1;
    background-image: linear-gradient(87deg, #ffffff 0%, #ffffff 96%);
    box-shadow: 0px 2px 20px 0px rgba(20, 17, 117, 0.11);
    border-radius: 4px;
    padding: 2rem;
    opacity: 0.64;
  }

  .col-wz {
    font-size: clamp(16px, 2vw, 22px);
    color: #000000;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .col_fk {
    min-width: 8px;
    height: 8px;
    background: #2975e6;
    margin-right: 12px;
  }

  .col-border {
    width: 100%;
    height: 2px;
    background: #f2f2f3;
    margin: 1.5rem 0;
  }

  .tbWk {
    width: 30%;
    max-width: 329px;
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tbImg {
    width: 80%;
    height: 80%;
    background: url("./img/img01.png") no-repeat center center;
    background-size: contain;
  }
}

.appTitle {
  width: 100%;
  padding: 1rem;
  border-bottom: 1px solid #c1ccd9;

  .title {
    width: fit-content;
    color: #1b5dd8;
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #1b5dd8;
  }
}
.hysy {
  width: 159px;
  height: 49px;
  position: absolute;
  top: 207px;
  left: 623px;
}
.gwykhglxt {
  width: 332px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 287px;
  left: 677px;
  font-family: SourceHanSansSC-Regular;
  font-size: 32px;
  color: #333333;
  letter-spacing: 0.8px;
  text-align: center;
  font-weight: 400;
}
.tpImg2 {
  width: 413px;
  height: 413px;
  position: absolute;
  top: 343px;
  left: 619px;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media screen and (max-width: 768px) {
  .home {
    .info-row {
      flex-direction: column;
    }

    .tbWk {
      width: 50%;
    }

    .info-box {
      width: 100%;
    }
  }
}
</style>

