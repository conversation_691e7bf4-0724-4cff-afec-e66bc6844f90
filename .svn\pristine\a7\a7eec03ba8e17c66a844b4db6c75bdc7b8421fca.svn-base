@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: SourceHanSansSC-Regular-2;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

.width100 {
  width: 100%;
}


.file-list {
  /* margin-top: 24px; */
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;

  background: rgba(27, 126, 242, 0.1);
  border: 1px solid rgba(17, 80, 154, 1);
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-item:hover {
  background: rgba(27, 126, 242, 0.1);
  border-color: #409eff;
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  width: 44px;
  height: 44px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.file-icon svg {
  width: 24px;
  height: 24px;
}

.file-icon-excel {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.file-icon-zip {
  background: linear-gradient(135deg, #e6a23c 0%, #f0c78a 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.file-icon-text {
  background: linear-gradient(135deg, #409eff 0%, #79bbff 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.file-icon-file {
  background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.3);
}

.file-item:hover .file-icon {
  transform: scale(1.1) rotate(5deg);
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #ffffff;
  margin: 0;
}

.file-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}
.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #f5f7fa;
}

.action-btn svg {
  width: 18px;
  height: 18px;
}

.download-btn {
  color: #409eff;
}

.download-btn:hover {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}
.delete-btn {
  color: #f56c6c;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #f56c6c 0%, #f89898 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}




// zhangzuying add css start


.dialogTitle {
  position: absolute;
  top: 35px;
  color: #ffffff8c;
  font-family: SourceHanSansSC-Regular;
font-size: 14px;
color: #A9C7EA !important;
letter-spacing: 0;
font-weight: 400;
}
.dialogPopover {
  position: absolute;
  top: 15px;
  right: 50px;
}
.dialogText {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.secondTitle {
  font-size: 12px;
  font-weight: 400;
  color: #1f2f3d59;
}

.notice-container {
  width: 100%;
  margin: 20px auto;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-family: "Microsoft Yahei", sans-serif;
}

.notice-header {
  background-color: #d1e7ff;
  padding: 12px 20px;
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.notice-content {
  padding: 20px;
  background-color: #fff;
}

.notice-content h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 12px;
  font-weight: 600;
}

.notice-content ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-bottom: 20px;
}

.notice-content ul li {
  margin-bottom: 10px;
  line-height: 1.6;
  color: #555;
}

.notice-content p {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #555;
}
.my-label {
  background: #e1f3d8;
}

.my-content {
  background: #fde2e2;
}
.file-list {
  margin-top: 20px;
}

// 运行日志el-drawer 的样式  start

.fancy-drawer {
  background-color: #f9fafc;
}
.stats-container {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;
  flex-wrap: wrap;
  margin-top: 10px;
  padding: 0 12px;
}
.stat-card {
  // width: 200px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.stat-icon {
  font-size: 24px;
  margin-bottom: 10px;
  text-align: center;
  img {
    width: 24px;
    height: 24px;
  }
}
.stat-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
  text-align: center;
}
.stat-label {
  font-size: 14px;
  text-align: center;
}
.all-card {
  // border: 1px solid #67c23a;
  color: #FFBF00;
}
.success-card {
  // border: 1px solid #67c23a;
  color: #3586FF;
}
.fail-card {
  // border: 1px solid #f56c6c;
  color: #f56c6c;
}
.fail-records-title {
  font-size: 16px;
  // font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  padding: 0 20px;
}
.tabs-container {
  background-color: #F8F9FB;
  padding: 10px;
  border-radius: 4px;
  padding: 0 20px;
}
.fail-data-item {
  margin-bottom: 10px;
  // padding: 10px;
  background-color: #f9fafc;
  border-radius: 4px;
}
.data-id {
  // font-weight: bold;
  font-size: 14px;
  margin-bottom: 5px;
}
.data-title {
  font-size: 14px;
  color: #666;
}
// 运行日志el-drawer 的样式  end
// zhangzuying add css end
