<template>
  <!-- 态势呈现界面 -->
  <div class="app-container">
    <!-- 头部 -->
    <div class="ac_header">
      <div class="ach_left">
        <p class="title">分析任务（条）</p>
        <p class="nums">{{ total }}</p>
      </div>
      <div class="ach_right" @click="handleAdd">
        <div class="public_button position_add_btn search_btn">
          <i class="mr10 el-icon-circle-plus-outline"></i>新建分析任务
        </div>
      </div>
      <div style="clear: both"></div>
    </div>

    <!-- 筛选框 -->
    <el-form
      :inline="true"
      size="small"
      class="search_form"
      style="margin: 15px 0;"
    >
      <el-form-item label="任务名称">
        <el-input
          v-model="searchTaskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 260px"
        />
      </el-form-item>
      <el-form-item>
        <div
          @click="handleSearch"
          class="public_button search_btn fl position_btn"
        >
          <i class="mr10 el-icon-search"></i>搜索
        </div>
        <div
          @click="handleResetSearch"
          class="public_button clear_button fl position_btn"
        >
          <i class="mr10 el-icon-refresh"></i>重置
        </div>
      </el-form-item>
    </el-form>

    <!-- 列表页 -->
    <el-table v-loading="loading" :data="filteredTaskList">
      <el-table-column label="任务名称" prop="taskName" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建人"
        prop="createByName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="120"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetails(scope.row)"
            >分析详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新建分析任务对话框 -->
    <el-dialog
      title="新建分析任务"
      :visible.sync="open"
      width="800px"
      append-to-body
      :show-close="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="dialog_form_style"
      >
        <el-form-item label="模式选择" prop="mode">
          <el-select
            v-model="form.mode"
            placeholder="请选择模式"
            style="width: 100%"
            @change="handleModeChange"
          >
            <el-option label="主题分析" value="theme" />
            <el-option label="专题分析" value="special" />
          </el-select>
        </el-form-item>

        <!-- 主题分析模式 -->
        <el-form-item
          v-if="form.mode === 'theme'"
          label="主题选择"
          prop="themes"
        >
          <el-select
            v-model="form.themes"
            multiple
            placeholder="请选择主题（可多选）"
            style="width: 100%"
          >
            <el-option
              v-for="theme in themeOptions"
              :key="theme.value"
              :label="theme.label"
              :value="theme.value"
            />
          </el-select>
        </el-form-item>

        <!-- 专题分析模式 -->
        <template v-if="form.mode === 'special'">
          <el-form-item label="主题选择" prop="parentTheme">
            <el-select
              v-model="form.parentTheme"
              placeholder="请选择主题"
              style="width: 100%"
              @change="handleParentThemeChange"
            >
              <el-option
                v-for="theme in themeOptions"
                :key="theme.value"
                :label="theme.label"
                :value="theme.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-if="form.parentTheme"
            label="专题选择"
            prop="specials"
          >
            <el-select
              v-model="form.specials"
              multiple
              placeholder="请选择专题（可多选）"
              style="width: 100%"
            >
              <el-option
                v-for="special in getSpecialOptionsByTheme(form.parentTheme)"
                :key="special.value"
                :label="special.label"
                :value="special.value"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <div
          class="public_button clear_button fr position_btn"
          @click="cancel"
        >
          <i class="mr10 el-icon-close"></i>取 消
        </div>
        <div
          @click="submitForm"
          class="public_button search_btn fr position_btn"
        >
          <i class="mr10 el-icon-finished"></i>确 定
        </div>
        <div style="clear: both"></div>
      </div>
    </el-dialog>

    <!-- 分析详情对话框 -->
    <el-dialog
      :title="detailTitle"
      :visible.sync="openDetailDialog"
      width="1000px"
      append-to-body
      :show-close="false"
    >
      <div class="detail-content">
        <LineChartVue1
          v-if="currentChartData"
          :chart-data="currentChartData"
          :tooltip-mode="currentTooltipMode"
          height="420px"
        />
      </div>

      <div slot="footer" class="dialog-footer">
        <div
          class="public_button clear_button fr position_btn"
          @click="detailCancel"
        >
          <i class="mr10 el-icon-close"></i>关 闭
        </div>
        <div style="clear: both"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import LineChartVue1 from "./LineChartVue1.vue";

export default {
  name: "SituationPresentation",
  components: { LineChartVue1 },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 总条数
      total: 3,
      // 任务列表数据
      taskList: [],
      // 任务名称搜索
      searchTaskName: "",
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openDetailDialog: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      // 表单参数
      form: {
        mode: undefined,
        themes: [],
        parentTheme: undefined,
        specials: [],
      },
      // 当前查看的任务详情
      currentTask: {},
      // 当前图表配置
      currentChartData: null,
      // 当前 tooltip 展示模式
      currentTooltipMode: "struggle-infra",
      // 详情标题
      detailTitle: "分析详情",
      // 主题选项
      themeOptions: [
        { label: "边境斗争", value: "border" },
        { label: "军事行动", value: "military" },
        { label: "外交关系", value: "diplomatic" },
        { label: "经济合作", value: "economic" },
        { label: "文化交流", value: "cultural" },
      ],
      // 专题选项（按主题分组）
      specialOptions: {
        border: [
          { label: "东段建设", value: "east" },
          { label: "西段建设", value: "west" },
          { label: "中段建设", value: "middle" },
          { label: "边境巡逻", value: "patrol" },
        ],
        military: [
          { label: "联合军演", value: "exercise" },
          { label: "装备部署", value: "deployment" },
          { label: "战略规划", value: "strategy" },
        ],
        diplomatic: [
          { label: "高层会晤", value: "summit" },
          { label: "协议签署", value: "agreement" },
          { label: "争端解决", value: "dispute" },
        ],
        economic: [
          { label: "贸易往来", value: "trade" },
          { label: "投资合作", value: "investment" },
          { label: "基础设施建设", value: "infrastructure" },
        ],
        cultural: [
          { label: "教育交流", value: "education" },
          { label: "艺术展览", value: "art" },
          { label: "体育赛事", value: "sports" },
        ],
      },
      // 表单校验
      rules: {
        mode: [
          { required: true, message: "请选择模式", trigger: "change" },
        ],
        themes: [
          {
            required: true,
            message: "请选择主题",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (this.form.mode === "theme" && (!value || value.length === 0)) {
                callback(new Error("请选择主题"));
              } else {
                callback();
              }
            },
          },
        ],
        parentTheme: [
          {
            required: true,
            message: "请选择主题",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (this.form.mode === "special" && !value) {
                callback(new Error("请选择主题"));
              } else {
                callback();
              }
            },
          },
        ],
        specials: [
          {
            required: true,
            message: "请选择专题",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (
                this.form.mode === "special" &&
                (!value || value.length === 0)
              ) {
                callback(new Error("请选择专题"));
              } else {
                callback();
              }
            },
          },
        ],
      },
    };
  },
  computed: {
    // 根据任务名称过滤后的列表
    filteredTaskList() {
      const keyword = (this.searchTaskName || "").trim();
      if (!keyword) return this.taskList;
      return this.taskList.filter((item) =>
        item.taskName && item.taskName.includes(keyword)
      );
    },
  },
  created() {
    this.initData();
  },
  methods: {
    // 初始化数据
    initData() {
      // 生成随机创建人和创建时间
      const creators = ["张三", "李四", "王五", "赵六", "钱七"];
      const now = new Date();
      
      this.taskList = [
        {
          id: 1,
          taskName: "斗争烈度与基建关联分析",
          createTime: this.generateRandomDate(now, -30, -1),
          createByName: creators[Math.floor(Math.random() * creators.length)],
          taskType: "infrastructure",
        },
        {
          id: 2,
          taskName: "斗争烈度与兵力关联分析",
          createTime: this.generateRandomDate(now, -20, -1),
          createByName: creators[Math.floor(Math.random() * creators.length)],
          taskType: "troops",
        },
        {
          id: 3,
          taskName: "斗争烈度与舆论关联分析",
          createTime: this.generateRandomDate(now, -10, -1),
          createByName: creators[Math.floor(Math.random() * creators.length)],
          taskType: "public_opinion",
        },
      ];
      this.total = this.taskList.length;
    },
    // 生成随机日期
    generateRandomDate(baseDate, daysAgoMin, daysAgoMax) {
      const daysAgo =
        daysAgoMin +
        Math.floor(Math.random() * (daysAgoMax - daysAgoMin + 1));
      const date = new Date(baseDate);
      date.setDate(date.getDate() + daysAgo);
      // 设置随机时间
      date.setHours(Math.floor(Math.random() * 24));
      date.setMinutes(Math.floor(Math.random() * 60));
      date.setSeconds(Math.floor(Math.random() * 60));
      return date;
    },
    // 获取列表（预留：当前为前端静态数据）
    getList() {
      // 这里可以调用API获取数据
      // 目前使用模拟数据
    },
    // 搜索
    handleSearch() {
      // 由于使用 computed 过滤，这里不需要额外逻辑，占位方便后续接入接口
    },
    // 重置搜索
    handleResetSearch() {
      this.searchTaskName = "";
    },
    // 模式改变
    handleModeChange() {
      // 清空相关字段
      this.form.themes = [];
      this.form.parentTheme = undefined;
      this.form.specials = [];
    },
    // 父主题改变
    handleParentThemeChange() {
      // 清空专题选择
      this.form.specials = [];
    },
    // 根据主题获取专题选项
    getSpecialOptionsByTheme(themeValue) {
      return this.specialOptions[themeValue] || [];
    },
    // 新增按钮操作
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新建分析任务";
    },
    // 查看详情
    handleViewDetails(row) {
      this.currentTask = row;
      this.openDetailDialog = true;
      this.detailTitle = row.taskName + " - 分析详情";

      // 根据任务类型设置图表与 tooltip 内容
      if (row.taskType === "infrastructure") {
        this.currentChartData = this.buildInfrastructureChartData();
        this.currentTooltipMode = "struggle-infra";
      } else if (row.taskType === "troops") {
        this.currentChartData = this.buildTroopsChartData();
        this.currentTooltipMode = "struggle-troops";
      } else if (row.taskType === "public_opinion") {
        this.currentChartData = this.buildOpinionChartData();
        this.currentTooltipMode = "struggle-opinion";
      }
    },
    // 斗争烈度 + 基建程度 图表数据
    buildInfrastructureChartData() {
      return {
        xAxisData: ["2020", "2021", "2022", "2023", "2024", "2025"],
        yAxisName: ["综合指数"],
        showArea: true,
        showShadow: true,
        showSymbol: true,
        symbolSize: 8,
        smooth: true,
        seriesData: [
          {
            name: "斗争烈度",
            color: "64,158,255",
            width: 3,
            data: [58, 72, 68, 80, 76, 70],
          },
          {
            name: "基建程度",
            color: "103,194,58",
            width: 3,
            data: [35, 48, 62, 75, 86, 92],
          },
        ],
      };
    },
    // 斗争烈度 + 兵力支撑 图表数据
    buildTroopsChartData() {
      return {
        xAxisData: ["2020", "2021", "2022", "2023", "2024", "2025"],
        yAxisName: ["兵力指数"],
        showArea: true,
        showShadow: true,
        showSymbol: true,
        symbolSize: 8,
        smooth: true,
        seriesData: [
          {
            name: "斗争烈度",
            color: "64,158,255",
            width: 3,
            data: [52, 60, 71, 68, 74, 82],
          },
          {
            name: "兵力投入强度",
            color: "255,193,7",
            width: 3,
            data: [45, 54, 66, 72, 81, 90],
          },
        ],
      };
    },
    // 斗争烈度 + 舆论热度 图表数据
    buildOpinionChartData() {
      return {
        xAxisData: ["2020", "2021", "2022", "2023", "2024", "2025"],
        yAxisName: ["舆论指数"],
        showArea: true,
        showShadow: true,
        showSymbol: true,
        symbolSize: 8,
        smooth: true,
        seriesData: [
          {
            name: "斗争烈度",
            color: "64,158,255",
            width: 3,
            data: [40, 52, 60, 58, 66, 73],
          },
          {
            name: "舆论热度",
            color: "245,108,108",
            width: 3,
            data: [28, 46, 59, 68, 79, 88],
          },
        ],
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 详情取消
    detailCancel() {
      this.openDetailDialog = false;
      this.currentChartData = null;
    },
    // 表单重置
    reset() {
      this.form = {
        mode: undefined,
        themes: [],
        parentTheme: undefined,
        specials: [],
      };
      this.resetForm("form");
    },
    // 提交表单
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 这里可以调用API保存数据
          this.$modal.msgSuccess("任务创建成功");
          this.open = false;
          this.reset();
          // 可以刷新列表
          // this.getList();
        }
      });
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.ac_header {
  width: 98%;
  margin: auto;
  height: 110px;
  background-image: url("../../assets/images/lsdrsj_bg.png");
  background-size: contain;
  .ach_left {
    float: left;
    height: 100%;
    .title {
      font-family: SourceHanSansSC-Bold;
      font-size: 16px;
      color: #ffffff;
      letter-spacing: 0;
      padding-left: 30px;
    }
    .nums {
      font-family: LetsgoDigital-Regular;
      font-size: 36px;
      color: #ffffff;
      letter-spacing: 0;
      text-shadow: 0 2px 5px rgba(2, 0, 70, 0.5);
      font-weight: 700;
      margin-top: 10px;
      padding-left: 30px;
      background-image: linear-gradient(to bottom, #ffffff, #ccd8f2, #3869cc);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }
  .ach_right {
    float: right;
  }
}

.position_btn {
  margin-right: 10px;
}
.position_add_btn {
  margin-right: 30px;
  margin-top: 32px;
}

.detail-content {
  padding: 20px 0;
}

.troops-detail,
.opinion-detail {
  .el-descriptions {
    margin-bottom: 20px;
  }
}

.opinion-images {
  display: flex;
  gap: 20px;
  margin-top: 20px;
  .image-placeholder {
    flex: 1;
    height: 200px;
    border: 1px dashed #dcdfe6;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #909399;
    i {
      font-size: 48px;
      margin-bottom: 10px;
    }
    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.dialog_form_style {
    height: auto !important;
}
</style>
