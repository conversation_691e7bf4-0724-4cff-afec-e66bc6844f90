<template>
  <div class="app-container">
    <!-- 主布局：左右分栏 -->
    <el-row :gutter="20" class="main-row">
      <!-- 左侧专题列表 -->
      <el-col :span="6" class="left-panel">
        <el-card shadow="always" class="left-card">
          <div class="select-box">
            <el-select v-model="currentTopicId" placeholder="请选择主题" @change="handleTopicChange">
              <el-option v-for="topic in topicOptions" :key="topic.id" :label="topic.categoryName" :value="topic.id" />
            </el-select>
          </div>
        </el-card>

        <el-card shadow="always" class="left-card">
          <div slot="header" class="card-header">
            <span>专题列表</span>
          </div>
          <el-input v-model="searchTopic" placeholder="搜索专题名称" clearable size="small" prefix-icon="el-icon-search"
            class="search-input" @keyup.enter="fetchThematicList" @input="handleSearchInput" />
          <div class="topic-list-wrapper">
            <div v-for="topic in thematicList" :key="topic.id" class="topic-item"
              :class="{ 'topic-item-active': currentThematicId === topic.id }" @click="handleThematicSelect(topic)">
              <div class="topic-info">
                <div class="topic-name">{{ topic.categoryName }}</div>
                <div class="topic-count">{{ topic.count || 0 }} 条数据</div>
              </div>
              <button class="topic-setting" @click.stop="handleThematicSetting(topic)">
                <i class="el-icon-setting"></i>
              </button>
            </div>
            <div class="topic-empty" v-if="thematicList.length === 0">
              暂无专题数据
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧内容区 -->
      <el-col :span="18" class="right-panel">
        <el-card shadow="always" class="right-card">
          <!-- 专题标题与说明 -->
          <div class="topic-header">
            <h3 class="topic-title">{{ currentThematicName || '' }}</h3>
            <p class="topic-desc">
              {{ isSettingVisible 
                ? '设置自动汇聚规则后，系统将自动匹配符合条件的数据并添加到专题中，新数据会定期更新。' 
                : '根据数据汇聚规则，形成面向不同专题的数据资产' 
              }}
            </p>
          </div>

          <!-- 状态1：规则配置页 -->
          <div v-if="isSettingVisible">
            <el-tabs v-model="activeTab" type="card" class="content-tabs" @tab-click="handleRuleTabChange">
              <!-- 5个Tab页共用模板 -->
              <el-tab-pane :label="`开源资讯 (${typeCount.openSource || 0})`" name="openSource"></el-tab-pane>
              <el-tab-pane :label="`事件信息 (${typeCount.event || 0})`" name="event"></el-tab-pane>
              <el-tab-pane :label="`舆情日报 (${typeCount.daily || 0})`" name="daily"></el-tab-pane>
              <el-tab-pane :label="`舆情专报 (${typeCount.special || 0})`" name="special"></el-tab-pane>
              <el-tab-pane :label="`理论研究成果 (${typeCount.research || 0})`" name="theory"></el-tab-pane>

              <!-- 共用的规则配置模板 -->
              <div class="rule-config">
                <div class="config-section">
                  <h4>内容条件 <i class="el-icon-info"></i></h4>
                  <div class="condition-group">
                    <div v-for="(condition, index) in ruleForm.ruleConfigs" :key="index" class="condition-row">
                      <el-select v-model="condition.fieldName" placeholder="字段" class="field-select"
                        @change="handleFieldChange(condition, index)">
                        <el-option v-for="item in fieldOptions[activeTab]" :key="item.fieldName"
                          :label="item.displayName" :value="item.fieldName"
                          :data-field-type="item.fieldType"></el-option>
                      </el-select>
                      <el-select v-model="condition.operator" placeholder="操作符" class="operator-select">
                        <el-option v-for="item in operatorOptions" :key="item.value" :label="item.label"
                          :value="item.value"></el-option>
                      </el-select>
                      <el-input v-model="condition.valueList" placeholder="输入关键词，多个关键词用回车分隔" class="value-input"
                        type="textarea" @change="handleKeywordChange(condition)"></el-input>
                      <el-button type="primary" icon="el-icon-plus" size="mini" @click="addCondition"
                        v-if="index === ruleForm.ruleConfigs.length - 1"></el-button>
                      <el-button type="danger" icon="el-icon-minus" size="mini" @click="removeCondition(index)"
                        v-if="ruleForm.ruleConfigs.length > 1"></el-button>
                    </div>
                  </div>
                </div>

                <div class="config-section">
                  <h4>执行频率</h4>
                  <el-select v-model="ruleForm.ruleSchedule.frequency" placeholder="执行频率" class="frequency-select"
                    @change="handleFrequencyTypeChange">
                    <el-option v-for="item in frequencyTypeOptions" :key="item.dictValue" :label="item.dictLabel"
                      :value="item.dictValue"></el-option>
                  </el-select>
                  <el-select v-model="ruleForm.ruleSchedule.dayOfWeek" placeholder="周几" class="weekday-select"
                    v-if="ruleForm.ruleSchedule.frequency === '2'" :disabled="ruleForm.ruleSchedule.frequency !== '2'">
                    <el-option v-for="item in weekdayOptions" :key="item.dictValue" :label="item.dictLabel"
                      :value="item.dictValue"></el-option>
                  </el-select>
                  <el-select v-model="ruleForm.ruleSchedule.executeHour" placeholder="时间" class="time-select">
                    <el-option v-for="item in executeHourOptions" :key="item.dictValue" :label="item.dictLabel"
                      :value="item.dictValue"></el-option>
                  </el-select>
                </div>

                <div class="config-actions">
                  <el-button @click="resetCurrentRule">重置规则</el-button>
                  <el-button type="primary" @click="testCurrentRule">测试规则</el-button>
                </div>
              </div>
            </el-tabs>

            <!-- 底部取消和保存按钮 -->
            <div class="rule-footer">
              <el-button @click="cancelRules">取消</el-button>
              <el-button type="primary" @click="saveOrUpdateRules">保存/修改</el-button>
            </div>
          </div>

          <!-- 状态2：列表页 -->
          <div v-else>
            <el-tabs v-model="activeTab" type="card" @tab-click="handleTabChange" class="content-tabs">
              <!-- 1. 开源资讯 -->
              <el-tab-pane :label="`开源资讯 (${typeCount.openSource || 0})`" name="openSource">
                <div class="tab-content">
                  <el-table v-loading="loading.openSource" :data="tableData.openSource || []"
                    @selection-change="handleSelectChange('openSource')" border stripe>
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" type="index" width="60" />
                    <el-table-column prop="eventName" label="事件名称" min-width="200" />
                    <el-table-column prop="createByName" label="填报人" width="120" />
                  </el-table>
                  <el-pagination v-if="total.openSource > 0" :current-page="pageInfo.openSource.pageNum"
                    :page-size="pageInfo.openSource.pageSize" :total="total.openSource"
                    layout="total, prev, pager, next" @current-change="handlePageChange($event, 'openSource')"
                    class="pagination" />
                </div>
              </el-tab-pane>

              <!-- 2. 事件信息 -->
              <el-tab-pane :label="`事件信息 (${typeCount.event || 0})`" name="event">
                <div class="tab-content">
                  <el-table v-loading="loading.event" :data="tableData.event || []"
                    @selection-change="handleSelectChange('event')" border stripe>
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" type="index" width="60" />
                    <el-table-column prop="eventName" label="事件名称" min-width="200" />
                    <el-table-column prop="createByName" label="填报人" width="120" />
                  </el-table>
                  <el-pagination v-if="total.event > 0" :current-page="pageInfo.event.pageNum"
                    :page-size="pageInfo.event.pageSize" :total="total.event" layout="total, prev, pager, next"
                    @current-change="handlePageChange($event, 'event')" class="pagination" />
                </div>
              </el-tab-pane>

              <!-- 3. 舆情日报 -->
              <el-tab-pane :label="`舆情日报 (${typeCount.daily || 0})`" name="daily">
                <div class="tab-content">
                  <el-table v-loading="loading.daily" :data="tableData.daily || []"
                    @selection-change="handleSelectChange('daily')" border stripe>
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" type="index" width="60" />
                    <el-table-column prop="eventName" label="事件名称" min-width="200" />
                    <el-table-column prop="createByName" label="填报人" width="120" />
                  </el-table>
                  <el-pagination v-if="total.daily > 0" :current-page="pageInfo.daily.pageNum"
                    :page-size="pageInfo.daily.pageSize" :total="total.daily" layout="total, prev, pager, next"
                    @current-change="handlePageChange($event, 'daily')" class="pagination" />
                </div>
              </el-tab-pane>

              <!-- 4. 舆情专报 -->
              <el-tab-pane :label="`舆情专报 (${typeCount.special || 0})`" name="special">
                <div class="tab-content">
                  <el-table v-loading="loading.special" :data="tableData.special || []"
                    @selection-change="handleSelectChange('special')" border stripe>
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" type="index" width="60" />
                    <el-table-column prop="eventName" label="事件名称" min-width="200" />
                    <el-table-column prop="createByName" label="填报人" width="120" />
                  </el-table>
                  <el-pagination v-if="total.special > 0" :current-page="pageInfo.special.pageNum"
                    :page-size="pageInfo.special.pageSize" :total="total.special" layout="total, prev, pager, next"
                    @current-change="handlePageChange($event, 'special')" class="pagination" />
                </div>
              </el-tab-pane>

              <!-- 5. 理论研究成果 -->
              <el-tab-pane :label="`理论研究成果 (${typeCount.research || 0})`" name="theory">
                <div class="tab-content">
                  <el-table v-loading="loading.theory" :data="tableData.theory || []"
                    @selection-change="handleSelectChange('theory')" border stripe>
                    <el-table-column type="selection" width="55" />
                    <el-table-column label="序号" type="index" width="60" />
                    <el-table-column prop="eventName" label="事件名称" min-width="200" />
                    <el-table-column prop="createByName" label="填报人" width="120" />
                  </el-table>
                  <el-pagination v-if="total.theory > 0" :current-page="pageInfo.theory.pageNum"
                    :page-size="pageInfo.theory.pageSize" :total="total.theory" layout="total, prev, pager, next"
                    @current-change="handlePageChange($event, 'theory')" class="pagination" />
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 右侧数据预览抽屉 -->
    <el-drawer title="数据预览" :visible.sync="drawer" direction="rtl" size="25%" custom-class="data-preview-drawer"
      :close-on-click-modal="false">
      <!-- 匹配数量提示 -->
      <div class="preview-match-tip">根据当前规则，匹配到 {{ previewTotal || 0 }} 条数据</div>

      <!-- 预览数据列表 -->
      <div class="preview-data-list">
        <div class="preview-data-item" v-for="(item, index) in previewData" :key="index">
          <div class="item-title">{{ index + 1 }}.{{ item.name }}</div>
          <div class="item-content">{{ item.content }}</div>
          <div class="item-meta">
            <span class="meta-source">来源：{{ item.source }}</span>
            <span class="meta-time">发布时间：{{ item.createTime }}</span>
          </div>
          <div class="item-tags">
            <el-tag :type="item.sentiment === '负面' ? 'danger' : 'info'" size="mini">{{ item.sentiment }}</el-tag>
            <el-tag type="primary" size="mini">{{ item.matcherLabel }}</el-tag>
          </div>
        </div>
      </div>

      <div class="preview-footer">
        <el-button @click="drawer = false">关闭</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  selectListBySystemType, // 获取所有主题
  selectThematicListById, // 根据主题查找专题
  fields, // 专题数据汇聚配置字段下拉框
  operators, // 专题数据汇聚配置操作符下拉框
  data_manage_frequency, // 获取执行频率（每天/每周）
  data_manage_dayofweek, // 获取执行频率（星期几）
  data_manage_executehour, // 获取执行频率（0-24时）
  selectDataByThenaticId, // 列表查询接口
  selectThematicCountByRuleType, //根据专题获取个数
  saveRules, // 保存规则
  getRuleByThematic, // 返显已配置的规则
  updateRules, // 修改规则
  execute, // 执行规则弹窗查询
} from "@/api/data/zcbuild";

// 格式化日期工具函数
function formatDate(time, format) {
  if (!time) return '';
  const date = new Date(time);
  const o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'H+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    'S': date.getMilliseconds()
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (const k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
    }
  }
  return format;
}

export default {
  filters: {
    formatDate(time) {
      return formatDate(time, 'yyyy-MM-dd HH:mm')
    }
  },
  data() {
    return {
      // 左侧主题和专题列表
      topicOptions: [],
      thematicList: [],
      searchTopic: '',
      currentTopicId: null,
      currentThematicId: null,
      currentThematicName: '', // 新增：存储当前专题名称
      typeCount: {
        openSource: 0,
        event: 0,
        daily: 0,
        special: 0,
        research: 0
      },

      // 规则配置页状态
      isSettingVisible: false,
      activeTab: 'openSource',

      // 右侧预览抽屉
      drawer: false,
      previewData: [],
      previewTotal: 0,

      // 规则数据（严格匹配接口层级：ruleSchedule内包含thematicId和ruleType）
      ruleForm: {
        ruleConfigs: [
          {
            fieldName: '',
            operator: '',
            valueList: '',
            id: '' // 用于修改接口的规则ID
          }
        ],
        ruleSchedule: {
          frequency: '',
          dayOfWeek: '',
          executeHour: '',
          thematicId: '',
          ruleType: 1,
        }
      },

      // 下拉框选项
      fieldOptions: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      operatorOptions: [],
      frequencyTypeOptions: [], // 执行频率选项（每天/每周）
      weekdayOptions: [], // 周几选项
      executeHourOptions: [], // 执行时间选项

      // 右侧列表数据
      tableData: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
      loading: {
        openSource: false,
        event: false,
        daily: false,
        special: false,
        theory: false
      },
      total: {
        openSource: 0,
        event: 0,
        daily: 0,
        special: 0,
        theory: 0
      },
      pageInfo: {
        openSource: { pageNum: 1, pageSize: 10 },
        event: { pageNum: 1, pageSize: 10 },
        daily: { pageNum: 1, pageSize: 10 },
        special: { pageNum: 1, pageSize: 10 },
        theory: { pageNum: 1, pageSize: 10 }
      },
      selectedIds: {
        openSource: [],
        event: [],
        daily: [],
        special: [],
        theory: []
      },
    };
  },
  computed: {
    hasSelected() {
      return this.selectedIds[this.activeTab].length > 0
    }
  },
  created() {
    // 初始化时加载基础数据
    this.fetchFrequencyTypeOptions();
    this.fetchExecuteHourOptions();
    this.fetchTopicOptions();
    this.fetchFieldOptions();
  },
  methods: {
    // 获取主题下拉选项
    fetchTopicOptions() {
      const params = { systemType: 1 };
      selectListBySystemType(params)
        .then(response => {
          if (response?.code === 200) {
            this.topicOptions = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名主题'
            }));
            if (this.topicOptions.length > 0) {
              this.currentTopicId = this.topicOptions[0].id;
              this.fetchThematicList();
            }
          } else {
            this.topicOptions = [];
          }
        })
        .catch(error => {
          console.error('获取主题列表失败:', error);
          this.$message.error('获取主题列表失败，请稍后重试');
          this.topicOptions = [];
        });
    },

    // 输入框搜索专题
    handleSearchInput() {
      this.fetchThematicList();
    },

    // 获取专题列表
    fetchThematicList() {
      const params = {
        id: this.currentTopicId,
        searchName: this.searchTopic,
        ruleType: ''
      };
      selectThematicListById(params)
        .then(response => {
          if (response?.code === 200) {
            this.thematicList = (response.data || []).map(item => ({
              id: item.id,
              categoryName: item.categoryName || '未命名专题',
              count: item.count || 0
            }));
            if (this.thematicList.length > 0) {
              this.currentThematicId = this.thematicList[0].id;
              this.currentThematicName = this.thematicList[0].categoryName; // 新增：同步专题名称
              this.ruleForm.ruleSchedule.thematicId = this.currentThematicId;
              this.loadData('openSource');
              this.fetchTypeCount();
            } else {
              this.currentThematicId = null;
              this.currentThematicName = ''; // 新增：清空专题名称
              this.ruleForm.ruleSchedule.thematicId = '';
              this.typeCount = { openSource: 0, event: 0, daily: 0, special: 0, research: 0 };
            }
          } else {
            this.thematicList = [];
            this.currentThematicId = null;
            this.currentThematicName = ''; // 新增：清空专题名称
            this.ruleForm.ruleSchedule.thematicId = '';
          }
        })
        .catch(error => {
          console.error('获取专题列表失败:', error);
          this.$message.error('获取专题列表失败，请稍后重试');
          this.thematicList = [];
          this.currentThematicId = null;
          this.currentThematicName = ''; // 新增：清空专题名称
          this.ruleForm.ruleSchedule.thematicId = '';
        });
    },

    // 获取各类型数据条数
    fetchTypeCount() {
      if (!this.currentThematicId) return;
      const params = { thematicId: this.currentThematicId };
      selectThematicCountByRuleType(params)
        .then(response => {
          if (response?.code === 200) {
            const data = response.data || {};
            this.typeCount = {
              openSource: data.openSource || 0,
              event: data.event || 0,
              daily: data.daily || 0,
              special: data.special || 0,
              research: data.research || 0
            };
          } else {
            this.typeCount = { openSource: 0, event: 0, daily: 0, special: 0, research: 0 };
          }
        })
        .catch(error => {
          this.typeCount = { openSource: 0, event: 0, daily: 0, special: 0, research: 0 };
        });
    },

    // 主题选择变化
    handleTopicChange() {
      this.fetchThematicList();
    },

    // 加载列表数据
    loadData(tabType) {
      const ruleTypeMap = {
        openSource: 1,
        event: 2,
        daily: 3,
        special: 4,
        theory: 5
      };
      const params = {
        thematicId: this.currentThematicId,
        ruleType: ruleTypeMap[tabType],
        pageNum: this.pageInfo[tabType].pageNum,
        pageSize: this.pageInfo[tabType].pageSize
      };
      this.loading[tabType] = true;
      selectDataByThenaticId(params)
        .then(response => {
          this.loading[tabType] = false;
          if (response?.code === 200) {
            this.tableData[tabType] = response.rows || [];
            this.total[tabType] = response.total || 0;
          } else {
            this.tableData[tabType] = [];
            this.total[tabType] = 0;
          }
        })
        .catch(error => {
          console.error(`加载${tabType}数据失败:`, error);
          this.$message.error(`加载${tabType}数据失败，请稍后重试`);
          this.tableData[tabType] = [];
          this.total[tabType] = 0;
          this.loading[tabType] = false;
        });
    },

    // 分页切换
    handlePageChange(page, tabType) {
      this.pageInfo[tabType].pageNum = page;
      this.loadData(tabType);
    },

    // 专题选择
    handleThematicSelect(data) {
      this.currentThematicId = data.id;
      this.currentThematicName = data.categoryName; // 新增：同步专题名称
      this.ruleForm.ruleSchedule.thematicId = data.id;
      this.isSettingVisible = false;
      this.loadData(this.activeTab);
      this.fetchTypeCount();
    },

    // 专题设置（进入规则配置页）
    handleThematicSetting(topic) {
      this.currentThematicId = topic.id;
      this.currentThematicName = topic.categoryName; 
      this.ruleForm.ruleSchedule.thematicId = topic.id;
      this.isSettingVisible = true;
      // 初始化规则类型（对应当前Tab）
      const ruleTypeMap = {
        openSource: 1,
        event: 2,
        daily: 3,
        special: 4,
        theory: 5
      };
      this.ruleForm.ruleSchedule.ruleType = ruleTypeMap[this.activeTab];
      // 调用接口查询已有规则
      this.fetchExistingRules();
    },

    // 查询已有规则
    fetchExistingRules() {
      const params = {
        thematicId: this.currentThematicId,
        ruleType: this.ruleForm.ruleSchedule.ruleType
      };
      getRuleByThematic(params)
        .then(response => {
          if (response?.code === 200) {
            const ruleData = response.data || {};
            // 处理规则配置
            if (ruleData.ruleConfig && ruleData.ruleConfig.length > 0) {
              this.ruleForm.ruleConfigs = ruleData.ruleConfig.map(item => ({
                fieldName: item.fieldName || '',
                operator: item.operator || '',
                valueList: item.valueList || '',
                id: item.id || ''
              }));
            } else {
              this.ruleForm.ruleConfigs = [{ fieldName: '', operator: '', valueList: '' }];
            }

            // 处理执行频率
            if (ruleData.ruleSchedule) {
              const schedule = ruleData.ruleSchedule;
              // 强制转换为字符串，确保与下拉框选项值类型一致
              this.ruleForm.ruleSchedule = {
                ...this.ruleForm.ruleSchedule,
                frequency: String(schedule.frequency || ''),  // 执行频率（每天/每周）
                dayOfWeek: String(schedule.dayOfWeek || ''),  // 周几（仅每周时有效）
                executeHour: String(schedule.executeHour || ''),  // 执行时间（小时）
                ruleId: schedule.ruleId || ''  // 规则ID（用于修改）
              };

              // 特殊处理：如果是每周，需要重新加载周几选项并回显
              if (this.ruleForm.ruleSchedule.frequency === '2') {
                this.handleFrequencyTypeChange(); // 触发周几选项加载
              }
            }
          } else {
            this.$message.error('查询规则失败：' + (response?.msg || '未知错误'));
            this.resetCurrentRule(); // 失败时重置表单
          }
        })
        .catch(error => {
          console.error('查询已有规则失败:', error);
          this.$message.error('查询规则失败，请稍后重试');
          this.resetCurrentRule(); // 失败时重置表单
        });
    },

    // 获取字段下拉框数据
    fetchFieldOptions() {
      const tabTypes = {
        openSource: 2,
        event: 1,
        daily: 1,
        special: 1,
        theory: 1
      };
      Object.keys(tabTypes).forEach(tab => {
        const type = tabTypes[tab];
        fields({ type })
          .then(response => {
            if (response?.code === 200) {
              this.fieldOptions[tab] = (response.data || []).map(item => ({
                fieldName: item.fieldName,
                displayName: item.displayName || '未命名字段',
                fieldType: item.fieldType
              }));
            } else {
              this.fieldOptions[tab] = [];
            }
          })
          .catch(error => {
            console.error(`获取${tab}字段列表失败:`, error);
            this.$message.error(`获取${tab}字段列表失败，请稍后重试`);
            this.fieldOptions[tab] = [];
          });
      });
    },

    // 获取执行频率类型下拉框数据（每天/每周）
    fetchFrequencyTypeOptions() {
      data_manage_frequency()
        .then(response => {
          if (response?.code === 200) {
            this.frequencyTypeOptions = response.data || [];
          } else {
            this.frequencyTypeOptions = [];
          }
        })
        .catch(error => {
          console.error('获取执行频率类型列表失败:', error);
          this.$message.error('获取执行频率类型列表失败，请稍后重试');
          this.frequencyTypeOptions = [];
        });
    },

    // 获取执行时间下拉框数据（0-24时）
    fetchExecuteHourOptions() {
      data_manage_executehour()
        .then(response => {
          if (response?.code === 200) {
            this.executeHourOptions = (response.data || []).map(item => ({
              dictLabel: item.dictLabel || '未设置',
              dictValue: item.dictValue
            }));
          } else {
            this.executeHourOptions = [];
          }
        })
        .catch(error => {
          console.error('获取执行时间列表失败:', error);
          this.$message.error('获取执行时间列表失败，请稍后重试');
          this.executeHourOptions = [];
        });
    },

    // 字段选择变化
    handleFieldChange(condition, index) {
      const fieldItem = this.fieldOptions[this.activeTab]?.find(item => item.fieldName === condition.fieldName);
      if (fieldItem) {
        this.fetchOperatorOptions(fieldItem.fieldType);
      } else {
        this.operatorOptions = [];
      }
    },

    // 获取操作符下拉框数据
    fetchOperatorOptions(fieldType) {
      if (!fieldType) {
        this.operatorOptions = [];
        return;
      }
      const params = { fieldType };
      operators(params)
        .then(response => {
          if (response?.code === 200) {
            this.operatorOptions = (response.data || []).map(item => ({
              label: item.label || '未命名操作符',
              value: item.value
            }));
          } else {
            this.operatorOptions = [];
          }
        })
        .catch(error => {
          console.error('获取操作符列表失败:', error);
          this.$message.error('获取操作符列表失败，请稍后重试');
          this.operatorOptions = [];
        });
    },

    // 执行频率类型变化（加载周几选项）
    handleFrequencyTypeChange() {
      if (this.ruleForm.ruleSchedule.frequency === '2') {
        data_manage_dayofweek()
          .then(response => {
            if (response?.code === 200) {
              this.weekdayOptions = response.data || [];
              // 周几选项加载完成后强制更新视图（解决回显延迟问题）
              this.$nextTick(() => {
                this.ruleForm.ruleSchedule.dayOfWeek = this.ruleForm.ruleSchedule.dayOfWeek;
              });
            } else {
              this.weekdayOptions = [];
            }
          })
          .catch(error => {
            console.error('获取周几列表失败:', error);
            this.$message.error('获取周几列表失败，请稍后重试');
            this.weekdayOptions = [];
          });
      } else {
        this.weekdayOptions = [];
        this.ruleForm.ruleSchedule.dayOfWeek = '';
      }
    },

    // 规则Tab切换
    handleRuleTabChange(tab) {
      this.activeTab = tab.name;
      // 更新规则类型到ruleSchedule内
      const ruleTypeMap = {
        openSource: 1,
        event: 2,
        daily: 3,
        special: 4,
        theory: 5
      };
      this.ruleForm.ruleSchedule.ruleType = ruleTypeMap[this.activeTab];
      // 切换Tab时重新查询该类型的规则
      this.fetchExistingRules();
    },

    // 新增条件行
    addCondition() {
      this.ruleForm.ruleConfigs.push({
        fieldName: '',
        operator: '',
        valueList: '',
      });
    },

    // 删除条件行
    removeCondition(index) {
      if (this.ruleForm.ruleConfigs.length <= 1) return;
      this.ruleForm.ruleConfigs.splice(index, 1);
    },

    // 处理关键词输入（多行转逗号分隔）
    handleKeywordChange(condition) {
      condition.valueList = (condition.valueList || '')
        .split('\n')
        .map(keyword => keyword.trim())
        .filter(keyword => keyword)
        .join(',');
    },

    // 保存或修改规则（根据是否有ruleId判断调用哪个接口）
    saveOrUpdateRules() {
      // 验证必要参数
      if (!this.ruleForm.ruleSchedule.thematicId) {
        this.$message.error('请选择专题');
        return;
      }
      if (this.ruleForm.ruleConfigs.length === 0) {
        this.$message.error('请至少配置一条规则');
        return;
      }
      if (!this.ruleForm.ruleSchedule.frequency) {
        this.$message.error('请选择执行频率');
        return;
      }
      if (!this.ruleForm.ruleSchedule.executeHour) {
        this.$message.error('请选择执行时间');
        return;
      }

      // 处理关键词格式
      this.ruleForm.ruleConfigs.forEach(condition => {
        this.handleKeywordChange(condition);
      });

      // 区分保存和修改接口
      if (this.ruleForm.ruleSchedule.ruleId) {
        // 调用修改接口
        updateRules(this.ruleForm)
          .then(response => {
            if (response?.code === 200) {
              this.$message.success('规则修改成功');
              this.isSettingVisible = false;
              this.fetchTypeCount(); // 刷新数据计数
            } else {
              this.$message.error('规则修改失败：' + (response?.msg || '未知错误'));
            }
          })
          .catch(error => {
            console.error('修改规则失败:', error);
            this.$message.error('规则修改失败，请稍后重试');
          });
      } else {
        // 调用保存接口
        saveRules(this.ruleForm)
          .then(response => {
            if (response?.code === 200) {
              this.$message.success('规则保存成功');
              this.isSettingVisible = false;
              this.fetchTypeCount(); // 刷新数据计数
            } else {
              this.$message.error('规则保存失败：' + (response?.msg || '未知错误'));
            }
          })
          .catch(error => {
            console.error('保存规则失败:', error);
            this.$message.error('规则保存失败，请稍后重试');
          });
      }
    },

    // 重置当前规则
    resetCurrentRule() {
      this.ruleForm = {
        ruleConfigs: [{
          fieldName: '',
          operator: '',
          valueList: '',
        }],
        ruleSchedule: {
          frequency: '',
          dayOfWeek: '',
          executeHour: '',
          thematicId: this.currentThematicId || '',
          ruleType: this.ruleForm.ruleSchedule.ruleType,
          ruleId: ''
        }
      };
      this.operatorOptions = [];
      this.weekdayOptions = [];
    },

    // 测试当前规则
    testCurrentRule() {
      const testParams = {
        ruleConfigs: [...this.ruleForm.ruleConfigs],
        thematicId: this.ruleForm.ruleSchedule.thematicId,
        ruleType: this.ruleForm.ruleSchedule.ruleType,
        pageNum: 1,
        pageSize: 10
      };

      // 同样处理关键词格式
      testParams.ruleConfigs.forEach(condition => {
        condition.valueList = (condition.valueList || '')
          .split('\n')
          .map(keyword => keyword.trim())
          .filter(keyword => keyword)
          .join(',');
      });

      // 调用测试接口
      execute(testParams)
        .then(response => {
          if (response?.code === 200) {
            this.previewData = response.data.list || [];
            this.previewTotal = response.data.total || 0;
            this.drawer = true;
          } else {
            this.$message.error('规则测试失败：' + (response?.msg || '未知错误'));
          }
        })
        .catch(error => {
          console.error('规则测试失败:', error);
          this.$message.error('规则测试失败，请稍后重试');
        });
    },

    // 取消规则配置
    cancelRules() {
      this.isSettingVisible = false;
      this.resetCurrentRule();
    },

    // 列表页Tab切换
    handleTabChange(tab) {
      this.activeTab = tab.name;
      if (this.currentThematicId) {
        this.loadData(tab.name);
      }
    },

    // 选择项变化
    handleSelectChange(tabType, selection) {
      this.selectedIds[tabType] = (selection || []).map(item => item.id);
    },
    submitTopic() {
      this.$refs.topicForm.validate(valid => {
        if (valid) {
          this.thematicList.push({
            id: this.thematicList.length + 1,
            label: this.topicForm.name,
            count: 0,
            children: []
          });
          this.$message.success('新增成功');
          this.topicDialog.visible = false;
        }
      });
    },
    handleAddData() {
      this.dataDialog.title = `新增${this.getTabName()}`;
      this.dataForm = { topicId: this.currentThematicId };
      this.dataDialog.visible = true;
    },
    submitData() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.tableData[this.activeTab].unshift({
            ...this.dataForm,
            id: this.tableData[this.activeTab].length + 1
          });
          this.total[this.activeTab]++;
          this.dataDialog.visible = false;
          this.$message.success('新增成功');
        }
      });
    },
    getTabName() {
      const names = {
        openSource: '开源资讯',
        event: '事件信息',
        daily: '舆情日报',
        special: '舆情专报',
        theory: '理论研究成果'
      };
      return names[this.activeTab] || '数据';
    },

  }
}
</script>

<style scoped>
.main-row {
  height: 100%;
  min-height: calc(100vh - 60px);
  padding: 15px;
}

.left-panel,
.right-panel {
  height: 100%;
}

.left-card,
.right-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-input {
  margin-bottom: 15px;
}

.topic-list-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 8px 5px;
}

.topic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  margin-bottom: 6px;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.topic-item-active {
  border-color: #409eff;
  background-color: #f0f7ff;
}

.topic-item:hover {
  background-color: #f5f7fa;
}

.topic-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.topic-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.topic-count {
  font-size: 12px;
  color: #409eff;
}

.topic-setting {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  color: #909399;
  cursor: pointer;
  transition: color 0.2s;
  padding: 0;
}

.topic-setting:hover {
  color: #409eff;
}

.topic-empty {
  text-align: center;
  padding: 30px 0;
  color: #909399;
  font-size: 14px;
  background-color: #ffffff;
  border-radius: 4px;
  margin: 0 5px;
}

.content-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 5px 0;
}

.pagination {
  margin-top: 15px;
  text-align: right;
}

::v-deep .el-tabs__content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

::v-deep .el-tab-pane {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 规则配置页样式 */
.rule-config {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.config-section {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #eee;
}

.config-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #303133;
  display: flex;
  align-items: center;
}

.config-section h4 i {
  margin-left: 5px;
  font-size: 14px;
  color: #909399;
}

.condition-group {
  margin-bottom: 15px;
}

.condition-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.field-select,
.operator-select,
.frequency-select,
.weekday-select,
.time-select {
  width: 140px;
  margin-right: 10px;
  margin-bottom: 5px;
}

.value-input {
  flex: 1;
  min-width: 200px;
  margin-right: 10px;
  margin-bottom: 5px;
}

.rule-footer {
  padding: 15px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: center;
  gap: 10px;
}

/* 预览抽屉样式 */
::v-deep .data-preview-drawer {
  width: 25% !important;
}

::v-deep .data-preview-drawer .el-drawer__body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: calc(100% - 56px);
}

.preview-match-tip {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.preview-data-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.preview-data-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #508df6;
  line-height: 1.4;
}

.item-content {
  font-size: 13px;
  color: #666666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-meta {
  font-size: 12px;
  color: #999999;
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
}

.item-tags {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

.preview-footer {
  margin-top: 16px;
  text-align: right;
}

::v-deep .data-preview-drawer .el-drawer__header {
  padding: 14px 20px;
  border-bottom: 1px solid #eee;
}
</style>