<template>
  <div id="app">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";

export default {
  name: "App",
  components: { ThemePicker },
  metaInfo() {
    return {
      title:
        this.$store.state.settings.dynamicTitle &&
        this.$store.state.settings.title,
      titleTemplate: (title) => {
        return title
          ? `${title} - ${process.env.VUE_APP_TITLE}`
          : process.env.VUE_APP_TITLE;
      },
    };
  },
};
</script>
<style scoped>
#app {
  background-image: url('./assets/common/background-list.png');
  background-size: 100% 150px;
  background-repeat: no-repeat;
  /* background: #0d7154; */
}
#app .theme-picker {
  display: none;
}
@font-face {
  font-family: SourceHanSansSC-Bold;
  src: url("./assets/SourceHanSansSC-Bold-2.otf");
}
@font-face {
  font-family: SourceHanSansSC-Regular;
  src: url("./assets/SourceHanSansSC-Regular-2.otf");
}
@font-face {
  font-family: <PERSON><PERSON><PERSON>_ShuHeiTi_Bold;
  src: url("./assets/<PERSON><PERSON><PERSON>_ShuHeiTi_Bold.ttf");
}
</style>
