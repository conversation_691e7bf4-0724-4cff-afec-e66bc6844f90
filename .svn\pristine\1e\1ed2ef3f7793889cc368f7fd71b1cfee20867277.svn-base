<template>
  <!-- 资产构建页 -->
  <div class="app-container">
    <!-- 顶部Tab切换 -->
    <el-tabs v-model="activeTab" type="card"  @tab-click="handleTabChange">
      <el-tab-pane label="资产概览" name="overview"></el-tab-pane>
      <el-tab-pane label="资产检索" name="search"></el-tab-pane>
    </el-tabs>

    <!-- 资产概览页面 -->
    <div v-if="activeTab === 'overview'" class="overview-page">
      <div class="stats-card">
        <!-- <div class="stat-item">
          <div style="color: #ffffff;">数据总量</div>
          <div class="num">{{ dataTotal.total || '0' }}</div>
          <div class="trend">
            较上月 
            <i :class="dataTotal.direction === '上升' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i> 
            {{ dataTotal.percent ? Math.abs(dataTotal.percent).toFixed(2) + '%' : '0%' }}
          </div>
        </div> -->
        <div class="stat-item row-layout">
          <div class="text-col">
            <div style="color: #ffffff;margin-top: 10px;margin-bottom: 10px;">数据总量</div>
            <div class="num">{{ dataTotal.total || '0' }}</div>
            <div class="trend">
              较上月
              <img :src="dataTotal.direction === '上升'
                ? require('../imgs_erb/icon_zz.png')
                : require('../imgs_erb/icon_xj.png')" class="trend-icon">
              <span class="percent-text">
                {{ dataTotal.percent ? Math.abs(dataTotal.percent).toFixed(2) + '%' : '0%' }}
              </span>
            </div>
          </div>
          <div class="img-col">
            <img src="../imgs_erb/img_fz_01.png" alt="数据示意图" class="stat-img-item">
          </div>
        </div>
        <div class="stat-item">
          <div style="color: #ffffff;">今日新增</div>
          <div class="num">{{ todaySituation.todayCount || '0' }}</div>
          <div class="trend">
            较昨日
            <i :class="todaySituation.direction === '上升' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            {{ todaySituation.percent ? Math.abs(todaySituation.percent).toFixed(2) + '%' : '0%' }}
          </div>
        </div>
        <div class="stat-item">
          <div style="color: #ffffff;">专题数量</div>
          <div class="num">{{ topicCount.count || '0' }}</div>
          <div class="trend">
            较上月
            <i :class="topicCount.direction === '上升' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            {{ topicCount.percent ? Math.abs(topicCount.percent).toFixed(2) + '%' : '0%' }}
          </div>
        </div>
        <div class="stat-item">
          <div style="color: #ffffff;">开源引接数量</div>
          <div class="num">{{ openSourceCount.count || '0' }}</div>
          <div class="trend">
            较昨日
            <i :class="openSourceCount.direction === '上升' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            {{ openSourceCount.percent ? Math.abs(openSourceCount.percent).toFixed(2) + '%' : '0%' }}
          </div>
        </div>
      </div>


      <div class="chart-group1">
        <div class="topic-name-wrap">
          <img src="../imgs_erb/icon_zsq.png" class="topic-tag-img">
          <span class="topic-name">资源分类占比</span>
        </div>
      </div>
      <div class="chart-group">
        <div class="chart-item" id="category-chart" style="width: 100%; height: 250px;"></div>
        <div class="chart-item" id="source-chart" style="width: 100%; height: 250px;"></div>
        <div class="chart-item" id="topic-chart" style="width: 100%; height: 250px;"></div>
      </div>

      <div class="trend-chart" id="trend-chart" style="width: 100%; height: 300px;"></div>
    </div>

    <!-- 资产检索页面 -->
    <div v-else-if="activeTab === 'search'" class="search-page">
      <div class="main-content">
        <div class="search-panel">
          <h3 class="panel-title">资源检索</h3>
          <div class="search-form">
            <div class="search-row">
              <label class="form-label">必要关键词</label>
              <div class="keyword-input-container">
                <div v-for="(keyword, index) in mustContain" :key="index" class="keyword-tag">
                  {{ keyword }}
                  <span class="tag-remove" @click="removeKeyword('mustContain', index)">×</span>
                </div>
                <el-input v-model="currentMustContain" placeholder="请输入关键词，回车添加" class="keyword-input"
                  @keyup.enter.native="addKeyword('mustContain', currentMustContain)"
                  @blur="handleInputBlur('mustContain', currentMustContain)"></el-input>
              </div>

              <label class="form-label">任意包含</label>
              <div class="keyword-input-container">
                <div v-for="(keyword, index) in anyContain" :key="index" class="keyword-tag">
                  {{ keyword }}
                  <span class="tag-remove" @click="removeKeyword('anyContain', index)">×</span>
                </div>
                <el-input v-model="currentAnyContain" placeholder="请输入关键词，回车添加" class="keyword-input"
                  @keyup.enter.native="addKeyword('anyContain', currentAnyContain)"
                  @blur="handleInputBlur('anyContain', currentAnyContain)"></el-input>
              </div>

              <label class="form-label">需要排除</label>
              <div class="keyword-input-container">
                <div v-for="(keyword, index) in exclude" :key="index" class="keyword-tag">
                  {{ keyword }}
                  <span class="tag-remove" @click="removeKeyword('exclude', index)">×</span>
                </div>
                <el-input v-model="currentExclude" placeholder="请输入关键词，回车添加" class="keyword-input"
                  @keyup.enter.native="addKeyword('exclude', currentExclude)"
                  @blur="handleInputBlur('exclude', currentExclude)"></el-input>
              </div>
            </div>

            <div class="search-row">
              <label class="form-label">资源类别</label>
              <div class="btn-group no-border">
                <span v-for="item in resourceTypeOptions" :key="item.dictValue"
                  :class="{ 'active': selectedResourceType === item.dictValue, 'btn': true }"
                  @click="selectedResourceType = item.dictValue">{{ item.dictLabel }}</span>
              </div>
            </div>

            <div class="search-row">
              <label class="form-label">发生时间</label>
              <div class="btn-group no-border">
                <span v-for="item in timeRangeOptions" :key="item.value"
                  :class="{ 'active': selectedTimeRange === item.value, 'btn': true }"
                  @click="selectedTimeRange = item.value">{{ item.label }}</span>
                <el-date-picker v-if="selectedTimeRange === 'custom'" v-model="dateRange" type="daterange"
                  range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                  class="date-picker"></el-date-picker>
              </div>
            </div>

            <div v-if="['2'].includes(selectedResourceType)" class="search-row publisher-row">
              <label class="form-label">发布主体</label>
              <div class="btn-group no-border publisher-group">
                <span v-for="item in publisherSubjects" :key="item.parentName" :class="{ 
                    'btn': true, 
                    'has-dropdown': item.children && item.children.length > 0,
                    'active': selectedPublisherParent === item.parentName 
                  }" @click="handlePublisherParentClick(item)">
                  {{ getSelectedChild(item) || item.parentName }}
                  <i v-if="item.children && item.children.length > 0" class="el-icon-arrow-down"></i>
                  <div v-if="item.children && item.children.length > 0 && item.isOpen"
                    class="dropdown-menu el-select-dropdown custom-dropdown">
                    <div v-for="(child, index) in getWebsiteNames(item)" :key="index"
                      class="dropdown-item el-select-dropdown__item" @click="selectPublisherChild(item, child, $event)">
                      {{ child }}
                    </div>
                  </div>
                </span>
              </div>
            </div>

            <div v-if="['2'].includes(selectedResourceType)" class="search-row subject-tags-row">
              <label class="form-label">主体标签</label>
              <div class="subject-tags-group">
                <span v-for="item in subjectTags" :key="item.label"
                  :class="{ 'active': selectedSubjectTag === item.label, 'tag-btn': true }"
                  @click="selectedSubjectTag = item.label">{{ item.label }}</span>
              </div>
            </div>

            <div class="search-row">
              <label class="form-label">信息匹配</label>
              <template v-if="selectedResourceType !== '2'">
                <el-select v-model="selectedTheme" placeholder="主题" class="match-select"
                  style="width: 100px; margin-right: 10px;">
                  <el-option v-for="item in themeOptions" :key="item.id" :label="item.categoryName"
                    :value="item.id"></el-option>
                </el-select>
                <el-select v-model="selectedSpecial" placeholder="专题" class="match-select"
                  style="width: 100px; margin-right: 10px;">
                  <el-option v-for="item in specialOptions" :key="item.id" :label="item.categoryName"
                    :value="item.id"></el-option>
                </el-select>
              </template>
              <el-select v-model="selectedSource" placeholder="来源" class="match-select"
                style="width: 100px; margin-right: 10px;">
                <el-option v-for="item in sourceOptions" :key="item.dictValue" :label="item.dictLabel"
                  :value="item.dictValue"></el-option>
              </el-select>
              <el-select v-model="selectedSentiment" placeholder="情感" class="match-select"
                style="width: 100px; margin-right: 10px;">
                <el-option v-for="item in sentimentOptions" :key="item.dictValue" :label="item.dictLabel"
                  :value="item.dictValue"></el-option>
              </el-select>
              <el-switch v-if="selectedResourceType === '2'" v-model="jfpp" :active-value="1" :inactive-value="0"
                active-text="简繁匹配" style="margin-left: 10px;"></el-switch>
            </div>

            <div class="search-row actions-row">
              <el-button @click="resetForm">重置</el-button>
              <el-button type="primary" @click="handleSearch">应用筛选</el-button>
              <el-button type="text" class="expression-mode">转换为表达式搜索模式</el-button>
            </div>
          </div>

          <div class="resource-list">
            <div class="list-header">
              <span>共 {{ total || 0 }} 条数据</span>
              <el-checkbox v-model="allSelected" @change="handleSelectAll">全选</el-checkbox>
              <el-button type="text" @click="handleExport">批量导出</el-button>
              <!-- 翻译按钮 -->
              <el-button type="text" @click="openTranslationDialog">翻译</el-button>
              <div class="right-ops">
                <el-switch v-model="mergeSimilar" active-text="相似信息去重"></el-switch>
                <div class="search-box">
                  <el-select v-model="searchType" placeholder="请选择" size="small">
                    <el-option label="全文" value="content"></el-option>
                    <el-option label="标题" value="title"></el-option>
                  </el-select>
                  <el-input v-model="searchKeyword" placeholder="请输入关键词" size="small" style="width: 150px;"
                    @keyup.enter="handleSearch">
                    <i slot="suffix" class="el-input__icon el-icon-search" @click="handleSearch"></i>
                  </el-input>
                </div>
              </div>
            </div>

            <div class="resource-item" v-for="(item, index) in resourceList" :key="index">
              <div class="item-top">
                <el-checkbox v-model="item.selected" @change="(val) => handleSelectItem(item.id, val)"></el-checkbox>
                <div class="item-title">{{ item.name }}</div>
                <div class="item-ops">
                  <el-button type="text" icon="el-icon-edit"></el-button>
                  <el-button type="text" icon="el-icon-arrow-down"></el-button>
                </div>
              </div>
              <div class="item-content">{{ item.content }}</div>
              <div class="item-meta">
                <span>来源：{{ item.source }}</span>
                <span>发生时间：{{ item.createTime }}</span>
                <el-tag :type="getSentimentType(item.sentiment)" size="mini">{{ item.sentiment }}</el-tag>
                <el-tag v-if="item.matcherLabel" type="primary" size="mini">{{ item.matcherLabel }}</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 翻译弹框 -->
      <el-dialog title="文本翻译" :visible.sync="translationDialogVisible" width="60%" center append-to-body
        :z-index="9999">
        <div class="translation-container">
          <el-input type="textarea" v-model="sourceText" placeholder="请输入要翻译的内容" rows="10"
            class="translation-input"></el-input>
          <el-button type="primary" @click="handleTranslate" style="margin: 10px 0;">翻译</el-button>
          <el-input type="textarea" v-model="translatedText" placeholder="翻译结果将显示在这里" rows="10" disabled
            class="translation-output"></el-input>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { 
  getMonthSituation,
  getTodaySituation,
  selectMonthlyClassificationSystem,
  selectOpenSourceDailyTrend,
  selectResourceCategory,
  selectDataSourceDistribution,
  selectThemeDistribution,
  selectMonthlyTotal,
  getPublisherLabelList,
  getMediatags,
  data_resource_category,
  data_resource_sources,
  manual_main_sentiment,
  selectListBySystemType,
  search,
  translate, 
  exportExcel,
} from "@/api/data/datasource";

import * as echarts from 'echarts';

export default {
  name: 'AssetManagement',
  data() {
    return {
      activeTab: 'overview',
      dataTotal: {},
      todaySituation: {},
      topicCount: {},
      openSourceCount: {},
      resourceCategoryData: [],
      dataSourceData: [],
      themeData: [],
      monthlyTotalData: [],
      resourceList: [], 
      total: 0, 
      mergeSimilar: false,
      resourceTypeOptions: [],
      selectedResourceType: '1',
      timeRangeOptions: [
        { label: '全部', value: '' },
        { label: '近7天', value: '7d' },
        { label: '近30天', value: '30d' },
        { label: '近三个月', value: '3m' },
        { label: '近六个月', value: '6m' },
        { label: '自定义', value: 'custom' }
      ],
      selectedTimeRange: '',
      dateRange: [], 
      publisherSubjects: [],
      selectedPublisherMap: {},
      selectedPublisherParent: '',
      subjectTags: [],
      selectedSubjectTag: '',
      sourceOptions: [],
      sentimentOptions: [],
      themeOptions: [],
      specialOptions: [],
      selectedTheme: '',
      selectedSpecial: '',
      selectedSource: '',
      selectedSentiment: '',
      // 关键词数组
      mustContain: [],
      anyContain: [],
      exclude: [],
      // 当前输入的关键词
      currentMustContain: '',
      currentAnyContain: '',
      currentExclude: '',
      searchType: 'content', 
      searchKeyword: '', 
      selectedIds: [], 
      allSelected: false, 
      jfpp: 0, 
      translationDialogVisible: false, 
      sourceText: '', 
      translatedText: '' 
    };
  },
  mounted() {
    this.$nextTick(() => {
      if (this.activeTab === 'overview') {
        this.fetchAllOverviewData();
        this.initCharts();
      } else {
        this.destroyCharts();
        this.fetchMediatags();
        this.fetchResourceTypeOptions();
        this.fetchSourceOptions();
        this.fetchSentimentOptions();
        this.fetchThemeOptions();
        this.fetchSpecialOptions();
        this.fetchPublisherSubjects();
      }
    });
  },
  methods: {
    // 添加关键词
    addKeyword(type, value) {
      if (value && value.trim() && !this[type].includes(value.trim())) {
        this[type].push(value.trim());
        this[`current${type.charAt(0).toUpperCase() + type.slice(1)}`] = '';
      }
    },
    
    // 移除关键词
    removeKeyword(type, index) {
      this[type].splice(index, 1);
    },
    
    // 输入框失焦时处理
    handleInputBlur(type, value) {
      if (value && value.trim() && !this[type].includes(value.trim())) {
        this[type].push(value.trim());
        this[`current${type.charAt(0).toUpperCase() + type.slice(1)}`] = '';
      }
    },

    // 打开翻译弹框方法
    openTranslationDialog() {
      this.translationDialogVisible = true;
    },

    handleTranslate() {
      if (!this.sourceText.trim()) {
        this.$message.warning('请输入要翻译的内容');
        return;
      }
      translate({ text: this.sourceText }).then(res => {
        if (res.code === 200) {
          this.translatedText = res.msg; 
        } else {
          this.$message.error(res.msg || '翻译失败');
        }
      }).catch(err => {
        console.error('翻译接口调用失败', err);
        this.$message.error('翻译失败，请稍后重试');
      });
    },

    fetchResourceTypeOptions() {
      data_resource_category().then(res => {
        if (res.code === 200) {
          this.resourceTypeOptions = res.data || [];
        } else {
          this.$message.error(res.msg || '获取资源类别下拉框失败');
        }
      }).catch(err => {
        console.error('获取资源类别下拉框接口调用失败', err);
        this.$message.error('获取资源类别下拉框失败，请稍后重试');
      });
    },
    
    fetchSourceOptions() {
      data_resource_sources().then(res => {
        if (res.code === 200) {
          this.sourceOptions = res.data || [];
        } else {
          this.$message.error(res.msg || '获取来源下拉框失败');
        }
      }).catch(err => {
        console.error('获取来源下拉框接口调用失败', err);
        this.$message.error('获取来源下拉框失败，请稍后重试');
      });
    },
    
    fetchSentimentOptions() {
      manual_main_sentiment().then(res => {
        if (res.code === 200) {
          this.sentimentOptions = res.data || [];
        } else {
          this.$message.error(res.msg || '获取情感下拉框失败');
        }
      }).catch(err => {
        console.error('获取情感下拉框接口调用失败', err);
        this.$message.error('获取情感下拉框失败，请稍后重试');
      });
    },
    
    fetchThemeOptions() {
      selectListBySystemType(1).then(res => {
        if (res.code === 200) {
          this.themeOptions = res.data || [];
        } else {
          this.$message.error(res.msg || '获取主题下拉框失败');
        }
      }).catch(err => {
        console.error('获取主题下拉框接口调用失败', err);
        this.$message.error('获取主题下拉框失败，请稍后重试');
      });
    },
    
    fetchSpecialOptions() {
      selectListBySystemType(2).then(res => {
        if (res.code === 200) {
          this.specialOptions = res.data || [];
        } else {
          this.$message.error(res.msg || '获取专题下拉框失败');
        }
      }).catch(err => {
        console.error('获取专题下拉框接口调用失败', err);
        this.$message.error('获取专题下拉框失败，请稍后重试');
      });
    },
    
    fetchMediatags() {
      getMediatags().then(res => {
        if (res.code === 200) {
          this.subjectTags = res.data.map(label => ({ label, value: label }));
        } else {
          this.$message.error(res.msg || '获取主体标签失败');
        }
      }).catch(err => {
        console.error('获取主体标签接口调用失败', err);
        this.$message.error('获取主体标签失败，请稍后重试');
      });
    },
    
    fetchPublisherSubjects() {
      getPublisherLabelList().then(res => {
        if (res.code === 200) {
          this.publisherSubjects = res.data || [];
        } else {
          this.$message.error(res.msg || '获取发布主体下拉框失败');
        }
      }).catch(err => {
        console.error('获取发布主体下拉框接口调用失败', err);
        this.$message.error('获取发布主体下拉框失败，请稍后重试');
      });
    },
    
    handlePublisherParentClick(item) {
      this.publisherSubjects.forEach(subject => {
        if (subject !== item) {
          subject.isOpen = false;
        }
      });
      item.isOpen = !item.isOpen;
      this.selectedPublisherParent = item.isOpen ? item.parentName : '';
    },
    
    getWebsiteNames(item) {
      if (item.children && item.children.length > 0) {
        return item.children[0].websiteNames || [];
      }
      return [];
    },
    
    getSelectedChild(item) {
      return this.selectedPublisherMap[item.parentName] || '';
    },
    
    selectPublisherChild(parent, child, event) {
      event.stopPropagation();
      parent.isOpen = false;
      this.$set(this.selectedPublisherMap, parent.parentName, child);
    },
    
    fetchAllOverviewData() {
      this.fetchDataTotal();
      this.fetchTodaySituation();
      this.fetchTopicCount();
      this.fetchOpenSourceCount();
      this.fetchResourceCategoryData();
      this.fetchDataSourceData();
      this.fetchThemeData();
      this.fetchMonthlyTotalData();
    },
    
    fetchDataTotal() {
      getMonthSituation().then(res => {
        if (res.code === 200) {
          this.dataTotal = res.data || {};
        } else {
          this.$message.error(res.msg || '获取数据总量失败');
        }
      }).catch(err => {
        console.error('获取数据总量接口调用失败', err);
        this.$message.error('获取数据总量失败，请稍后重试');
      });
    },
    
    fetchTodaySituation() {
      getTodaySituation().then(res => {
        if (res.code === 200) {
          this.todaySituation = res.data || {};
        } else {
          this.$message.error(res.msg || '获取今日新增失败');
        }
      }).catch(err => {
        console.error('获取今日新增接口调用失败', err);
        this.$message.error('获取今日新增失败，请稍后重试');
      });
    },
    
    fetchTopicCount() {
      selectMonthlyClassificationSystem().then(res => {
        if (res.code === 200) {
          this.topicCount = res.data || {};
        } else {
          this.$message.error(res.msg || '获取专题数量失败');
        }
      }).catch(err => {
        console.error('获取专题数量接口调用失败', err);
        this.$message.error('获取专题数量失败，请稍后重试');
      });
    },
    
    fetchOpenSourceCount() {
      selectOpenSourceDailyTrend().then(res => {
        if (res.code === 200) {
          this.openSourceCount = res.data || {};
        } else {
          this.$message.error(res.msg || '获取开源引接数量失败');
        }
      }).catch(err => {
        console.error('获取开源引接数量接口调用失败', err);
        this.$message.error('获取开源引接数量失败，请稍后重试');
      });
    },
    
    fetchResourceCategoryData() {
      selectResourceCategory().then(res => {
        if (res.code === 200) {
          this.resourceCategoryData = res.data || [];
          this.initCharts();
        } else {
          this.$message.error(res.msg || '获取资源类别数据失败');
        }
      }).catch(err => {
        console.error('获取资源类别数据接口调用失败', err);
        this.$message.error('获取资源类别数据失败，请稍后重试');
      });
    },
    
    fetchDataSourceData() {
      selectDataSourceDistribution().then(res => {
        if (res.code === 200) {
          this.dataSourceData = res.data || [];
          this.initCharts();
        } else {
          this.$message.error(res.msg || '获取资源来源数据失败');
        }
      }).catch(err => {
        console.error('获取资源来源数据接口调用失败', err);
        this.$message.error('获取资源来源数据失败，请稍后重试');
      });
    },
    
    fetchThemeData() {
      selectThemeDistribution().then(res => {
        if (res.code === 200) {
          this.themeData = res.data || [];
          this.initCharts();
        } else {
          this.$message.error(res.msg || '获取主题分析数据失败');
        }
      }).catch(err => {
        console.error('获取主题分析数据接口调用失败', err);
        this.$message.error('获取主题分析数据失败，请稍后重试');
      });
    },
    
    fetchMonthlyTotalData() {
      selectMonthlyTotal().then(res => {
        if (res.code === 200) {
          this.monthlyTotalData = res.data || [];
          this.initCharts();
        } else {
          this.$message.error(res.msg || '获取资源入库趋势数据失败');
        }
      }).catch(err => {
        console.error('获取资源入库趋势数据接口调用失败', err);
        this.$message.error('获取资源入库趋势数据失败，请稍后重试');
      });
    },
    
    handleTabChange(tab) {
      if (tab.name === 'overview') {
        this.$nextTick(() => {
          this.fetchAllOverviewData();
          this.initCharts();
        });
      } else {
        this.destroyCharts();
        this.fetchMediatags();
        this.fetchResourceTypeOptions();
        this.fetchSourceOptions();
        this.fetchSentimentOptions();
        this.fetchThemeOptions();
        this.fetchSpecialOptions();
        this.fetchPublisherSubjects();
      }
    },
    
    initCharts() {
      this.destroyCharts();
      const checkContainer = (id) => {
        const dom = document.getElementById(id);
        return dom && dom.offsetWidth > 0;
      };
      
      if (checkContainer('category-chart') && this.resourceCategoryData.length > 0) {
        const categoryChart = echarts.init(document.getElementById('category-chart'));
        categoryChart.setOption({
          title: {
            text: '按资源类别分析',
            textStyle: {
              fontFamily: 'YouSheBiaoTiHei', // 字体
              fontSize: 16,                  // 字号
              color: '#FFFFFF',              // 文字颜色
              letterSpacing: 0,              // 字间距
              textShadow: '0 2px 4px #1B1A85', // 文字阴影
              fontWeight: 400                // 字重
            },
            left: 'center',    // 标题水平居中
            top: 0             // 标题顶部对齐（贴近图表顶部）
          },
          tooltip: { trigger: 'item' },
          series: [{
            type: 'pie',
            radius: '55%',
            data: this.resourceCategoryData.map(item => ({
              name: item.NAME,
              value: item.VALUE
            })),
            label: { formatter: '{b}: {d}%' }
          }]
        });
      }
      
      if (checkContainer('source-chart') && this.dataSourceData.length > 0) {
        const sourceChart = echarts.init(document.getElementById('source-chart'));
        sourceChart.setOption({
          title: { text: '按资源来源分析' },
          tooltip: { trigger: 'item' },
          series: [{
            type: 'pie',
            radius: ['30%', '55%'],
            data: this.dataSourceData.map(item => ({
              name: item.NAME,
              value: item.VALUE
            })),
            label: { formatter: '{b}: {d}%' }
          }]
        });
      }
      
      if (checkContainer('topic-chart') && this.themeData.length > 0) {
        const topicChart = echarts.init(document.getElementById('topic-chart'));
        topicChart.setOption({
          title: { text: '按主题分析' },
          tooltip: { trigger: 'item' },
          series: [{
            type: 'pie',
            radius: ['30%', '55%'],
            data: this.themeData.map(item => ({
              name: item.name,
              value: item.value
            })),
            label: { formatter: '{b}: {d}%' }
          }]
        });
      }
      
      if (checkContainer('trend-chart') && this.monthlyTotalData.length > 0) {
        const trendChart = echarts.init(document.getElementById('trend-chart'));
        trendChart.setOption({
          title: { text: '资源入库趋势（近一年）' },
          tooltip: { trigger: 'axis' },
          xAxis: {
            type: 'category',
            data: this.monthlyTotalData.map(item => `${item.month_num}月`)
          },
          yAxis: { type: 'value' },
          series: [{
            data: this.monthlyTotalData.map(item => item.total_count),
            type: 'line',
            smooth: true,
            areaStyle: {}
          }]
        });
      }
      
      window.addEventListener('resize', this.handleResize);
    },
    
    handleResize() {
      ['category-chart', 'source-chart', 'topic-chart', 'trend-chart'].forEach(id => {
        const dom = document.getElementById(id);
        if (dom) {
          const chart = echarts.getInstanceByDom(dom);
          if (chart) chart.resize();
        }
      });
    },
    
    destroyCharts() {
      ['category-chart', 'source-chart', 'topic-chart', 'trend-chart'].forEach(id => {
        const dom = document.getElementById(id);
        if (dom && echarts.getInstanceByDom(dom)) {
          echarts.getInstanceByDom(dom).dispose();
        }
      });
    },
    
    resetForm() {
      this.selectedResourceType = '1';
      this.selectedTimeRange = '';
      this.selectedPublisherMap = {};
      this.selectedPublisherParent = '';
      this.selectedSubjectTag = '';
      this.selectedTheme = '';
      this.selectedSpecial = '';
      this.selectedSource = '';
      this.selectedSentiment = '';
      // 重置关键词
      this.mustContain = [];
      this.anyContain = [];
      this.exclude = [];
      this.currentMustContain = '';
      this.currentAnyContain = '';
      this.currentExclude = '';
      this.dateRange = [];
      this.searchType = 'content';
      this.searchKeyword = '';
      this.selectedIds = [];
      this.allSelected = false;
      this.resourceList.forEach(item => {
        item.selected = false;
      });
      this.publisherSubjects.forEach(item => {
        item.isOpen = false;
      });
      this.jfpp = 0;
    },
    
    handleSearch() {
      const params = {
        resourceType: this.selectedResourceType,
        // 将关键词数组转为逗号分隔的字符串
        mustContain: this.mustContain.join(','),
        anyContain: this.anyContain.join(','),
        exclude: this.exclude.join(','),
        regexMode: false,
        timeRange: this.selectedTimeRange,
        startDate: this.dateRange[0] ? this.formatDate(this.dateRange[0]) : '',
        endDate: this.dateRange[1] ? this.formatDate(this.dateRange[1]) : '',
        websiteNameList: Object.values(this.selectedPublisherMap),
        mediaTagsList: this.selectedSubjectTag ? [this.selectedSubjectTag] : [],
        emotion: this.selectedSentiment,
        title: this.searchType === 'title' ? this.searchKeyword : '',
        content: this.searchType === 'content' ? this.searchKeyword : '',
        deduplication: this.mergeSimilar ? 1 : 0,
        jfpp: this.jfpp ? 1 : 0,
      };

      search(params).then(res => {
        if (res.code === 200) {
          this.resourceList = res.rows || [];
          this.total = res.total || 0;
          this.selectedIds = [];
          this.allSelected = false;
          this.resourceList.forEach(item => {
            item.selected = false;
          });
        } else {
          this.$message.error(res.msg || '搜索失败');
        }
      }).catch(err => {
        console.error('搜索接口调用失败', err);
        this.$message.error('搜索失败，请稍后重试');
      });
    },
    
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    getSentimentType(sentiment) {
      switch (sentiment) {
        case '正面':
          return 'success';
        case '负面':
          return 'danger';
        case '中立':
          return 'warning';
        default:
          return 'info';
      }
    },
    
    handleSelectAll(val) {
      this.allSelected = val;
      if (val) {
        this.selectedIds = this.resourceList.map(item => item.id);
        this.resourceList.forEach(item => {
          item.selected = true;
        });
      } else {
        this.selectedIds = [];
        this.resourceList.forEach(item => {
          item.selected = false;
        });
      }
    },
    
    handleSelectItem(id, isSelected) {
      if (isSelected) {
        if (!this.selectedIds.includes(id)) {
          this.selectedIds.push(id);
        }
      } else {
        this.selectedIds = this.selectedIds.filter(item => item !== id);
      }
      
      this.$nextTick(() => {
        this.allSelected = this.selectedIds.length === this.resourceList.length;
      });
    },
    
    handleExport() {
      this.download(
        "api/search/export",
        {
          ids: this.selectedIds.join(','),
          resourceType: this.selectedResourceType,
        },
        `文件导入任务_${new Date().getTime()}.xlsx`
      );
    }
  },
  beforeDestroy() {
    this.destroyCharts();
    window.removeEventListener('resize', this.handleResize);
  }
};
</script>

<style scoped>
.app-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.top-tabs {
  margin-bottom: 20px;
}

.stats-card {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 15px;
}

.stat-item {
  background: rgba(27,126,242,0.10);
  /* border-radius: 8px; */
  padding: 16px;
  flex: 1;
  min-width: 180px;
}

.num {
  /* font-size: 24px;
  font-weight: 600;
  margin: 8px 0; */
  font-family: "LetsgoDigital-Regular", sans-serif;
  font-size: 28px;
  color: #FFFFFF;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 28px;
  background: linear-gradient(to bottom,
      rgba(255, 255, 255, 1) 0%,
      rgba(204, 216, 242, 1) 50%,
      rgba(56, 105, 204, 1) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.trend {
  font-size: 14px;
  color: #ffffff;
  margin-top: 10px;
  margin-bottom: 10px;
}

.chart-group {
  display: flex;
  /* justify-content: space-between; */
  margin-bottom: 20px;
  gap: 15px;
  background: rgba(27, 126, 242, 0.10);
  /* border-radius: 8px; */
  padding: 16px;
  flex: 1;
}

.chart-group1 {
  display: flex;
  margin-bottom: 0px;
  gap: 15px;
  background: rgba(27, 126, 242, 0.10);
  padding: 16px;
  flex: 1;
}

.chart-item {
  width: 32%;
  height: 250px;
  min-width: 250px;
  /* border: 1px solid #ebeef5; */
  border-radius: 4px;
  padding: 10px;
  box-sizing: border-box;
}

.trend-chart {
  width: 100%;
  height: 300px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  box-sizing: border-box;
}

.main-content {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  box-sizing: border-box;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.search-form {
  margin-bottom: 20px;
}

.search-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.form-label {
  width: 80px;
  text-align: left;
  flex-shrink: 0;
  margin-top: 4px;
}

/* 关键词输入框样式 */
.keyword-input-container {
  flex: 1;
  min-width: 0;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  min-height: 80px;
  align-items: center;
}

.keyword-tag {
  background-color: #f0f2f5;
  border-radius: 4px;
  padding: 2px 8px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.tag-remove {
  cursor: pointer;
  color: #909399;
  font-size: 12px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.tag-remove:hover {
  background-color: #ccc;
  color: #fff;
}

.keyword-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 100px;
  height: 30px;
  padding: 0;
}

.keyword-input::-webkit-input-placeholder {
  color: #c0c4cc;
}

.btn-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  position: relative; 
}

.btn-group.no-border .btn {
  border: none;
  border-radius: 0;
  padding: 0 8px;
}

.btn {
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.btn.active {
  color: #2d884a;
  background-color: transparent;
  border-color: transparent;
}

.btn.has-dropdown {
  display: flex;
  align-items: center;
  gap: 4px;
}

.subject-tags-row {
  align-items: flex-start;
}

.subject-tags-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-left: 80px; 
}

.tag-btn {
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: none; 
  border-radius: 0; 
  background-color: transparent; 
  white-space: nowrap;
}

.tag-btn.active {
  color: #2d884a; 
}

.date-picker {
  width: 240px;
  flex-shrink: 0;
}

.match-select {
  width: 100px;
  margin-right: 10px;
}

.actions-row {
  justify-content: flex-end;
  margin-top: 20px;
}

.expression-mode {
  margin-left: 10px;
  color: #2d884a;
}

.resource-list {
  margin-top: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  flex-wrap: wrap;
  gap: 10px;
}

.right-ops {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-box {
  display: flex;
  align-items: center;
}

.search-box .el-select {
  margin-right: 5px;
}

.resource-item {
  display: flex;
  flex-direction: column;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.resource-item:last-child {
  border-bottom: none;
}

.item-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.item-top .el-checkbox {
  margin-right: 8px;
}

.item-title {
  font-weight: 500;
  flex: 1;
}

.item-ops {
  display: flex;
  gap: 10px;
}

.item-content {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.5;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #999;
}

.publisher-row {
  align-items: center;
}

.publisher-group {
  display: flex;
  gap: 10px;
  flex-wrap: nowrap;
  align-items: center;
}

.publisher-group .btn {
  background-color: #fff;
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 120px;
  position: relative;
}

.publisher-group .btn.active {
  border-color: #2d884a;
  color: #2d884a;
}

.custom-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 2000;
  min-width: 200px;
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  margin-top: 4px;
  padding: 5px 0;
}

.dropdown-item.el-select-dropdown__item {
  padding: 6px 12px;
  clear: both;
  font-weight: 400;
  color: #303133;
  white-space: nowrap;
  cursor: pointer;
}

.dropdown-item.el-select-dropdown__item:hover {
  background-color: #f5f7fa;
  color: #2d884a;
}

.translation-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.translation-input, .translation-output {
  width: 100%;
}

/* 解决弹框层级问题 */
::v-deep .el-dialog__wrapper {
  z-index: 9999 !important;
}

@media (max-width: 1200px) {
  .chart-group {
    flex-direction: column;
  }
  .chart-item {
    width: 100%;
    height: 300px;
  }
}

@media (max-width: 768px) {
  .stats-card {
    flex-direction: column;
  }
  .search-row {
    flex-direction: column;
    align-items: flex-start;
  }
  .form-label {
    width: 100%;
    margin-bottom: 5px;
  }
  .keyword-input-container, .date-picker {
    width: 100%;
  }
  .subject-tags-group {
    margin-left: 0;
  }
  .publisher-group {
    flex-wrap: wrap;
  }
}

/* 数据总量 文本+图片 左右布局 */
.row-layout {
  display: flex;
  align-items: center;
  gap: 20px;
}
.text-col {
  flex: 1; /* 文本占剩余宽度 */
}
.img-col {
  width: 120px; /* 图片宽度，可根据实际调整 */
  height: 120px; /* 图片高度，匹配统计项高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}
.stat-img-item {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 保持图片比例 */
}

.trend-icon {
  width: 16px;  /* 根据实际图标大小调整 */
  height: 16px;
  margin: 0 5px; /* 与文字保持间距 */
  vertical-align: middle; /* 与文字垂直居中对齐 */
}

/* 原有样式保持不变... */
.row-layout {
  display: flex;
  align-items: center;
  gap: 20px;
}
.text-col {
  flex: 1;
}
.img-col {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.stat-img-item {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.percent-text {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #00FFA0;
  letter-spacing: 0;
  font-weight: 400;
  margin-left: 3px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.topic-name {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.topic-tag-img {
  width: 4px;
  height: 20px;
  margin-right: 8px;
  vertical-align: middle;
  margin-left: -12px;
}

.topic-name-wrap {
  display: flex;
  align-items: center;
}
</style>