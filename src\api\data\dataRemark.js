import request from '@/utils/request'

// 获取数据标签列表
export function getDataLabelList(query) {
  return request({
    url: '/dataaccess/dataLabel/list',
    method: 'get',
    params: query
  })
}

// 查询标签组列表
export function getTagGroupList() {
  return request({
    url: '/dataaccess/dataLabel/tagGroupList',
    method: 'get'
  })
}

// 获取数据标签树
export function getDataLabelTree() {
  return request({
    url: '/dataaccess/dataLabel/tree',
    method: 'get'
  })
}

// 获取数据标签详细信息
export function getDataLabelDetail(id) {
  return request({
    url: '/dataaccess/dataLabel/' + id,
    method: 'get'
  })
}

// 新增数据标签和标签组
export function addDataLabel(data) {
  return request({
    url: '/dataaccess/dataLabel',
    method: 'post',
    data: data
  })
}

// 编辑数据标签和标签组
export function updateDataLabel(data) {
  return request({
    url: '/dataaccess/dataLabel',
    method: 'put',
    data: data
  })
}

// 批量删除数据标签和标签组
export function deleteDataLabel(ids) {
  return request({
    url: '/dataaccess/dataLabel/' + ids,
    method: 'delete'
  })
}

// 批量导出数据标签
export function exportDataLabel(data) {
  return request({
    url: '/dataaccess/dataLabel/export',
    method: 'post',
    data: data
  })
}
