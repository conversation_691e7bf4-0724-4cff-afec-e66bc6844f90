import request from '@/utils/request'

// 数据共享分页查询
export function getPageByapiName(rows) {
  return request({
    url: '/data-sharing/getPageByapiName',
    method: 'get',
    params: rows
  })
}

// 数据共享获取所有专题
export function selectAllThematicList(data) {
  return request({
    url: '/data-sharing/selectAllThematicList',
    method: 'get',
    params: data
  })
}

// 数据共享新增
export function addDataSharing(data) {
  return request({
    url: '/data-sharing/add',
    method: 'post',
    data
  })
}

// 数据共享修改
export function updateDataSharing(data) {
  return request({
    url: '/data-sharing/update',
    method: 'post',
    data
  })
}

// 数据共享删除
export function deleteDataSharing(id) {
  return request({
    url: '/data-sharing/delete',
    method: 'get',
    params: id
  })
}

// 数据共享获取Token
export function getToken() {
  return request({
    url: '/data-sharing/getToken',
    method: 'get',
  })  
}

