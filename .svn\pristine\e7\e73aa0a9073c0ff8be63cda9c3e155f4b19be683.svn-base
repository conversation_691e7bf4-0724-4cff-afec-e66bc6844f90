<template>
  <!-- 资产构建页 -->
  <div class="app-container">
    <!-- 顶部Tab切换 -->
    <el-tabs v-model="activeTab" type="card" @tab-click="handleTabChange">
      <el-tab-pane label="资产概览" name="overview" style="font-size: 24px;"></el-tab-pane>
      <el-tab-pane label="资产检索" name="search" style="font-size: 24px;"></el-tab-pane>
    </el-tabs>

    <!-- 资产概览页面 -->
    <div v-if="activeTab === 'overview'" class="overview-page">
      <div class="stats-card">
        <div class="stat-item row-layout">
          <div class="text-col">
            <div style="color: #ffffff;margin-top: 10px;margin-bottom: 10px;">数据总量</div>
            <div class="num">
              {{ dataTotal.total || '0' }}
            </div>
            <div class="trend">
              较上月
              <img :src="dataTotal.direction === '上升'
                ? require('../imgs_erb/icon_zz.png')
                : require('../imgs_erb/icon_xj.png')" class="trend-icon">
              <span class="percent-text" :style="{ color: dataTotal.percent > 0 ? '#00FFA0' : '#ff4d4f' }">
                {{ dataTotal.percent ? Math.abs(dataTotal.percent).toFixed(2) + '%' : '0%' }}
              </span>
            </div>
          </div>
          <div class="img-col">
            <img src="../imgs_erb/icon_charts1.png" alt="数据示意图" class="stat-img-item">
          </div>
        </div>
        <div class="stat-item row-layout">
          <div class="text-col">
            <div style="color: #ffffff;margin-top: 10px;margin-bottom: 10px;">今日新增</div>
            <div class="num">
              {{ todaySituation.todayCount || '0' }}
            </div>
            <div class="trend">
              较昨日
              <img :src="todaySituation.direction === '上升'
                ? require('../imgs_erb/icon_zz.png')
                : require('../imgs_erb/icon_xj.png')" class="trend-icon">
              <span class="percent-text" :style="{ color: todaySituation.percent > 0 ? '#00FFA0' : '#ff4d4f' }">
                {{ todaySituation.percent ? Math.abs(todaySituation.percent).toFixed(2) + '%' : '0%' }}
              </span>
            </div>
          </div>
          <div class="img-col">
            <img src="../imgs_erb/icon_charts2.png" alt="数据示意图" class="stat-img-item">
          </div>
        </div>
        <div class="stat-item row-layout">
          <div class="text-col">
            <div style="color: #ffffff;margin-top: 10px;margin-bottom: 10px;">专题数量</div>
            <div class="num">
              {{ topicCount.count || '0' }}
            </div>
            <div class="trend">
              较上月
              <img :src="topicCount.direction === '上升'
                ? require('../imgs_erb/icon_zz.png')
                : require('../imgs_erb/icon_xj.png')" class="trend-icon">
              <span class="percent-text" :style="{ color: topicCount.percent > 0 ? '#00FFA0' : '#ff4d4f' }">
                {{ topicCount.percent ? Math.abs(topicCount.percent).toFixed(2) + '%' : '0%' }}
              </span>
            </div>
          </div>
          <div class="img-col">
            <img src="../imgs_erb/icon_charts3.png" alt="数据示意图" class="stat-img-item">
          </div>
        </div>
        <div class="stat-item row-layout">
          <div class="text-col">
            <div style="color: #ffffff;margin-top: 10px;margin-bottom: 10px;">开源引接数量</div>
            <div class="num">
              {{ openSourceCount.count || '0' }}
            </div>
            <div class="trend">
              较昨日
              <img :src="openSourceCount.direction === '上升'
                ? require('../imgs_erb/icon_zz.png')
                : require('../imgs_erb/icon_xj.png')" class="trend-icon">
              <span class="percent-text" :style="{ color: openSourceCount.percent > 0 ? '#00FFA0' : '#ff4d4f' }">
                {{ openSourceCount.percent ? Math.abs(openSourceCount.percent).toFixed(2) + '%' : '0%' }}
              </span>
            </div>
          </div>
          <div class="img-col">
            <img src="../imgs_erb/icon_charts4.png" alt="数据示意图" class="stat-img-item">
          </div>
        </div>
      </div>

      <div class="chart-group1">
        <div class="topic-name-wrap">
          <img src="../imgs_erb/icon_zsq.png" class="topic-tag-img">
          <span class="topic-name">资源分类占比</span>
        </div>
      </div>
      <div class="chart-group">
        <PieCharts :chart-data="dataSourceData" title="按资源来源分析" width="100%"  height="300px" />
        <OldDeviceChart :chart-data="resourceCategoryData" title="按资源类别分析" width="100%" height="250px" />
        <EnterprisePieChart :chart-data="themeData" title="按主题分析" width="100%" height="250px" />
      </div>

      <div class="chart-group1">
        <div class="topic-name-wrap">
          <img src="../imgs_erb/icon_zsq.png" class="topic-tag-img">
          <span class="topic-name">资源入库趋势（近一年）</span>
        </div>
      </div>
      <div class="chart-group">
        <div class="trend-chart" id="trend-chart" style="width: 100%; height: 300px;"></div>
      </div>
    </div>

    <!-- 资产检索页面 -->
    <div v-else-if="activeTab === 'search'" class="search-page">
      <div class="main-content">
        <div class="search-panel">
          <div class="search-form">
            <div class="search-row">
              <label class="form-label">必要关键词</label>
              <div class="keyword-input-container">
                <div v-for="(keyword, index) in mustContain" :key="index" class="keyword-tag">
                  {{ keyword }}
                  <span class="tag-remove" @click="removeKeyword('mustContain', index)">×</span>
                </div>
                <el-input v-model="currentMustContain" placeholder="请输入关键词，回车添加" class="keyword-input"
                  @keyup.enter.native="addKeyword('mustContain', currentMustContain)"
                  @blur="handleInputBlur('mustContain', currentMustContain)"></el-input>
              </div>

              <label class="form-label">任意包含</label>
              <div class="keyword-input-container">
                <div v-for="(keyword, index) in anyContain" :key="index" class="keyword-tag">
                  {{ keyword }}
                  <span class="tag-remove" @click="removeKeyword('anyContain', index)">×</span>
                </div>
                <el-input v-model="currentAnyContain" placeholder="请输入关键词，回车添加" class="keyword-input"
                  @keyup.enter.native="addKeyword('anyContain', currentAnyContain)"
                  @blur="handleInputBlur('anyContain', currentAnyContain)"></el-input>
              </div>

              <label class="form-label">需要排除</label>
              <div class="keyword-input-container">
                <div v-for="(keyword, index) in exclude" :key="index" class="keyword-tag">
                  {{ keyword }}
                  <span class="tag-remove" @click="removeKeyword('exclude', index)">×</span>
                </div>
                <el-input v-model="currentExclude" placeholder="请输入关键词，回车添加" class="keyword-input"
                  @keyup.enter.native="addKeyword('exclude', currentExclude)"
                  @blur="handleInputBlur('exclude', currentExclude)"></el-input>
              </div>
            </div>

            <!-- 资源类别：添加class标识，方便DOM选择 -->
            <div class="search-row">
              <label class="form-label">资源类别</label>
              <div class="btn-group no-border resource-type-group">
                <span 
                  v-for="item in resourceTypeOptions" 
                  :key="item.dictValue"
                  :class="{ 'active': selectedResourceType === item.dictValue, 'btn': true }"
                  @click="selectedResourceType = item.dictValue"
                >
                  {{ item.dictLabel }}
                </span>
              </div>
            </div>

            <div class="search-row">
              <label class="form-label">发生时间</label>
              <div class="btn-group no-border">
                <span v-for="item in timeRangeOptions" :key="item.value"
                  :class="{ 'active': selectedTimeRange === item.value, 'btn': true }"
                  @click="selectedTimeRange = item.value">{{ item.label }}</span>
                <el-date-picker v-if="selectedTimeRange === 'custom'" v-model="dateRange" style="width: 240px"
                  value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期"
                  end-placeholder="结束日期"></el-date-picker>
              </div>
            </div>

            <div v-if="['2'].includes(selectedResourceType)" class="search-row publisher-row">
              <label class="form-label">发布主体</label>
              <div class="btn-group no-border publisher-group">
                <span v-for="item in publisherSubjects" :key="item.parentName" :class="{ 
                    'btn': true, 
                    'has-dropdown': item.children && item.children.length > 0,
                    'active': selectedPublisherParent === item.parentName 
                  }" @click="handlePublisherParentClick(item)">
                  {{ getSelectedChild(item) || item.parentName }}
                  <i v-if="item.children && item.children.length > 0" class="el-icon-arrow-down"></i>
                  <div v-if="item.children && item.children.length > 0 && item.isOpen"
                    class="dropdown-menu el-select-dropdown custom-dropdown">
                    <div v-for="(child, index) in getWebsiteNames(item)" :key="index"
                      class="dropdown-item el-select-dropdown__item" @click="selectPublisherChild(item, child, $event)">
                      {{ child }}
                    </div>
                  </div>
                </span>
              </div>
            </div>

            <div v-if="['2'].includes(selectedResourceType)" class="search-row subject-tags-row">
              <label class="form-label">主体标签</label>
              <div class="subject-tags-group">
                <span v-for="item in subjectTags" :key="item.label"
                  :class="{ 'active': selectedSubjectTag === item.label, 'tag-btn': true }"
                  @click="selectedSubjectTag = item.label">{{ item.label }}</span>
              </div>
            </div>

            <div class="search-row head-container">
              <label class="form-label">信息匹配</label>
              <template v-if="selectedResourceType !== '2'">
                <el-select v-model="selectedTheme" placeholder="主题" class="match-select"
                  style="width: 100px; margin-right: 10px;">
                  <el-option v-for="item in themeOptions" :key="item.dictValue" :label="item.dictLabel"
                    :value="item.dictValue"></el-option>
                </el-select>
                <el-select v-model="selectedSpecial" placeholder="专题" class="match-select"
                  style="width: 100px; margin-right: 10px;">
                  <el-option v-for="item in specialOptions" :key="item.dictValue" :label="item.dictLabel"
                    :value="item.dictValue"></el-option>
                </el-select>
                <div class="select-box">
                  <el-select v-model="selectedSource" placeholder="来源" class="match-select"
                    style="width: 100px; margin-right: 10px;">
                    <el-option v-for="item in sourceOptions" :key="item.dictValue" :label="item.dictLabel"
                      :value="item.dictValue"></el-option>
                  </el-select>
                </div>
              </template>
              <el-select v-model="selectedSentiment" placeholder="情感" class="match-select"
                style="width: 100px; margin-right: 10px;">
                <el-option v-for="item in sentimentOptions" :key="item.dictValue" :label="item.dictLabel"
                  :value="item.dictValue"></el-option>
              </el-select>
              <el-switch v-if="selectedResourceType === '2'" v-model="jfpp" :active-value="1" :inactive-value="0"
                active-text="简繁匹配" style="margin-left: 10px; margin-top: 20px;"></el-switch>
            </div>

            <div class="search-row actions-row">
              <div class="public_button export_button fl position_btn" @click="handleExport">
                <i class="mr10 el-icon-upload2" style="margin-right:5px ;"></i>批量导出
              </div>
              <div class="public_button search_btn fl position_btn" @click="openTranslationDialog">
                <i class="mr10 el-icon-search"></i>翻译
              </div>
              <div class="public_button clear_button fl position_btn" @click="resetForm">
                <i class="mr10 el-icon-refresh"></i>重置
              </div>
              <div @click="handleSearch" class="public_button search_btn fl position_btn">
                <i class="mr10 el-icon-search"></i>应用筛选
              </div>
              <el-button type="text" class="expression-mode" @click="isRegexMode = !isRegexMode">
                {{ isRegexMode ? '切换为普通搜索模式' : '转换为表达式搜索模式' }}
              </el-button>
            </div>
          </div>

          <div class="resource-list">
            <div class="list-header">
              <span style="color: #ffffff;">共 {{ total || 0 }} 条数据</span>
              <el-checkbox v-model="allSelected" @change="handleSelectAll"
                style="color: #ffffff !important;">全选</el-checkbox>
              <div class="right-ops">
                <el-switch v-model="mergeSimilar" active-text="相似信息去重"
                  style="color: #ffffff !important; margin-top: 10px;"></el-switch>
                <div class="search-box">
                  <el-select v-model="searchType" placeholder="请选择" size="small" class="head-container">
                    <el-option label="全文" value="content"></el-option>
                    <el-option label="标题" value="title"></el-option>
                  </el-select>
                  <el-input v-model="searchKeyword" placeholder="请输入关键词" size="small" style="width: 150px;"
                    @keyup.enter="handleSearch" class="head-container1">
                    <i slot="suffix" class="el-input__icon el-icon-search" @click="handleSearch"></i>
                  </el-input>
                </div>
              </div>
            </div>

            <div class="resource-item" v-for="(item, index) in resourceList" :key="index">
              <div class="item-top">
                <el-checkbox v-model="item.selected" @change="(val) => handleSelectItem(item.id, val)"></el-checkbox>
                <div class="item-title">{{ item.name }}</div>
              </div>
              <div class="item-content">{{ item.content }}</div>
              <div class="item-meta">
                <span>来源：{{ item.source }}</span>
                <span>发生时间：{{ item.createTime }}</span>
                <el-tag :type="getSentimentType(item.sentiment)" size="mini">{{ item.sentiment }}</el-tag>
                <el-tag v-if="item.matcherLabel" type="primary" size="mini">{{ item.matcherLabel }}</el-tag>
              </div>
            </div>

            <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pageSize"
              @pagination="handleSearch" style="margin-top: 20px; float: right;" class="pagination" />
          </div>
        </div>
      </div>

      <!-- 翻译弹框 -->
      <el-dialog title="文本翻译" :visible.sync="translationDialogVisible" width="60%" center append-to-body
        :z-index="9999">
        <div class="translation-container">
          <el-input type="textarea" v-model="sourceText" placeholder="请输入要翻译的内容" rows="10"></el-input>
          <div class="public_button clear_button fl position_btn" @click="handleTranslate">
            <i class="mr10 el-icon-refresh"></i>翻译
          </div>
          <el-input type="textarea" v-model="translatedText" placeholder="翻译结果将显示在这里" rows="10" disabled></el-input>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { 
  getMonthSituation,
  getTodaySituation,
  selectMonthlyClassificationSystem,
  selectOpenSourceDailyTrend,
  selectResourceCategory,
  selectDataSourceDistribution,
  selectThemeDistribution,
  selectMonthlyTotal,
  getPublisherLabelList,
  getMediatags,
  data_resource_category,
  data_resource_sources,
  manual_main_sentiment,
  manual_theme,
  manual_main_topic,
  search,
  translate, 
  exportExcel,
} from "@/api/data/datasource";

import OldDeviceChart from '@/views/components/OldDeviceChart.vue'
import EnterprisePieChart from '@/views/components/EnterprisePieChart.vue'
import PieCharts from '@/views/components/PieCharts.vue'
import * as echarts from 'echarts';

export default {
  name: 'AssetManagement',
  components: { OldDeviceChart, EnterprisePieChart, PieCharts },
  data() {
    return {
      currentPage: 1,
      pageSize: 10,
      activeTab: 'overview',
      dataTotal: {},
      todaySituation: {},
      topicCount: {},
      openSourceCount: {},
      resourceCategoryData: [],
      dataSourceData: [],
      themeData: [],
      monthlyTotalData: [],
      resourceList: [], 
      total: 0, 
      mergeSimilar: false,
      resourceTypeOptions: [],
      selectedResourceType: '1', // 与“事件信息”的dictValue保持一致
      timeRangeOptions: [
        { label: '全部', value: '' },
        { label: '近7天', value: '7d' },
        { label: '近30天', value: '30d' },
        { label: '近三个月', value: '3m' },
        { label: '近六个月', value: '6m' },
        { label: '自定义', value: 'custom' }
      ],
      selectedTimeRange: '',
      dateRange: [], 
      publisherSubjects: [],
      selectedPublisherMap: {},
      selectedPublisherParent: '',
      subjectTags: [],
      selectedSubjectTag: '',
      sourceOptions: [],
      sentimentOptions: [],
      themeOptions: [],
      specialOptions: [],
      selectedTheme: '',
      selectedSpecial: '',
      selectedSource: '',
      selectedSentiment: '',
      mustContain: [],
      anyContain: [],
      exclude: [],
      currentMustContain: '',
      currentAnyContain: '',
      currentExclude: '',
      searchType: 'content', 
      searchKeyword: '', 
      selectedIds: [], 
      allSelected: false, 
      jfpp: 0, 
      translationDialogVisible: false, 
      sourceText: '', 
      translatedText: '',
      customData: [],
      customDataA: [],
      customDataB: [],
      isRegexMode: false, // 表达式搜索模式标识
    };
  },
  mounted() {
    this.$nextTick(() => {
      if (this.activeTab === 'overview') {
        this.fetchAllOverviewData();
        this.initCharts();
      } else {
        this.destroyCharts();
        this.fetchSearchPageData(); // 统一加载检索页数据
      }
    });
  },
  methods: {
    // 统一加载检索页所需数据
    fetchSearchPageData() {
      Promise.all([
        this.fetchMediatags(),
        this.fetchResourceTypeOptions(),
        this.fetchSourceOptions(),
        this.fetchSentimentOptions(),
        this.fetchThemeOptions(),
        this.fetchSpecialOptions(),
        this.fetchPublisherSubjects()
      ]).then(() => {
        // 所有数据加载完成后，执行默认选中逻辑
        this.setDefaultResourceTypeActive();
      });
    },

    // 设置资源类别默认选中样式（核心修复：直接操作DOM）
    setDefaultResourceTypeActive() {
      this.$nextTick(() => {
        // 延迟50ms确保DOM完全渲染（极端场景保障）
        setTimeout(() => {
          const resourceTypeGroup = document.querySelector('.resource-type-group');
          if (resourceTypeGroup) {
            const defaultBtn = Array.from(resourceTypeGroup.children).find(
              btn => btn.textContent.trim() === '事件信息'
            );
            if (defaultBtn) {
              defaultBtn.click(); // 模拟点击“事件信息”选项
            }
          }
        }, 50);
      });
    },

    // 添加关键词
    addKeyword(type, value) {
      if (value && value.trim() && !this[type].includes(value.trim())) {
        this[type].push(value.trim());
        this[`current${type.charAt(0).toUpperCase() + type.slice(1)}`] = '';
      }
    },

    // 移除关键词
    removeKeyword(type, index) {
      this[type].splice(index, 1);
    },

    // 输入框失焦时添加关键词
    handleInputBlur(type, value) {
      if (value && value.trim() && !this[type].includes(value.trim())) {
        this[type].push(value.trim());
        this[`current${type.charAt(0).toUpperCase() + type.slice(1)}`] = '';
      }
    },

    // 打开翻译弹框
    openTranslationDialog() {
      this.translationDialogVisible = true;
    },

    // 执行翻译
    handleTranslate() {
      if (!this.sourceText.trim()) {
        this.$message.warning('请输入要翻译的内容');
        return;
      }
      translate({ text: this.sourceText }).then(res => {
        if (res.code === 200) {
          this.translatedText = res.msg; 
        } else {
          this.$message.error(res.msg || '翻译失败');
        }
      }).catch(err => {
        console.error('翻译接口调用失败', err);
        this.$message.error('翻译失败，请稍后重试');
      });
    },

    // 获取资源类别选项
    fetchResourceTypeOptions() {
      return new Promise((resolve, reject) => {
        data_resource_category().then(res => {
          if (res.code === 200) {
            this.resourceTypeOptions = res.data || [];
            resolve();
          } else {
            this.$message.error(res.msg || '获取资源类别下拉框失败');
            reject(res.msg);
          }
        }).catch(err => {
          console.error('获取资源类别下拉框接口调用失败', err);
          this.$message.error('获取资源类别下拉框失败，请稍后重试');
          reject(err);
        });
      });
    },

    // 获取来源选项
    fetchSourceOptions() {
      return new Promise((resolve, reject) => {
        data_resource_sources().then(res => {
          if (res.code === 200) {
            this.sourceOptions = res.data || [];
            resolve();
          } else {
            this.$message.error(res.msg || '获取来源下拉框失败');
            reject(res.msg);
          }
        }).catch(err => {
          console.error('获取来源下拉框接口调用失败', err);
          this.$message.error('获取来源下拉框失败，请稍后重试');
          reject(err);
        });
      });
    },

    // 获取情感选项
    fetchSentimentOptions() {
      return new Promise((resolve, reject) => {
        manual_main_sentiment().then(res => {
          if (res.code === 200) {
            this.sentimentOptions = res.data || [];
            resolve();
          } else {
            this.$message.error(res.msg || '获取情感下拉框失败');
            reject(res.msg);
          }
        }).catch(err => {
          console.error('获取情感下拉框接口调用失败', err);
          this.$message.error('获取情感下拉框失败，请稍后重试');
          reject(err);
        });
      });
    },

    // 获取主题选项
    fetchThemeOptions() {
      return new Promise((resolve, reject) => {
        manual_theme().then(res => {
          if (res.code === 200) {
            this.themeOptions = res.data.map(item => ({
              dictValue: item.dictValue,
              dictLabel: item.dictLabel
            }));
            resolve();
          } else {
            this.$message.error(res.msg || '获取主题下拉框失败');
            reject(res.msg);
          }
        }).catch(err => {
          console.error('获取主题下拉框接口调用失败', err);
          this.$message.error('获取主题下拉框失败，请稍后重试');
          reject(err);
        });
      });
    },

    // 获取专题选项
    fetchSpecialOptions() {
      return new Promise((resolve, reject) => {
        manual_main_topic().then(res => {
          if (res.code === 200) {
            this.specialOptions = res.data.map(item => ({
              dictValue: item.dictValue,
              dictLabel: item.dictLabel
            }));
            resolve();
          } else {
            this.$message.error(res.msg || '获取专题下拉框失败');
            reject(res.msg);
          }
        }).catch(err => {
          console.error('获取专题下拉框接口调用失败', err);
          this.$message.error('获取专题下拉框失败，请稍后重试');
          reject(err);
        });
      });
    },

    // 获取主体标签
    fetchMediatags() {
      return new Promise((resolve, reject) => {
        getMediatags().then(res => {
          if (res.code === 200) {
            this.subjectTags = res.data.map(label => ({ label, value: label }));
            resolve();
          } else {
            this.$message.error(res.msg || '获取主体标签失败');
            reject(res.msg);
          }
        }).catch(err => {
          console.error('获取主体标签接口调用失败', err);
          this.$message.error('获取主体标签失败，请稍后重试');
          reject(err);
        });
      });
    },

    // 获取发布主体
    fetchPublisherSubjects() {
      return new Promise((resolve, reject) => {
        getPublisherLabelList().then(res => {
          if (res.code === 200) {
            this.publisherSubjects = res.data || [];
            resolve();
          } else {
            this.$message.error(res.msg || '获取发布主体下拉框失败');
            reject(res.msg);
          }
        }).catch(err => {
          console.error('获取发布主体下拉框接口调用失败', err);
          this.$message.error('获取发布主体下拉框失败，请稍后重试');
          reject(err);
        });
      });
    },

    // 点击发布主体父项
    handlePublisherParentClick(item) {
      this.publisherSubjects.forEach(subject => {
        if (subject !== item) {
          subject.isOpen = false;
        }
      });
      item.isOpen = !item.isOpen;
      this.selectedPublisherParent = item.isOpen ? item.parentName : '';
    },

    // 获取网站名称列表
    getWebsiteNames(item) {
      if (item.children && item.children.length > 0) {
        return item.children[0].websiteNames || [];
      }
      return [];
    },

    // 获取选中的子项
    getSelectedChild(item) {
      return this.selectedPublisherMap[item.parentName] || '';
    },

    // 选择发布主体子项
    selectPublisherChild(parent, child, event) {
      event.stopPropagation();
      parent.isOpen = false;
      this.$set(this.selectedPublisherMap, parent.parentName, child);
    },

    // 加载概览页所有数据
    fetchAllOverviewData() {
      this.fetchDataTotal();
      this.fetchTodaySituation();
      this.fetchTopicCount();
      this.fetchOpenSourceCount();
      this.fetchResourceCategoryData();
      this.fetchDataSourceData();
      this.fetchThemeData();
      this.fetchMonthlyTotalData();
    },

    // 获取数据总量
    fetchDataTotal() {
      getMonthSituation().then(res => {
        if (res.code === 200) {
          this.dataTotal = res.data || {};
        } else {
          this.$message.error(res.msg || '获取数据总量失败');
        }
      }).catch(err => {
        console.error('获取数据总量接口调用失败', err);
        this.$message.error('获取数据总量失败，请稍后重试');
      });
    },

    // 获取今日新增
    fetchTodaySituation() {
      getTodaySituation().then(res => {
        if (res.code === 200) {
          this.todaySituation = res.data || {};
        } else {
          this.$message.error(res.msg || '获取今日新增失败');
        }
      }).catch(err => {
        console.error('获取今日新增接口调用失败', err);
        this.$message.error('获取今日新增失败，请稍后重试');
      });
    },

    // 获取专题数量
    fetchTopicCount() {
      selectMonthlyClassificationSystem().then(res => {
        if (res.code === 200) {
          this.topicCount = res.data || {};
        } else {
          this.$message.error(res.msg || '获取专题数量失败');
        }
      }).catch(err => {
        console.error('获取专题数量接口调用失败', err);
        this.$message.error('获取专题数量失败，请稍后重试');
      });
    },

    // 获取开源引接数量
    fetchOpenSourceCount() {
      selectOpenSourceDailyTrend().then(res => {
        if (res.code === 200) {
          this.openSourceCount = res.data || {};
        } else {
          this.$message.error(res.msg || '获取开源引接数量失败');
        }
      }).catch(err => {
        console.error('获取开源引接数量接口调用失败', err);
        this.$message.error('获取开源引接数量失败，请稍后重试');
      });
    },

    // 获取资源类别数据
    fetchResourceCategoryData() {
      selectResourceCategory().then(res => {
        if (res.code === 200) {
          this.resourceCategoryData = res.data.map(item => ({
            name: item.NAME || item.name,
            value: item.VALUE || item.value
          }));
        }
      });
    },

    // 获取数据源分布
    fetchDataSourceData() {
      selectDataSourceDistribution().then(res => {
        if (res.code === 200) {
          this.dataSourceData = res.data
            .map(item => ({
              name: item.NAME || '未知来源',
              value: item.VALUE || 0,
              percent: (item.percentage * 100).toFixed(1)
            }));
        }
      });
    },

    // 获取主题分布
    fetchThemeData() {
      selectThemeDistribution().then(res => {
        if (res.code === 200) {
          this.themeData = res.data.map(item => ({
            name: item.NAME || item.name,
            value: item.VALUE || item.value
          }));
        }
      });
    },

    // 获取月度总量（趋势图）
    fetchMonthlyTotalData() {
      selectMonthlyTotal().then(res => {
        if (res.code === 200) {
          this.monthlyTotalData = res.data || [];
          this.initCharts();
        } else {
          this.$message.error(res.msg || '获取资源入库趋势数据失败');
        }
      }).catch(err => {
        console.error('获取资源入库趋势数据接口调用失败', err);
        this.$message.error('获取资源入库趋势数据失败，请稍后重试');
      });
    },

    // Tab切换事件
    handleTabChange(tab) {
      if (tab.name === 'overview') {
        this.$nextTick(() => {
          this.fetchAllOverviewData();
          this.initCharts();
        });
      } else {
        this.destroyCharts();
        this.fetchSearchPageData(); // 切换到检索页时加载数据并设置选中样式
      }
    },

    // 初始化图表
    initCharts() {
      this.destroyCharts();
      const checkContainer = (id) => {
        const dom = document.getElementById(id);
        return dom && dom.offsetWidth > 0;
      };
      if (checkContainer('trend-chart') && this.monthlyTotalData.length > 0) {
        const trendChart = echarts.init(document.getElementById('trend-chart'));
        trendChart.setOption({
          title: { text: '' },
          tooltip: { trigger: 'axis' },
          grid: {
            left: '0%',
            right: '0%',
            bottom: '0%',
            top: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: this.monthlyTotalData.map(item => `${item.month_num}月`),
            axisLine: {
              lineStyle: { color: '#ffffff80' }
            },
            axisLabel: {
              color: '#A9C7EA'
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              lineStyle: { color: '#ffffff80' }
            },
            axisLabel: {
              color: '#A9C7EA'
            },
            splitLine: {
              lineStyle: { color: '#ffffff20' }
            }
          },
          series: [{
            type: 'line',
            data: this.monthlyTotalData.map(item => item.total_count),
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(64, 158, 255, 0.8)' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.2)' }
                ]
              }
            },
            lineStyle: {
              color: '#409EFF',
              width: 2
            }
          }],
          backgroundColor: 'transparent'
        });
      }
      window.addEventListener('resize', this.handleResize);
    },

    // 图表自适应
    handleResize() {
      const trendChart = document.getElementById('trend-chart');
      if (trendChart) {
        const chart = echarts.getInstanceByDom(trendChart);
        if (chart) chart.resize();
      }
    },

    // 销毁图表
    destroyCharts() {
      const trendChart = document.getElementById('trend-chart');
      if (trendChart && echarts.getInstanceByDom(trendChart)) {
        echarts.getInstanceByDom(trendChart).dispose();
      }
      window.removeEventListener('resize', this.handleResize);
    },

    // 重置表单
    resetForm() {
      this.selectedResourceType = '1';
      this.selectedTimeRange = '';
      this.selectedPublisherMap = {};
      this.selectedPublisherParent = '';
      this.selectedSubjectTag = '';
      this.selectedTheme = '';
      this.selectedSpecial = '';
      this.selectedSource = '';
      this.selectedSentiment = '';
      this.mustContain = [];
      this.anyContain = [];
      this.exclude = [];
      this.currentMustContain = '';
      this.currentAnyContain = '';
      this.currentExclude = '';
      this.dateRange = [];
      this.searchType = 'content';
      this.searchKeyword = '';
      this.selectedIds = [];
      this.allSelected = false;
      this.resourceList.forEach(item => {
        item.selected = false;
      });
      this.publisherSubjects.forEach(item => {
        item.isOpen = false;
      });
      this.jfpp = 0;
      this.isRegexMode = false;

      // 重置后重新设置默认选中样式
      this.$nextTick(() => {
        this.setDefaultResourceTypeActive();
      });
    },

    // 执行搜索
    handleSearch() {
      // 动态构建关键词参数（根据模式切换参数名）
      const keywordParams = this.isRegexMode 
        ? {
            mustRegex: this.mustContain.join(','),
            anyRegex: this.anyContain.join(','),
            excludeRegex: this.exclude.join(','),
            regexMode: true // 传递模式标识
          }
        : {
            mustContain: this.mustContain.join(','),
            anyContain: this.anyContain.join(','),
            exclude: this.exclude.join(','),
            regexMode: false // 传递模式标识
          };

      const params = {
        resourceType: this.selectedResourceType,
        ...keywordParams, // 合并关键词参数
        timeRange: this.selectedTimeRange,
        startDate: this.dateRange[0] ? this.formatDate(this.dateRange[0]) : '',
        endDate: this.dateRange[1] ? this.formatDate(this.dateRange[1]) : '',
        websiteNameList: Object.values(this.selectedPublisherMap),
        mediaTagsList: this.selectedSubjectTag ? [this.selectedSubjectTag] : [],
        emotion: this.selectedSentiment,
        title: this.searchType === 'title' ? this.searchKeyword : '',
        content: this.searchType === 'content' ? this.searchKeyword : '',
        deduplication: this.mergeSimilar ? 1 : 0,
        jfpp: this.jfpp ? 1 : 0,
        pageNum: this.currentPage,
        pageSize: this.pageSize,
        themeId: this.selectedTheme,
        mainTopicId: this.selectedSpecial,
        dataSource: this.selectedSource,
      };

      // search(params).then(res => {
      //   if (res.code === 200) {
      //     this.resourceList = res.rows || [];
      //     this.total = res.total || 0;
      //     this.selectedIds = [];
      //     this.allSelected = false;
      //     this.resourceList.forEach(item => {
      //       item.selected = false;
      //     });
      //   } else {
      //     this.$message.error(res.msg || '搜索失败');
      //   }
      search(params).then(res => {
        if (res.code === 200) {
          this.resourceList = (res.rows || []).map(item => ({
            ...item,
            selected: false 
          }));
          this.total = res.total || 0;
          this.selectedIds = [];
          this.allSelected = false;
        } else {
          this.$message.error(res.msg || '搜索失败');
        }
      }).catch(err => {
        console.error('搜索接口调用失败', err);
        this.$message.error('搜索失败，请稍后重试');
      });
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 根据情感获取标签类型
    getSentimentType(sentiment) {
      switch (sentiment) {
        case '正面':
          return 'success';
        case '负面':
          return 'danger';
        case '中立':
          return 'warning';
        default:
          return 'info';
      }
    },

    // 全选/取消全选
    handleSelectAll(val) {
      this.allSelected = val;
      if (val) {
        this.selectedIds = this.resourceList.map(item => item.id);
        this.resourceList.forEach(item => {
          item.selected = true;
        });
      } else {
        this.selectedIds = [];
        this.resourceList.forEach(item => {
          item.selected = false;
        });
      }
    },

    // 单个选择项变更
    handleSelectItem(id, isSelected) {
      if (isSelected) {
        if (!this.selectedIds.includes(id)) {
          this.selectedIds.push(id);
        }
      } else {
        this.selectedIds = this.selectedIds.filter(item => item !== id);
      }
      this.$nextTick(() => {
        this.allSelected = this.selectedIds.length === this.resourceList.length;
      });
    },

    // 批量导出
    handleExport() {
      if (this.selectedIds.length === 0) {
        this.$message.warning('请先选择要导出的数据');
        return;
      }
      this.download(
        "api/search/export",
        {
          ids: this.selectedIds.join(','),
          resourceType: this.selectedResourceType,
        },
        `文件导入任务_${new Date().getTime()}.xlsx`
      );
    }
  },
  beforeDestroy() {
    this.destroyCharts();
  }
};
</script>

<style scoped>
/* 原有样式保持不变... */
.app-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.top-tabs {
  margin-bottom: 20px;
}

.stats-card {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 15px;
}

.stat-item {
  background: rgba(27,126,242,0.10);
  padding: 3px 20px;
  flex: 1;
  min-width: 180px;
}

.num {
  font-family: "LetsgoDigital-Regular", sans-serif;
  font-size: 28px;
  font-weight: 700;
  letter-spacing: 0px;
  line-height: 28px;
  background: linear-gradient(to bottom,
      rgba(255, 255, 255, 1) 0%,
      rgba(204, 216, 242, 1) 50%,
      rgba(56, 105, 204, 1) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.trend {
  font-size: 14px;
  color: #ffffff;
  margin-top: 10px;
  margin-bottom: 10px;
}

.chart-group {
  display: flex;
  gap: 15px;
  background: rgba(27, 126, 242, 0.10);
  padding: 16px;
  flex: 1;
}

.chart-group1 {
  display: flex;
  gap: 15px;
  background: rgba(27, 126, 242, 0.10);
  padding: 16px;
  flex: 1;
}

.trend-chart {
  width: 100%;
  height: 300px;
}

.main-content {
  padding: 15px;
  box-sizing: border-box;
}

.search-form {
  margin-bottom: 20px;
}

.search-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.form-label {
  width: 80px;
  text-align: left;
  flex-shrink: 0;
  margin-top: 4px;
  color: #ffffff;
}

.keyword-input-container {
  flex: 1;
  min-width: 0;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 5px 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  min-height: 80px;
  align-items: center;
}

.keyword-tag {
  background-color: #f0f2f5;
  border-radius: 4px;
  padding: 2px 8px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.tag-remove {
  cursor: pointer;
  color: #909399;
  font-size: 12px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.tag-remove:hover {
  background-color: #ccc;
  color: #fff;
}

.keyword-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 100px;
  height: 30px;
  padding: 0;
}

.keyword-input::-webkit-input-placeholder {
  color: #c0c4cc;
}

.btn-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  position: relative; 
}

.btn-group.no-border .btn {
  border: none;
  border-radius: 0;
  padding: 0 8px;
}

.btn-group .btn {
  color: #ffffff;
}
.btn-group .btn.active {
  color: #A9C7EA !important;
}

.subject-tags-row {
  align-items: flex-start;
}

.subject-tags-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-left: 97px;
  margin-top: -32px;
}

.subject-tags-group .tag-btn {
  color: #ffffff;
}
.subject-tags-group .tag-btn.active {
  color: #A9C7EA !important;
}

.date-picker {
  width: 240px;
  flex-shrink: 0;
}

.match-select {
  width: 100px;
  margin-right: 10px;
}

.actions-row {
  justify-content: flex-end;
  margin-top: 20px;
}

.expression-mode {
  margin-left: 10px;
  color: #ffffff;
}

.resource-list {
  margin-top: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(250, 250, 250, 0.5);
  flex-wrap: wrap;
  gap: 10px;
}

.right-ops {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-box {
  display: flex;
  align-items: center;
}

.search-box .el-select {
  margin-right: 5px;
}

.resource-item {
  display: flex;
  flex-direction: column;
  padding: 10px 0;
  border-bottom: 1px solid rgba(250, 250, 250, 0.5);
}

.resource-item:last-child {
  border-bottom: none;
}

.item-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.item-top .el-checkbox {
  margin-right: 8px;
}

.item-title {
  font-weight: 500;
  flex: 1;
  color: #ffffff !important;
}

.item-ops {
  display: flex;
  gap: 10px;
}

.item-content {
  color: #A9C7EA !important;
  margin-bottom: 8px;
  line-height: 1.5;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #afb2b7;
}

.publisher-row {
  align-items: center;
}

.publisher-group {
  display: flex;
  gap: 10px;
  flex-wrap: nowrap;
  align-items: center;
}

.publisher-group .btn {
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 120px;
  position: relative;
}

.publisher-group .btn.active {
  border-color:#A9C7EA !important;
  color: #A9C7EA !important;
}

.custom-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 2000;
  min-width: 200px;
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  margin-top: 4px;
  padding: 5px 0;
}

.dropdown-item.el-select-dropdown__item {
  padding: 6px 12px;
  clear: both;
  font-weight: 400;
  color: #303133;
  white-space: nowrap;
  cursor: pointer;
}

.dropdown-item.el-select-dropdown__item:hover {
  background-color: #f5f7fa;
  color: #A9C7EA !important;
}

.translation-container {
  margin: 10px 0;
}

::v-deep .el-dialog__wrapper {
  z-index: 9999 !important;
}

@media (max-width: 1200px) {
  .chart-group {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .stats-card {
    flex-direction: column;
  }
  .search-row {
    flex-direction: column;
    align-items: flex-start;
  }
  .form-label {
    width: 100%;
    margin-bottom: 5px;
  }
  .keyword-input-container, .date-picker {
    width: 100%;
  }
  .subject-tags-group {
    margin-left: 0;
  }
  .publisher-group {
    flex-wrap: wrap;
  }
}

.row-layout {
  display: flex;
  align-items: center;
  gap: 20px;
}
.text-col {
  flex: 1;
}
.img-col {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}
.stat-img-item {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.trend-icon {
  width: 16px;
  height: 16px;
  margin: 0 5px;
  vertical-align: middle;
}

.percent-text {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  letter-spacing: 0;
  font-weight: 400;
  margin-left: 3px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.topic-name {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
}

.topic-tag-img {
  width: 4px;
  height: 20px;
  margin-right: 8px;
  vertical-align: middle;
  margin-left: -12px;
}

.topic-name-wrap {
  display: flex;
  align-items: center;
}

::v-deep .el-select .el-input__inner {
  background-color: #ffffff;
  border-color: #dcdcdc;
  color: #333333;
}
::v-deep .el-select:focus .el-input__inner,
::v-deep .el-select:hover .el-input__inner,
::v-deep .el-select-dropdown {
  border-color: #409EFF;
}
::v-deep .el-select-dropdown__item.selected {
  background-color: #409EFF;
  color: #ffffff;
}

::v-deep .el-switch.is-checked .el-switch__core {
  background-color: #409EFF;
  border-color: #409EFF;
}
::v-deep .el-switch .el-switch__core {
  background-color: #62f1f1;
  border-color: #62f1f1;
}

::v-deep .el-tabs--card>.el-tabs__header {
  border-bottom: none;
  border-radius: 4px 4px 0 0;
}

::v-deep .el-tabs--card>.el-tabs__header .el-tabs__nav {
  border: 0;
}

::v-deep .el-tabs--top.el-tabs--card>.el-tabs__header .el-tabs__item {
  border: 0;
  font-size: 18px;
}

.keyword-input-container {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  min-height: 42px;
  padding: 5px;
  margin-right: 10px;
}
.keyword-input-container ::v-deep .el-input__inner {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  height: 36px !important;
  line-height: 36px !important;
}

.keyword-tag {
  background: rgba(0, 255, 160, 0.20);
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  letter-spacing: 0;
  font-weight: 400;
}

.keyword-input {
  height: 36px;
  padding: 0 10px;
}

::v-deep .el-textarea__inner {
  background-color: transparent !important;
  border: 0 !important;
}

::v-deep .el-switch__label {
  color: #fff;
}

.head-container ::v-deep .el-input__inner {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  height: 40px !important;
  line-height: 40px !important;
  color: #ffffff !important;
  margin-top: 10px;
}

.head-container ::v-deep .el-form-item__label {
  height: 40px !important;
  line-height: 40px !important;
}

.head-container ::v-deep .el-input__suffix {
  height: 100%;
  right: 5px;
  margin-top: 4px;
  transition: all .3s;
  pointer-events: none;
}

.head-container1 ::v-deep .el-input__inner {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  height: 40px !important;
  line-height: 40px !important;
  color: #ffffff !important;
  margin-top: 10px;
}

.head-container1 ::v-deep .el-form-item__label {
  height: 40px !important;
  line-height: 40px !important;
}

.head-container1 ::v-deep .el-input__suffix {
  height: 100%;
  right: 5px;
  margin-top: 8px;
  transition: all .3s;
  pointer-events: none;
}

.position_btn {
  margin-right: 0px;
}

/* 资源类别 - 按钮悬浮样式 */
.btn-group .btn:hover {
  color: #409EFF !important;
  cursor: pointer;
  transition: color 0.2s ease;
}

/* 发生时间 - 按钮悬浮样式（复用资源类别悬浮逻辑，统一交互） */
.btn-group.no-border .btn:hover {
  color: #409EFF !important;
  cursor: pointer;
  transition: color 0.2s ease;
}

/* 发布主体 - 按钮悬浮样式 */
.publisher-group .btn:hover {
  border-color: #409EFF !important;
  color: #409EFF !important;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* 主体标签 - 悬浮样式 */
.subject-tags-group .tag-btn:hover {
  color: #409EFF !important;
  cursor: pointer;
  transition: color 0.2s ease;
}
</style>