// 高级检索接口文件
import request from '@/utils/request'

 // 根据主题获取资源类别
 export function data_resource_category_history(data) {
  return request({
    url: '/dataaccess/HistoryAnalysisTopicTrends/selectByThemeId',        // 请求地址
    method: 'get',
     params: data
  })
}
 // 资源检索搜索
 export function Historysearch(data) {
   return request({
     url: '/dataaccess/HistoryAnalysisTopicTrends/selectByPage',
     method: 'post',
     data
   })
 }
