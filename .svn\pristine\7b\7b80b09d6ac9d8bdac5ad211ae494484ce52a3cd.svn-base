#app {

  .main-container {
    height: 100%;
    transition: margin-left .28s;
    margin-left: $base-sidebar-width;
    position: relative;
  }

  .sidebarHide {
    margin-left: 0 !important;
  }

  .sidebar-container {
    -webkit-transition: width .28s;
    transition: width 0.28s;
    width: $base-sidebar-width !important;
    // background-color: $base-menu-background;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    // -webkit-box-shadow: 2px 0 6px rgba(0,21,41,.35);
    // box-shadow: 2px 0 6px rgba(0,21,41,.35);

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 90px);
        padding-top: 20px;
        background-image: linear-gradient(179deg, #0B133D 0%, rgba(11, 19, 61, 0.60) 100%);
        // margin-top: 90px;
        // width: 230px;
        // margin-left: 26px;
        // background-image: linear-gradient(179deg, rgba(255,255,255,1) 0%, #FFFFFF 31%);
        // box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.06);
        // border-radius: 4px;
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      // width: 100% !important;
      // width: 190px;
      // margin: auto;
    }

    .el-menu-item,
    .el-submenu__title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;

      background: #FFFFFF;
      // box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.1);
      border-radius: 2px;
      width: 240px;
      height: 50px;
      line-height: 50px;
      margin: auto;
      margin-top: 10px;

      font-size: 16px;
      color: #FFFFFF !important;
      letter-spacing: 0;
      font-weight: 500;
    }

    .submenu-title-noDropdown.is-active {
      color: $base-menu-color-active !important;
      // color: $base-menu-color-active !important;
      // background-image: linear-gradient(90deg, #0d7154 0%, #0d7154a3 100%) !important;
      // box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.1) !important;

      opacity: 0.96;
      background-image: linear-gradient(90deg, rgba(0, 43, 169, 0.00) 0%, #002BA9 35%, #002BA9 65%, rgba(0, 43, 169, 0.00) 100%);
      border-radius: 2px !important;

      .svg-icon {
        width: 1em;
        height: 1em;
        vertical-align: -0.15em;
        fill: #FFFFFF;
        overflow: hidden;
      }
    }


    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    & .theme-dark .is-active>.el-submenu__title {
      color: $base-menu-color-active !important;
      // background-image: linear-gradient(90deg, #0d7154 0%, #0d7154a3 100%) !important;
      // box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.1) !important;
      border-radius: 2px !important;

      opacity: 0.96;
      background-image: linear-gradient(90deg, rgba(0, 43, 169, 0.00) 0%, #002BA9 35%, #002BA9 65%, rgba(0, 43, 169, 0.00) 100%);

      i {
        color: #FFFFFF;
      }

      .svg-icon {
        width: 1em;
        height: 1em;
        vertical-align: -0.15em;
        fill: #FFFFFF;
        overflow: hidden;
      }
    }

    & .nest-menu .el-submenu>.el-submenu__title,
    & .el-submenu .el-menu-item {
      // min-width: $base-sidebar-width !important;

      &:hover {
        // background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    & .theme-dark .nest-menu .el-submenu>.el-submenu__title,
    & .theme-dark .el-submenu .el-menu-item {

      &.is-active {
        font-weight: 700;

        // .svg-icon {
        //   width: 1em;
        //   height: 1em;
        //   vertical-align: -0.15em;
        //   fill:  #FFFFFF;
        //   overflow: hidden;
        // }
      }

      // font-weight: 700;
      // background-color: $base-sub-menu-background !important;

      &:hover {
        // background-color: $base-sub-menu-hover !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 64px !important;
    }

    .main-container {
      margin-left: 64px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $base-sidebar-width !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $base-sidebar-width !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$base-sidebar-width, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-submenu>.el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      // background-color: rgba(0, 0, 0, 0.06) !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

.mr10 {
  margin-right: 10px;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.el-menu-item:hover {
  background-color: transparent !important;
  opacity: 0.96 !important;
  ;
  background-image: linear-gradient(90deg, rgba(0, 43, 169, 0.00) 0%, #002BA9 35%, #002BA9 65%, rgba(0, 43, 169, 0.00) 100%) !important;
}

.el-form-item__label {
  font-family: SourceHanSansSC-Regular;
  font-size: 16px !important;
  ;
  color: #FFFFFF !important;
  ;
  letter-spacing: 0;
  text-align: right;
  font-weight: 400;
}

.el-form .el-input__inner {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  height: 50px !important;
  line-height: 50px !important;
  color: #ffffff !important;
}
.el-form .el-input__inner,
.el-form .el-input.is-disabled .el-input__inner {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  height: 50px !important;
  line-height: 50px !important;
  color: #ffffff !important;
}


.el-form .el-form-item__label {
  height: 50px !important;
  line-height: 50px !important;
}

.search_form {
    // height: 160px;
  background: rgba(27, 126, 242, 0.10) !important;
  margin-top: 20px !important;
  margin-bottom: 20px !important;
  padding: 20px 30px;
}

// .el-form {
//   // height: 160px;
//   background: rgba(27, 126, 242, 0.10) !important;
//   margin-top: 20px !important;
//   margin-bottom: 20px !important;
//   padding: 20px 30px;
// }

.el-form-item {
  margin-right: 20px !important;
}

.el-date-editor .el-range-input {
  background-color: transparent !important;
  color: #ffffff !important;
}


.public_button {
  box-shadow: inset 0px 0px 12px 0px rgba(204, 237, 247, 0.4);
  border-radius: 10px;
  padding: 7px 20px;
  font-family: SourceHanSansSC-Regular;
  font-size: 16px;
  color: #ffffff;
  letter-spacing: 0;
  font-weight: 400;
  cursor: pointer;
}

.search_btn {
  background-image: linear-gradient(180deg, #78b8ff 0%, #002ba9 100%);
  border: 1px solid rgba(17, 80, 154, 1);
}

.clear_button {
  background-image: linear-gradient(180deg, #78B8FF 0%, #0075AF 100%);
  border-radius: 10px;
}

.export_button {
  background-image: linear-gradient(180deg, #34D399 0%, #10B981 100%);
  border-radius: 10px;
}

.delete_button {
  background-image: linear-gradient(180deg, #F87171 0%, #EF4444 100%);
  border-radius: 10px;
}

// 公共table
.el-table {
  background: rgba(27, 126, 242, 0.10) !important;
}

.el-table tr {
  background: rgba(27, 126, 242, 0.10) !important;
}

.el-table th.el-table__cell.is-leaf,
.el-table td.el-table__cell {
  border: transparent !important;
}

.el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  background: rgba(27, 126, 242, 0.10) !important;
}

.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell {
  background-color: rgba(27, 126, 242, 0.10) !important;
}

.el-table .cell {
  font-family: SourceHanSansSC-Regular !important;
  font-size: 14px !important;
  color: #FFFFFF !important;
  letter-spacing: 0;
  font-weight: 400;
}

.el-checkbox__inner {
  border: 1px solid #5F89BB !important;
  background-color: transparent !important;
}

.el-table th.el-table__cell>.cell {
  font-size: 16px !important;
}

.el-table::before {
  left: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 0 !important;
  z-index: 3 !important;
}



.el-tag.el-tag--success {
  background-color: #e7faf071 !important;
  border-color: #e7faf071 !important;
  color: #13ce66 !important;
}

.el-tag.el-tag--info {
  background-color: #f4f4f50e!important;
    border-color: #f4f4f517!important;
    color: #909399!important;
}
.el-tag {
  background-color: #e8f4ff5e!important;
    border-color: #e8f4ff5e!important;
    color: #ffffff!important;
}

.el-tag.el-tag--warning {
  background-color: #fff8e677!important;
  border-color: #fff8e677!important;
  color: #ffba00!important;
}

.el-tag.el-tag--danger {
  background-color: #ffeded5e!important;
  border-color: #ffeded5e!important;
  color: #ff4949!important;
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.el-pagination .el-select .el-input .el-input__inner,
.el-pagination__editor.el-input .el-input__inner {
  background-color: transparent!important;
  border: 1px solid rgba(27, 126, 242, 0.20)!important;
  color: #ffffff!important;
}

.el-pagination.is-background .btn-prev, .el-pagination.is-background .btn-next, .el-pagination.is-background .el-pager li {
  background-color: rgba(27, 126, 242, 0.20)!important;
    color: #ffffff!important;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background-image: linear-gradient(180deg, #78B8FF 0%, #002BA9 100%)!important;
border: 1px solid rgba(17,80,154,1)!important;
// border-radius: 4px!important;
}

.el-pagination__total,.el-pagination__jump {
  color: #ffffff!important;
}

.v-modal {
  background: rgba(0, 0, 0, 0.70)!important;
  opacity: 1 !important;
}

.dialog_form_style {
  height: 50vh;
    overflow-y: overlay;
}

.el-dialog:not(.is-fullscreen) {
  background: #0D235B!important;
  border-radius: 5px;
}

.el-dialog__title {
  color: #FFFFFF !important;
}

.el-table .fixed-width .el-button--mini {
  // font-family: SourceHanSansSC-Bold;
// font-size: 14px;
color: #66CCFF !important;
letter-spacing: 0;
text-align: center;
// font-weight: 700;
}


.el-descriptions-item__label.is-bordered-label {
  text-align: center;
  background-color: transparent !important;
  color: #ffffff !important;
}

.el-descriptions__body {
  background: rgba(1, 35, 102, 0.70) !important;
  border: 1px solid rgba(17, 80, 154, 1) !important;
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4) !important;
  // border-radius: 10px !important;
}

.el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 1px solid #e6ebf51a !important;
  text-align: center;
}

.el-descriptions__body {
  color: #ffffff !important;
}


/* 标签页样式-el-tabs 组件 */
.el-tabs {
  background-color: transparent;
}

.el-tabs__nav {
  border-bottom: none !important;
}

.el-tabs__item {
  background-color: transparent !important;
  color: #fff;
  padding: 12px 20px;
  margin-right: 5px;
  border-radius: 4px 4px 0 0;
  font-size: 14px;
  transition: all 0.3s;
}

.el-tabs__item.is-active {
  color: #66CCFF !important;
  background-image: linear-gradient(179deg, rgba(27, 126, 242, 0.00) 0%, rgba(27, 126, 242, 0.20) 100%) !important;
  position: relative;
}

.el-tabs__item.is-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: #66CCFF !important;
}

.el-tabs__content {
  background-color: transparent;
  padding: 15px 0;
}

.el-tabs__item:hover,
.el-tabs__item {
  color: #A9C7EA !important;
}

.el-tabs__nav-wrap::after {
  height: 0 !important;
}

.el-tabs--bottom .el-tabs__item.is-bottom:last-child,
.el-tabs--bottom .el-tabs__item.is-top:last-child,
.el-tabs--top .el-tabs__item.is-bottom:last-child,
.el-tabs--top .el-tabs__item.is-top:last-child,
.el-tabs--bottom .el-tabs__item.is-bottom:nth-child(2),
.el-tabs--bottom .el-tabs__item.is-top:nth-child(2),
.el-tabs--top .el-tabs__item.is-bottom:nth-child(2),
.el-tabs--top .el-tabs__item.is-top:nth-child(2) {
  padding: 0 20px !important;
}


.el-radio__input.is-checked .el-radio__inner {
  border-color: #66CCFF !important;
  background: #51B1FC !important;
}

.notices {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #FFFFFF;
  letter-spacing: 0;
  font-weight: 400;
  padding: 5px 15px;
}

.el-textarea__inner {
  background: rgba(1, 35, 102, 0.70);
  border: 1px solid rgba(17, 80, 154, 1);
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4);
  border-radius: 10px;
}

.splitpanes.default-theme .splitpanes__splitter {
  background-color: #ffffff47 !important;
}

.default-theme.splitpanes--vertical>.splitpanes__splitter,
.default-theme .splitpanes--vertical>.splitpanes__splitter {
  border-left: 1px solid #ffffff47 !important;
}

.position_btn {
  margin-right: 10px;
}

.position_add_btn {
  margin-right: 30px;
  margin-top: 32px;
}


// el-tree 组件样式
.el-tree {
  background-color: transparent !important;
  color: #ffffff !important;
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content,
.el-tree-node:focus>.el-tree-node__content,
.el-tree-node__content:hover {
  background-color: transparent !important;
}

.el-tree-node__content {
  height: 35px !important;
}

.el-input.is-disabled .el-input__inner {
  background-color: #3d4e79 !important;
  border-color: #0A2159 !important;
}

.el-input-group__append,
.el-input-group__prepend {
  background: rgba(27, 126, 242, 0.20) !important;
  border: 0 !important;
  border-radius: 10px;
  color: #fff !important;
}

.vue-treeselect__control {
  background: rgba(1, 35, 102, 0.70) !important;
  border: 1px solid rgba(17, 80, 154, 1) !important;
  box-shadow: inset 0px 0px 12px 0px rgba(13, 187, 236, 0.4) !important;
  border-radius: 10px !important;
  height: 50px !important;
  line-height: 50px !important;
  color: #fff !important;
}

.vue-treeselect__placeholder,
.vue-treeselect__single-value {
  height: 50px !important;
  line-height: 50px !important;
}

.el-input-number__increase,
.el-input-number__decrease {
  background: transparent !important;
  border: 0 !important;
  // box-shadow: inset 0px 0px 12px 0px rgba(13,187,236,0.4);
  // border-radius: 10px;
  height: 50px !important;
  line-height: 50px !important;
  color: #fff !important;
}

.el-form-item--medium .el-form-item__content {
  line-height: 50px !important;
  // color: #fff !important;
}

.el-textarea__inner {
  color: #fff !important;
}

.vue-treeselect--has-value .vue-treeselect__input,
.vue-treeselect__single-value {
  color: #fff !important;
}