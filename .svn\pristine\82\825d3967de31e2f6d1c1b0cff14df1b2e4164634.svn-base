import request from '@/utils/request'

// 查询文件导入任务列表
export function getFileImportList(query) {
  return request({
    url: '/dataaccess/fileimport/list',
    method: 'get',
    params: query
  })
}

// 获取文件导入任务详情
export function getFileImportDetail(id) {
  return request({
    url: `/dataaccess/fileimport/${id}`,
    method: 'get'
  })
}

// 新增文件导入任务
export function addFileImport(data) {
  return request({
    url: '/dataaccess/fileimport',
    method: 'post',
    data: data
  })
}

// 修改文件导入任务
export function updateFileImport(data) {
  return request({
    url: '/dataaccess/fileimport',
    method: 'put',
    data: data
  })
}

// 删除文件导入任务
export function deleteFileImport(ids) {
  return request({
    url: `/dataaccess/fileimport/${ids}`,
    method: 'delete'
  })
}

// 下载模板
export function downloadTemplate(category) {
  return request({
    url: `/dataaccess/fileimport/download/template`,
    method: 'get',
    params: { category },
    responseType: 'blob'
  })
}

// 终止运行任务
export function stopFileImportTask(id) {
  return request({
    url: `/dataaccess/fileimport/stop/${id}`,
    method: 'put'
  })
}

// 重新运行任务
export function rerunFileImportTask(id) {
  return request({
    url: `/dataaccess/fileimport/rerun/${id}`,
    method: 'put'
  })
}

// 获取任务运行日志
export function getFileImportRunLog(id) {
  return request({
    url: `/dataaccess/fileimport/runlog/${id}`,
    method: 'get'
  })
}

// 获取任务文件列表
export function getFileImportFileList(taskId) {
  return request({
    url: `/dataaccess/fileimport/files/${taskId}`,
    method: 'get'
  })
}

// 下载任务文件
export function downloadFileImportFile(fileId) {
  return request({
    url: `/dataaccess/fileimport/file/download/${fileId}`,
    method: 'get',
    responseType: 'blob'
  })
}

// 删除任务文件
export function deleteFileImportFile(fileId) {
  return request({
    url: `/dataaccess/fileimport/file/${fileId}`,
    method: 'delete'
  })
}
