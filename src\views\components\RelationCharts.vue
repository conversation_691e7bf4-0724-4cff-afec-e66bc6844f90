<template>
  <div ref="chartContainer" style="width: 100%; height: 100%;"></div>
</template>

<script>
import * as echarts from 'echarts';

// 定义默认的节点图形路径 (一个圆角矩形)
const DEFAULT_NODE_SYMBOL =
  'path://M19.300,3.300 L253.300,3.300 C262.136,3.300 269.300,10.463 269.300,19.300 L269.300,21.300 C269.300,30.137 262.136,37.300 253.300,37.300 L19.300,37.300 C10.463,37.300 3.300,30.137 3.300,21.300 L3.300,19.300 C3.300,10.463 10.463,3.300 19.300,3.300 Z';

export default {
  name: 'ForceGraph',
  props: {
    /**
     * @description 图的数据源
     * @type {Object}
     * @property {Array<Object>} nodes - 节点列表
     * @property {string} nodes[].id - 节点唯一ID (必填)
     * @property {string} nodes[].name - 节点显示名称 (必填)
     * @property {string} nodes[].category - 节点所属类别名称 (必填)
     * @property {string} [nodes[].symbol] - 节点图形路径
     * @property {Array<number>} [nodes[].symbolSize] - 节点大小 [width, height]
     * @property {Object} [nodes[].itemStyle] - 节点样式
     * @property {boolean} [nodes[].draggable=true] - 是否可拖拽
     * @property {Object} [nodes[].data] - 节点额外数据
     * @property {Array<Object>} links - 边列表
     * @property {string} links[].source - 源节点ID (必填)
     * @property {string} links[].target - 目标节点ID (必填)
     * @property {string} links[].category - 边的类别名称 (必填)
     * @property {Object} [links[].lineStyle] - 边的样式
     * @property {Object} [links[].data] - 边额外数据
     * @property {Array<Object>} categories - 类别列表 (必填)
     * @property {string} categories[].name - 类别名称 (必填)
     * @property {string} [categories[].color] - 该类别的颜色
     */
    graphData: {
      type: Object,
      required: true,
      validator: (value) => {
        if (!value || !value.nodes || !value.links || !value.categories) {
          console.error('graphData 必须包含 nodes, links 和 categories');
          return false;
        }
        if (!value.nodes.every((n) => n.id && n.name && n.category)) {
          console.error('每个 node 必须包含 id, name 和 category');
          return false;
        }
        if (!value.links.every((l) => l.source && l.target && l.category)) {
          console.error('每个 link 必须包含 source, target 和 category');
          return false;
        }
        if (!value.categories.every((c) => c.name)) {
          console.error('每个 category 必须包含 name');
          return false;
        }
        return true;
      },
    },

    /**
     * @description ECharts 配置项，会与默认配置合并
     * @type {Object}
     */
    option: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      myChart: null,
      chartOption: null,
    };
  },
  watch: {
    graphData: {
      handler() {
        this.updateChart();
      },
      deep: true,
    },
    option: {
      handler() {
        this.updateChart();
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart();
    this.updateChart();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      this.myChart = echarts.init(this.$refs.chartContainer);
    },

    updateChart() {
      if (!this.myChart) return;
      this.chartOption = this.generateOption();
      this.myChart.setOption(this.chartOption, true);
    },

    generateOption() {
      const { nodes, links, categories } = this.graphData;

      // 创建类别名称到索引的映射
      const categoryNameToIndexMap = new Map(
        categories.map((cat, index) => [cat.name, index])
      );

      // 处理节点，将 category 名称转换为索引
      const processedNodes = nodes.map((node) => ({
        ...node,
        category: categoryNameToIndexMap.get(node.category),
        symbol: node.symbol || DEFAULT_NODE_SYMBOL,
        draggable: node.draggable !== false, // 默认 true
      }));

      // 处理边，将 category 名称转换为索引
      const processedLinks = links.map((link) => ({
        ...link,
        category: categoryNameToIndexMap.get(link.category),
      }));

      // 提取类别颜色
      const categoryColors = categories.map((cat) => cat.color);

      const defaultOption = {
        color: categoryColors.length > 0 ? categoryColors : [
          '#c4895c', '#6987b8', '#f9a5c7', '#f2a498', '#91c7ae', '#fcd684'
        ],
        tooltip: {
          formatter: function (param) {
            if (param.dataType === 'edge') {
              const categoryName = categories[param.data.category].name;
              return `${categoryName}: ${param.data.targetName}`;
            }
            const categoryName = categories[param.data.category].name;
            return `${categoryName}: ${param.data.name}`;
          },
        },
        // legend: [
        //   {
        //     data: categories.map((cat) => cat.name),
        //   },
        // ],
        series: [
          {
            type: 'graph',
            layout: 'force',
            force: {
              repulsion: 1000,
              edgeLength: [150, 300],
            },
            roam: true,
            focusNodeAdjacency: true,
            symbol: DEFAULT_NODE_SYMBOL,
            symbolSize: [265, 50],
            label: {
              normal: {
                show: true,
                position: 'inside',
                textStyle: {
                  fontSize: 15,
                  color: '#fff', // 文字颜色为白色，与深色背景形成对比
                  align: 'center',
                  baseline: 'middle',
                  rich: {
                    line: {
                      height: 20, // 每行的高度
                    },
                  },
                },
                // 自定义标签格式化，支持换行符
                formatter: function (params) {
                  return params.data.name.split('\n').map(line => `{line|${line}}`).join('\n');
                }
              },
            },
            edgeLabel: {
              normal: {
                show: true,
                formatter: function (param) {
                  return categories[param.data.category].name;
                },
                textStyle: {
                  fontSize: 12,
                  color: '#fff', // 文字颜色为白色，与深色背景形成对比
                },
              },
            },
            // lineStyle: {
            //   normal: {
            //     opacity: 0.8,
            //     width: 2,
            //     curveness: 0.2,
            //     // 边的颜色跟随其category
            //     color: function (params) {
            //       return categoryColors[params.data.category] || params.color;
            //     },
            //   },
            // },
            data: processedNodes,
            links: processedLinks,
            categories: categories,
          },
        ],
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
      };

      // 使用用户传入的option覆盖默认配置
      return this.deepMerge(defaultOption, this.option);
    },

    /**
     * @description 深度合并两个对象
     */
    deepMerge(target, source) {
      if (typeof target !== 'object' || target === null || typeof source !== 'object' || source === null) {
        return source;
      }
      const merged = Array.isArray(target) ? [...target] : { ...target };
      Object.keys(source).forEach(key => {
        if (Array.isArray(source[key]) && Array.isArray(target[key])) {
          merged[key] = [...target[key], ...source[key]];
        } else if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          merged[key] = this.deepMerge(target[key], source[key]);
        } else {
          merged[key] = source[key];
        }
      });
      return merged;
    },

    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },

    /**
     * @description 获取ECharts实例，便于外部调用
     * @returns {Object|null} ECharts实例
     */
    getEchartsInstance() {
      return this.myChart;
    },
  },
};
</script>

<style scoped>
/* 组件内部样式 */
</style>