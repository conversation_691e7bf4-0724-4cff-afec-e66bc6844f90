<template>
  <div class="demo-container">
    <h1 class="demo-title">Cool World Map Component</h1>
    
    <!-- 示例1: 基础地图 -->
    <div class="demo-section">
      <h2 class="section-title">Basic Map</h2>
      <CoolWorldMap 
        height="500px"
        backgroundColor="#0a0f1e"
      />
    </div>
    
    <!-- 示例2: 带数据点的地图 -->
    <div class="demo-section">
      <h2 class="section-title">Map with Data Points</h2>
      <CoolWorldMap 
        height="500px"
        backgroundColor="#0a0f1e"
        :data="cityData"
        :enableRipple="true"
        :showLegend="true"
        :legendData="legendItems"
        @map-click="handleMapClick"
      />
    </div>
    
    <!-- 示例3: 自定义样式 -->
    <div class="demo-section">
      <h2 class="section-title">Custom Style Map</h2>
      <CoolWorldMap 
        height="500px"
        backgroundColor="#1a1a2e"
        :mapStyle="customMapStyle"
        :emphasisStyle="customEmphasisStyle"
        :scatterStyle="customScatterStyle"
        :data="customData"
      />
    </div>
    
    <!-- 示例4: 带视觉映射 -->
    <div class="demo-section">
      <h2 class="section-title">Map with Visual Mapping</h2>
      <CoolWorldMap 
        height="500px"
        backgroundColor="#0a0f1e"
        :visualMap="visualMapConfig"
        :data="heatmapData"
        :enableRipple="false"
      />
    </div>
  </div>
</template>

<script>
import CoolWorldMap from './CoolWorldMap.vue'

export default {
  name: 'WorldMapDemo',
  components: {
    CoolWorldMap
  },
  data() {
    return {
      // 城市数据
      cityData: [
        { name: 'New York', value: [-74.006, 40.7128, 20], tooltip: '<strong>New York</strong><br/>Population: 8.3M' },
        { name: 'London', value: [-0.1278, 51.5074, 18], tooltip: '<strong>London</strong><br/>Population: 9.0M' },
        { name: 'Tokyo', value: [139.6917, 35.6895, 25], tooltip: '<strong>Tokyo</strong><br/>Population: 13.9M' },
        { name: 'Paris', value: [2.3522, 48.8566, 16], tooltip: '<strong>Paris</strong><br/>Population: 2.1M' },
        { name: 'Sydney', value: [151.2093, -33.8688, 15], tooltip: '<strong>Sydney</strong><br/>Population: 5.3M' },
        { name: 'Beijing', value: [116.4074, 39.9042, 22], tooltip: '<strong>Beijing</strong><br/>Population: 21.5M' },
        { name: 'Dubai', value: [55.2708, 25.2048, 14], tooltip: '<strong>Dubai</strong><br/>Population: 3.3M' },
        { name: 'Singapore', value: [103.8198, 1.3521, 17], tooltip: '<strong>Singapore</strong><br/>Population: 5.7M' }
      ],
      
      // 图例数据
      legendItems: [
        { label: 'Major Cities', color: '#00ffff' },
        { label: 'Population > 10M', color: '#00d9ff' }
      ],
      
      // 自定义地图样式
      customMapStyle: {
        areaColor: '#16213e',
        borderColor: '#e94560',
        borderWidth: 1.5,
        shadowBlur: 20,
        shadowColor: 'rgba(233, 69, 96, 0.6)'
      },
      
      customEmphasisStyle: {
        areaColor: '#1f4068',
        borderColor: '#ff6b9d',
        borderWidth: 2,
        shadowBlur: 25,
        shadowColor: 'rgba(255, 107, 157, 0.8)'
      },
      
      customScatterStyle: {
        symbolSize: 15,
        color: '#ff6b9d',
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowBlur: 12,
        shadowColor: 'rgba(255, 107, 157, 0.9)'
      },
      
      // 自定义数据
      customData: [
        { name: 'Los Angeles', value: [-118.2437, 34.0522, 18] },
        { name: 'Mumbai', value: [72.8777, 19.0760, 20] },
        { name: 'São Paulo', value: [-46.6333, -23.5505, 19] },
        { name: 'Moscow', value: [37.6173, 55.7558, 17] }
      ],
      
      // 视觉映射配置
      visualMapConfig: {
        min: 10,
        max: 30,
        text: ['High', 'Low'],
        calculable: true,
        inRange: {
          color: ['#0a2f51', '#0e4d92', '#2e8cca', '#00d9ff', '#00ffff']
        }
      },
      
      // 热力图数据
      heatmapData: [
        { name: 'USA', value: [-95.7129, 37.0902, 28] },
        { name: 'China', value: [104.1954, 35.8617, 30] },
        { name: 'India', value: [78.9629, 20.5937, 25] },
        { name: 'Brazil', value: [-47.8825, -15.7942, 20] },
        { name: 'Russia', value: [105.3188, 61.5240, 18] },
        { name: 'Japan', value: [138.2529, 36.2048, 22] },
        { name: 'Germany', value: [10.4515, 51.1657, 16] },
        { name: 'UK', value: [-3.4360, 55.3781, 15] },
        { name: 'France', value: [2.2137, 46.2276, 14] },
        { name: 'Italy', value: [12.5674, 41.8719, 13] }
      ]
    }
  },
  methods: {
    handleMapClick(params) {
      console.log('Map clicked:', params)
      alert(`You clicked: ${params.name}`)
    }
  }
}
</script>

<style scoped>
.demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0a0f1e 0%, #1a1f3a 100%);
  padding: 40px 20px;
}

.demo-title {
  text-align: center;
  font-size: 48px;
  font-weight: 300;
  color: #00ffff;
  margin-bottom: 60px;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  letter-spacing: 2px;
}

.demo-section {
  max-width: 1400px;
  margin: 0 auto 60px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 30px;
  border: 1px solid rgba(0, 217, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 217, 255, 0.1);
  backdrop-filter: blur(10px);
}

.section-title {
  font-size: 24px;
  font-weight: 400;
  color: #00d9ff;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(0, 217, 255, 0.3);
  letter-spacing: 1px;
}
</style>
