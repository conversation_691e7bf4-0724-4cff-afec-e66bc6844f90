<template>
  <div class="analysis-comparison">
    <!-- 搜索条件区域 -->
    <div class="filter-section">
      <template v-for="(filter, index) in filterConfig">
        <!-- 输入框 -->
        <div class="labed-input">{{ filter.placeholder }}</div>
        <el-input
          v-if="filter.type === 'input'"
          :key="filter.id || index"
          v-model="filterValues[filter.key]"
          :placeholder="filter.placeholder"
          :class="filter.className"
          style="margin-right: 10px"
        ></el-input>

        <!-- 下拉框 -->
        <el-select
          v-if="filter.type === 'select'"
          :key="filter.id || index"
          v-model="filterValues[filter.key]"
          :placeholder="filter.placeholder"
          :class="filter.className"
          style="margin-right: 10px"
        >
          <el-option
            v-for="option in filter.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          ></el-option>
        </el-select>
      </template>
      <el-button
        v-if="filterConfig.length > 0"
        type="primary"
        @click="handleSearch"
        >搜索</el-button
      >
    </div>

    <!-- 对比类型选择 -->
    <div class="comparison-type" v-if="showComparisonType">
      <el-radio-group v-model="comparisonType" @change="handleRadio">
        <el-radio
          v-for="type in comparisonTypes"
          :key="type.value"
          :label="type.value"
          >{{ type.label }}</el-radio
        >
      </el-radio-group>
    </div>

    <div class="content-wrapper">
      <splitpanes
        :horizontal="this.$store.getters.device === 'mobile'"
        class="default-theme"
        v-if="treedisabled"
        @resize="handleResize"
      >
        <!-- 左侧列表 -->
        <pane size="16"
          ><div class="department-list">
            <el-tree
              :data="deptConfig.data"
              :props="deptConfig.props"
              :filter-node-method="filterNode"
              ref="tree"
              node-key="id"
              default-expand-all
              highlight-current
              @node-click="handleNodeClick"
              class="treeh"
            >
              <template #default="{ node, data }">
                <span class="custom-tree-node">
                  <div v-if="isFirstRootNode(node)">
                    <span class="prefix-img eltree-text"
                      ><img src="./img/tree1.png" alt="" />
                      <span style="margin-left: 10px">{{ node.label }}</span>
                    </span>
                  </div>
                  <span v-else-if="!isFirstRootNode(node)" class="eltree-con">
                    <div style="display: inline-block">
                      <img
                        src="./img/lx.png"
                        alt=""
                        style="width: 8px; height: 8px; margin-right: 10px"
                      />
                    </div>
                    {{ node.label }}</span
                  >
                  <!-- <span v-else-if="!isLastLevelNode(node)" class="eltree-con">{{
                node.label
              }}</span> -->
                </span>
              </template>
            </el-tree>
          </div></pane
        >

        <!-- <div class="department-header">
          <i :class="orgIcon"></i>
          {{ orgName }}
        </div>
        <el-menu
          :default-active="activeStaff"
          class="staff-list"
          @select="handleStaffSelect"
        >
          <el-menu-item v-for="staff in staffList" :key="staff.id" :index="staff.id">
            <span>{{ staff.name }}</span>
            <span class="department">({{ staff.department }})</span>
          </el-menu-item>
        </el-menu> -->
        <!-- 右侧内容 -->
        <pane size="84"
          ><div class="chart-section">
            <div class="radar-chart" ref="radarChart"></div>
            <el-table
              :data="tableData"
              border
              style="width: 90%; margin: 0 auto; margin-top: 20px"
              class="custom-table"
            >
              <el-table-column
                v-for="col in tableColumns"
                :key="col.prop"
                :prop="col.prop"
                :label="col.label"
                align="center"
              ></el-table-column>
            </el-table></div
        ></pane>
      </splitpanes>
      <div class="chart-section" v-else>
        <div class="radar-chart" ref="radarChart"></div>
        <el-table
          :data="tableData"
          border
          style="width: 90%; margin: 0 auto;"
          class="custom-table"
        >
          <el-table-column
            v-for="col in tableColumns"
            :key="col.prop"
            :prop="col.prop"
            :label="col.label"
            align="center"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { debounce } from "lodash"; // 或者自己实现一个简单的防抖函数
import { deptTreeSelect } from "@/api/system/user";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";
export default {
  name: "AnalysisComparison",
  props: {
    // 搜索条件配置
    filterConfig: {
      type: Array,
      default: () => [],
    },
    // 是否显示对比类型选择
    showComparisonType: {
      type: Boolean,
      default: true,
    },
    // 是否显示tree
    treedisabled: {
      type: Boolean,
      default: true,
    },
    // 对比类型选项
    comparisonTypes: {
      type: Array,
      default: () => [],
    },
    // 表格数据
    tableData: {
      type: Array,
      default: () => [],
    },
    // 组织机构名称
    // orgName: {
    //   type: String,
    //   required: true
    // },
    // 组织机构图标
    // orgIcon: {
    //   type: String,
    //   default: 'el-icon-office-building'
    // },
    // 人员列表
    // staffList: {
    //   type: Array,
    //   required: true
    // },
    // 表格列配置
    tableColumns: {
      type: Array,
      required: true,
    },
    // 图表指标配置
    chartIndicators: {
      type: Array,
      required: true,
    },
    //tree数据
    deptConfig: {
      type: Object,
      required: true,
    },
  },
  components: { Splitpanes, Pane },
  data() {
    return {
      deptOptions: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      filterValues: {},
      comparisonType: 1,
      activeStaff: "",
      // tableData: [],
      chart: null,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      this.handleResize(); // 调整图表大小
    });
    this.initFilterValues();
    // this.initChart();
  },
  methods: {
    // renderContent(h, { node, data, store }) {
    //   // 给第一级节点添加背景色
    //   if (node.level === 1) {
    //     return (
    //       <div style="background-color:  #EBF0F8; padding: 4px 10px; display: inline-block;width: 100%;">
    //         <img src="/img/tree1.png" style="margin-right: 5px;"/>
    //         <span>{node.label}</span>
    //       </div>
    //     );
    //   } else {
    //     return (
    //       <span>
    //         <span class="el-icon-document" style="margin-right: 5px;"></span>{node.label}
    //       </span>
    //     );
    //   }
    // },
    // 判断是否是第一个根节点
    isFirstRootNode(node) {
      // 假设 deptOptions 是根节点的数组
      // 检查 node 是否是 deptOptions 中的第一个元素
      return node.level === 1;
    },
    // 判断节点是否为最后一级
    isLastLevelNode(node) {
      return !node.childNodes || node.childNodes.length === 0;
    },
    // 判断节点是否为最后一级
    isLastLevelNode1(node) {
      return !node.children || node.children.length === 0;
    },
    handleNodeClick(data) {
      if (this.isLastLevelNode1(data)) {
        // 只有在最后一级节点时才执行操作
        // console.log('点击了最后一级节点:', node);
        // 这里可以添加其他逻辑
        this.$emit("treeclick", data);
      }
    },
    handleRadio(val) {
      let type = {};
      this.comparisonTypes.map((item) => {
        if (item.value === val) {
          type = item;
        }
      });
      this.$emit("radiochange", type);

      this.updateChart(this.tableData);
    },
    updateChart(type) {
      const personalScores = type.map((item) => item[Object.keys(item)[1]]);
      const averageScore = type.map((item) => item[Object.keys(item)[2]]);
      // 根据 type 更新 ECharts 图表
      if (this.chart) {
        // 更新图表的配置
        this.chart.setOption({
          // 新的配置
          series: [
            {
              name: "得分对比",
              type: "radar",
              data: [
                {
                  value: personalScores,
                  name: "本人得分",
                  lineStyle: { color: "#409EFF" },
                  itemStyle: { color: "#409EFF" },
                },
                {
                  value: averageScore,
                  name: "平均分",
                  lineStyle: { color: "#67C23A" },
                  itemStyle: { color: "#67C23A" },
                },
              ],
            },
          ],
        });
      }
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then((response) => {
        this.deptOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    initFilterValues() {
      this.filterConfig.forEach((filter) => {
        this.$set(this.filterValues, filter.key, "");
      });
    },
    handleResize: debounce(function () {
      if (this.chart) {
        this.chart.resize();
      }
    }, 200), // 200ms 的防抖时间
    initChart() {
      const personalScores = this.tableData.map(
        (item) => item[Object.keys(item)[1]]
      );
      const averageScore = this.tableData.map(
        (item) => item[Object.keys(item)[2]]
      );

      this.chart = echarts.init(this.$refs.radarChart);
      const option = {
        legend: {
          // x : 'right',
          right: 300,
          y: "center",
          orient: "hideOverlap",
          itemHeight: 10,
          itemWidth: 14,
          data: ["本人得分", "平均分"],
        },
        radar: {
          // itemWidth: 30,
          axisLine: {
            show: false // 不显示对照线条
        },
          indicator: this.chartIndicators,
          shape: "polygon",
          splitNumber: 4,
          axisName: {
            color: "#fff",
            backgroundColor: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "#1d6fff", // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: "#47a7ff", // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
            borderRadius: 3,
            padding: [6, 10, 5, 10],
          },
        },
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            name: "得分对比",
            type: "radar",
            data: [
              {
                value: personalScores,
                name: "本人得分",
                lineStyle: { color: "#409EFF" },
                itemStyle: { color: "#409EFF" },
              },
              {
                value: averageScore,
                name: "平均分",
                lineStyle: { color: "#67C23A" 
                },
                itemStyle: { color: "#67C23A" },
               
              },
            ],
          },
        ],
      };
      this.chart.setOption(option);
    },
    handleSearch() {
      this.$emit("search", this.filterValues);
    },
    handleStaffSelect(index) {
      this.activeStaff = index;
      this.$emit("staff-select", index);
    },
  },
  created() {
    this.getDeptTree();
  },
  watch: {
    tableData: {
      immediate: true,
      handler(newValue) {
        // 当 tableData 变化时，更新 ECharts
        this.updateChart(newValue);
      },
    },
  },
};
</script>

<style scoped>
.analysis-comparison {
  padding: 20px;
}

.filter-section {
  display: flex;
  margin-bottom: 16px;
}

.comparison-type {
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 2px solid #c1ccd9;
}

.content-wrapper {
  display: flex;
  gap: 20px;
}

.department-list {
  /* width: 256px; */
  padding: 10px 20px;
  /* background: #f5f7fa; */
  border-radius: 4px;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.06);
}

.department-header {
  padding: 15px;
  background: #e6f1fc;
  color: #409eff;
  font-weight: bold;
}

.staff-list {
  border-right: none;
}

.staff-list .el-menu-item {
  display: flex;
  justify-content: space-between;
  height: 40px;
  line-height: 40px;
}

.department {
  color: #909399;
  font-size: 12px;
}

.chart-section {
  background: url(./img/backimg.png) no-repeat;
  background-size: 100% 100%;
  flex: 1;
}

.radar-chart {
  height: 380px;
  width: 100%;
}

.el-radio-group {
  display: flex;
  gap: 10px;
}

.el-table {
  margin-top: 20px;
}

.el-table th {
  background-color: #f5f7fa;
}
.el-input {
  width: 180px;
  height: 32px;
}
.el-select {
  width: 180px;
  height: 32px;
}
.search-btn {
  width: 80px;
  height: 32px;
  background: #1b5dd8;
  border-radius: 16px;
  line-height: 0px;
  margin-left: 20px;
}
.labed-input {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  font-weight: 400;
  line-height: 36px;
  margin-right: 10px;
}
/deep/ .el-radio__label {
  font-family: SourceHanSansSC-Bold;
  font-size: 16px;
  font-weight: 600;
}
/deep/ .el-radio__input.is-checked + .el-radio__label {
  font-family: SourceHanSansSC-Bold;
  font-size: 16px;
  color: #1b5dd8;
  font-weight: 600;
}
/deep/ .el-radio__input.is-checked .el-radio__inner {
  width: 16px;
  height: 16px;
  background: #ffffff;
  border: 1px solid rgba(33, 83, 200, 1);
  border-radius: 8px;
}
/deep/ .el-radio__input.is-checked .el-radio__inner::after {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(33, 83, 200, 1);
}
/deep/ label.el-radio.is-checked::after {
  content: "";
  position: absolute;
  left: 50%;
  top: 150%;
  transform: translate(-50%, -50%);
  width: 64px;
  height: 2px;
  border-radius: 50%;
  background: #1b5dd8;
}
/deep/ .el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th {
  background-color: transparent;
}
/deep/ .custom-table .el-table__body tr td:first-child,
/deep/ .custom-table .el-table__header tr th:first-child {
  background-color: #f6f8fe; /* 修改为你想要的颜色 */
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #203f78;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

/* 隐藏Element UI el-tree的向下剪头 */
/deep/ .el-tree .el-tree-node__expand-icon.el-icon-caret-right {
  display: none;
}
.custom-tree-node {
  display: flex;
  align-items: center;
}
.prefix-text {
  /* padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px; */
  font-family: SourceHanSansSC-Regular;
  padding: 2.5px 2px;
  margin-right: 7px;
  font-size: 8px;
  color: #09b72b;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
  background: #f3fff0;
  border: 1px solid rgba(9, 183, 43, 1);
  border-radius: 2px;
}
.prefix-img {
  color: white;
  display: flex;
  align-items: center;
  margin-right: 8px;
}
.eltree-text {
  font-family: SourceHanSansSC-Bold;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
  line-height: 24px;
  font-weight: 500;
  width: 207px;
  padding: 5px;
}
.eltree-con {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  line-height: 24px;
  font-weight: 400;
}
/deep/ .el-tree-node__content {
  margin-top: 10px;
  border-bottom: 1px solid #e6f1fc;
}
/* /deep/ .el-tree-node:last-child{
  border-bottom: 0;
} */

/* 为 el-tree 的第一级节点添加滚动条 */
/deep/ .el-tree .el-tree-node__children {
  overflow-y: auto;
  max-height: 600px; /* 你可以根据需要设置最大高度 */
}

/* 移除第一级节点的边框，以避免滚动条与节点边框混淆 */
/deep/ .el-tree .el-tree-node__content:first-child {
  border-top: none;
}

/* 为树组件容器设置固定宽度 */

/* 隐藏滚动条的整个容器 */
/deep/ .el-tree .el-tree-node__children {
  -ms-overflow-style: none; /* IE 10+ */
  scrollbar-width: none; /* Firefox */
}

/* 针对Webkit内核浏览器的隐藏滚动条样式 */
/deep/ .el-tree .el-tree-node__children::-webkit-scrollbar {
  display: none;
}
</style>
