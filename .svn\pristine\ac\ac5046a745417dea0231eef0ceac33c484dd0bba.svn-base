<template>
  <div class="enterprise-pie-chart">
    <div class="chart-title">{{ title }}</div>
    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'EnterprisePieChart',
  props: {
    chartData: {
      type: Array,
      default: () => [
        { name: '化肥企业', value: 38 },
        { name: '农药企业', value: 30 },
        { name: '种子企业', value: 22 },
        { name: '农机销售', value: 10 }
      ]
    },
    title: {
      type: String,
      default: '默认标题'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      chartInstance: null,
      colorList: [
        'rgba(22, 160, 255, 1)',
        'rgba(29, 255, 213, 1)',
        'rgba(208, 255, 255, 1)',
        'rgba(255, 235, 32, 1)'
      ]
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
    this.$nextTick(() => this.chartInstance && this.chartInstance.resize())
  },
  beforeDestroy() {
    if (this.chartInstance) this.chartInstance.dispose()
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    chartData: {
      handler(newVal) {
        if (this.chartInstance) {
          this.updateChartData(newVal)
        }
      },
      deep: true
    },
    width() { this.$nextTick(() => this.handleResize()) },
    height() { this.$nextTick(() => this.handleResize()) },
    title(newVal) {
      if (this.chartInstance) {
        this.chartInstance.setOption({
          title: { text: newVal }
        })
      }
    }
  },
  methods: {
    initChart() {
      const container = this.$refs.chartContainer
      container.style.width = this.width
      container.style.height = this.height
      this.chartInstance = echarts.init(container)
      this.chartInstance.setOption(this.getChartOption(this.chartData))
    },

    updateChartData(data) {
      const sum = data.reduce((t, i) => t + i.value, 0)
      const processedData = this.processChartData(data)
      this.chartInstance.setOption({
        // title: { text: this.title },
        series: this.getSeriesConfig(processedData)
      })
    },

    getChartOption(data) {
      const sum = data.reduce((t, i) => t + i.value, 0)
      const processedData = this.processChartData(data)
      
      return {
        // title: {
        //   text: this.title,
        //   left: 'center',
        //   top: '0%', // 关键修改：将标题固定在顶部（0%位置）
        //   textStyle: {
        //     fontWeight: 400,
        //     color: '#fff',
        //     fontSize: 18
        //   }
        // },
        // 调整网格顶部边距，避免标题与图表重叠
        grid: { top: 60, left: 40, right: 40, bottom: 20, containLabel: true },
        series: this.getSeriesConfig(processedData)
      }
    },

    processChartData(originalData) {
      let processedData = [];
      originalData.forEach(item => {
        const percent = item._percent || (item.value / (originalData.reduce((t, i) => t + i.value, 0)) * 100).toFixed(1);
        processedData.push({ value: item.value, name: item.name, _percent: percent });
        processedData.push({ name: '', value: 1, itemStyle: { color: 'transparent' }, percent: 0 });
      });
      return processedData;
    },

    getSeriesConfig(processedData) {
      const colorList1 = []
      const colorList2 = []
      this.colorList.forEach(item => {
        colorList1.push(item), colorList1.push('transparent')
        colorList2.push(item.replace(/,\s*\d+(\.\d+)?\)/, ', 0.3)')), colorList2.push('transparent')
      })

      return [
        // 内层饼图
        {
          type: 'pie',
          zlevel: 3,
          radius: ['35%', '38%'],
          center: ['50%', '55%'], // 微调中心位置，适配顶部标题
          itemStyle: { color: (p) => colorList2[p.dataIndex % colorList2.length] },
          label: { show: false },
          data: processedData
        },
        // 外层饼图
        {
          type: 'pie',
          zlevel: 1,
          silent: true,
          radius: ['42%', '68%'],
          center: ['50%', '55%'], // 微调中心位置，适配顶部标题
          itemStyle: { color: (p) => colorList1[p.dataIndex % colorList1.length] },
          label: {
            padding: [0, -30],
            alignTo: 'labelLine',
            formatter: (p) => p.data.name ? `{dot|}   {d|${p.data._percent}%}\n\n{c|${p.name} }` : '',
            rich: {
              c: { color: 'rgba(255,255,255,1)', fontSize: 11, lineHeight: 14 },
              d: { fontSize: 11, color: 'rgba(255,255,255,.7)' },
              dot: { backgroundColor: 'auto', width: 0, height: 0, borderRadius: 3, fontSize: 14, padding: [2, -2, 2, -2] }
            }
          },
          labelLine: { length: 20, length2: 30, show: (p) => p.data.name !== '' },
          data: processedData
        },
        // 最外层装饰圈
        {
          type: 'pie',
          radius: ['71%', '71.3%'],
          center: ['50%', '55%'], // 微调中心位置，适配顶部标题
          hoverAnimation: false,
          itemStyle: { color: 'rgba(201,254,240,0.15)' },
          label: { show: false },
          data: [{ value: 1 }]
        }
      ]
    },

    handleResize() {
      this.chartInstance && this.chartInstance.resize()
    }
  }
}
</script>

<style scoped>
.enterprise-pie-chart {
  padding: 10px;
}

/* 标题样式：居中、白色、黑体、18px */
.chart-title {
  width: 100%;
  text-align: center; /* 居中 */
  color: #ffffff; /* 白色 */
  font-weight: bold; /* 黑体（粗体） */
  font-size: 18px; /* 大小18px */
  margin-bottom: 10px; /* 与图表间距 */
}

.chart-container {
  width: 100%;
  height: 100%;
  min-width: 500px;
  min-height: 300px;
}
</style>