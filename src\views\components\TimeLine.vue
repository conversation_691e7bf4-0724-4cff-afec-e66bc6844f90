<template>
  <div ref="chartContainer" style="width: 100%; height: 100%;"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'TimelineChart',
  props: {
    /**
     * @description 时间轴数据
     * @type {Array<Object>}
     * @property {string} date - 事件日期 (必填)
     * @property {string} title - 事件标题 (必填)
     * @property {string} [country] - 国家/地区 (可选)
     * @property {string} [content] - 事件详细内容 (可选，用于tooltip)
     * @property {Object} [label] - 自定义标签样式 (可选，会覆盖默认样式)
     */
    timelineData: {
      type: Array,
      required: true,
      validator: (value) => {
        return value.every(item => item.date && item.title);
      }
    },

    /**
     * @description ECharts 配置项，会与默认配置合并
     * @type {Object}
     */
    option: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      myChart: null,
      chartOption: null
    };
  },
  watch: {
    timelineData: {
      handler() {
        this.updateChart();
      },
      deep: true
    },
    option: {
      handler() {
        this.updateChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
    this.updateChart();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    initChart() {
      this.myChart = echarts.init(this.$refs.chartContainer);
    },

    updateChart() {
      if (!this.myChart) return;
      this.chartOption = this.generateOption();
      this.myChart.setOption(this.chartOption, true);
    },

    generateOption() {
      const timelineData = this.timelineData;

      // 处理数据，为ECharts series准备格式
      const seriesData = timelineData.map((item, index) => {
        const baseItem = {
          date: item.date,
          title: item.title,
          country: item.country,
          content: item.content,
          value: 0, // 固定y值，使点在同一水平线上
          name: item.date, // 用于tooltip的默认显示
          label: {
            show: true,
            lineHeight: 20,
            align: index % 2 === 0 ? 'left' : 'right',
            padding: 20,
            position: index % 2 === 0 ? 'left' : 'right',
            formatter: `{bolder|${item.date}}\n{normal|${item.title}}`,
            rich: {
              bolder: {
                fontWeight: 700,
                color: '#C1911E',
                fontSize: 16
              },
              normal: {
                color: '#fff',
                fontSize: 14
              }
            },
            ...item.label // 应用用户自定义的label样式
          }
        };
        return baseItem;
      });

      // 准备Y轴标签数据
      const yAxisData = timelineData.map(item => 
        item.country ? `${item.date}  ${item.country}` : item.date
      );

      // 用于连接首尾的空数据点
      const linev = { value: 0, symbol: 'none' };

      const defaultOption = {
        tooltip: {
          trigger: 'item',
          formatter: params => {
            const data = params.data;
            let res = `<strong>${data.date}</strong>`;
            res += `<br/>${data.title}`;
            if (data.country) res += `<br/><span style="color: #666;">${data.country}</span>`;
            if (data.content) res += `<br/><br/>${data.content}`;
            return res;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#ccc',
          borderWidth: 1,
          padding: 10,
          textStyle: {
            color: '#333'
          },
          // 调整tooltip位置，使其在标签旁边
          position: function(point) {
            const isLeft = point[0] < window.innerWidth / 2;
            return isLeft ? [point[0] + 20, point[1]] : [point[0] - 20, point[1]];
          }
        },
        grid: {
            top: '5%',
            left: '0%', // 给左侧标签留出空间
            right: '0%', // 给右侧标签留出空间
            bottom: '5%',
        },
        xAxis: {
            show: false,
            type: 'value',
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: { show: false },
            splitLine: { show: false },
            min: -1, // 稍微扩大范围，防止点在边缘被截断
            max: 1
        },
        yAxis: {
            type: 'category',
            axisLine: {
                show: true,
                lineStyle: {
                    width: 3,
                    color: '#0066CC',
                },
            },
            axisTick: {
                show: true,
                alignWithLabel: true,
                lineStyle: {
                    color: '#0066CC',
                    width: 2
                }
            },
            splitLine: { show: false },
            axisLabel: {
                show: false
            },
            data: ['', ...yAxisData, ''] // 上下各加一个空，让轴线更长一点
        },
        series: [
            {
                type: 'line',
                symbol: 'circle',
                symbolSize: 12,
                color: '#FF6600',
                itemStyle: {
                    borderColor: '#fff',
                    borderWidth: 2,
                    shadowBlur: 4,
                    shadowColor: 'rgba(0,0,0,0.3)'
                },
                lineStyle: {
                    color: '#0066CC',
                    width: 2,
                    type: 'solid'
                },
                hoverAnimation: true,
                data: [linev, ...seriesData, linev]
            }
        ]
      };

      // 使用用户传入的option覆盖默认配置
      return this.deepMerge(defaultOption, this.option);
    },

    /**
     * @description 深度合并两个对象
     */
    deepMerge(target, source) {
        if (typeof target !== 'object' || target === null || typeof source !== 'object' || source === null) {
            return source;
        }
        const merged = Array.isArray(target) ? [...target] : { ...target };
        Object.keys(source).forEach(key => {
            if (Array.isArray(source[key]) && Array.isArray(target[key])) {
                merged[key] = [...target[key], ...source[key]];
            } else if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                merged[key] = this.deepMerge(target[key], source[key]);
            } else {
                merged[key] = source[key];
            }
        });
        return merged;
    },

    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },

    getEchartsInstance() {
      return this.myChart;
    }
  }
};
</script>

<style scoped>
/* 组件内部样式 */
</style>