import request from '@/utils/request'

// 查询重要岗位审核情况列表
export function listAudit(query) {
  return request({
    url: '/system/audit/list',
    method: 'get',
    params: query
  })
}

// 查询重要岗位审核情况详细
export function getAudit(id) {
  return request({
    url: '/system/audit/' + id,
    method: 'get'
  })
}

// 新增重要岗位审核情况
export function addAudit(data) {
  return request({
    url: '/system/audit',
    method: 'post',
    data: data
  })
}

// 修改重要岗位审核情况
export function updateAudit(data) {
  return request({
    url: '/system/audit',
    method: 'put',
    data: data
  })
}

// 删除重要岗位审核情况
export function delAudit(id) {
  return request({
    url: '/system/audit/' + id,
    method: 'delete'
  })
}
