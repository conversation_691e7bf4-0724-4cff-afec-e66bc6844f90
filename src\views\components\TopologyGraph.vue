<template>
  <div ref="chartContainer" style="width: 100%; height: 100%;"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'ForceGraph',
  props: {
    /**
     * @description 图的数据源
     * @type {Object}
     * @property {Array<Object>} nodes - 节点列表
     * @property {string} nodes[].id - 节点唯一ID (可选, 若未提供则使用索引)
     * @property {string} nodes[].name - 节点显示名称
     * @property {string} [nodes[].category] - 节点所属类别名称
     * @property {string} [nodes[].symbol] - 节点图形 'circle', 'rect', 'image://url'
     * @property {number} [nodes[].symbolSize] - 节点大小
     * @property {Object} [nodes[].itemStyle] - 节点样式
     * @property {Object} [nodes[].data] - 节点额外数据，可在tooltip中使用
     * @property {Array<Object>} links - 边列表
     * @property {string|number} links[].source - 源节点ID或索引
     * @property {string|number} links[].target - 目标节点ID或索引
     * @property {string} [links[].value] - 边的数值或标签
     * @property {Object} [links[].lineStyle] - 边的样式
     * @property {Object} [links[].data] - 边额外数据
     * @property {Array<Object>} [categories] - 类别列表
     * @property {string} categories[].name - 类别名称
     * @property {string} [categories[].symbol] - 该类别节点的默认图形
     */
    graphData: {
      type: Object,
      required: true,
      validator: (value) => {
        return value.nodes && Array.isArray(value.nodes) && value.links && Array.isArray(value.links);
      }
    },

    /**
     * @description ECharts 配置项，会与默认配置合并
     * @type {Object}
     */
    option: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      myChart: null,
      chartOption: null
    };
  },
  watch: {
    graphData: {
      handler() {
        this.updateChart();
      },
      deep: true
    },
    option: {
      handler() {
        this.updateChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
    this.updateChart();
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    /**
     * @description 初始化ECharts实例
     */
    initChart() {
      this.myChart = echarts.init(this.$refs.chartContainer);
    },

    /**
     * @description 更新图表数据和配置
     */
    updateChart() {
      if (!this.myChart) return;

      this.chartOption = this.generateOption();
      this.myChart.setOption(this.chartOption, true);
    },

    /**
     * @description 生成完整的ECharts配置项
     * @returns {Object}
     */
    generateOption() {
      const { nodes, links, categories } = this.graphData;

      // 处理节点，为没有id的节点添加索引作为id
      const processedNodes = nodes.map((node, index) => ({
        id: node.id ?? index,
        ...node
      }));

      // 处理边，将可能的字符串ID转换为索引
      const nodeIdToIndexMap = new Map(processedNodes.map((node, index) => [node.id, index]));
      const processedLinks = links.map(link => {
        const source = typeof link.source === 'string' ? nodeIdToIndexMap.get(link.source) : link.source;
        const target = typeof link.target === 'string' ? nodeIdToIndexMap.get(link.target) : link.target;
        return { ...link, source, target };
      });

      // 默认配置
      const defaultOption = {
        // backgroundColor: '#1a4377',
        grid: {
            left: '10%',
            top: 60,
            right: '10%',
            bottom: 60,
        },
        // toolbox: {
        //     feature: {
        //         saveAsImage: {}
        //     }
        // },
        // legend: {
        //     show : true,
        //     textStyle: {
        //         color: '#fff'
        //     }
        // },
        tooltip: {
            trigger: 'item',
            formatter: this.tooltipFormatter
        },
        series: [{
            type: 'graph',
            layout: 'force',
            force: {
                repulsion: 1000,
                edgeLength: 70,
                layoutAnimation: true,
            },
            symbolSize: 70,
            nodeScaleRatio: 1,
            roam: true,
            draggable: true,
            focusNodeAdjacency: false,
            edgeSymbol: ['circle', 'arrow'],
            label: {
                normal: {
                    show: true,
                    position: 'inside',
                    color: 'gold',
                    fontSize: 14,
                    fontWeight: 'bold'
                }
            },
            edgeLabel: {
                normal: {
                    show: true,
                    textStyle: {
                        fontSize: 12,
                        color: '#fff'
                    },
                    formatter: "{c}"
                }
            },
            categories: categories || [],
            itemStyle: {
                normal: {
                    borderColor: '#04f2a7',
                    borderWidth: 2,
                    shadowBlur: 10,
                    shadowColor: '#04f2a7',
                    color: '#001c43',
                }
            },
            lineStyle: {
                normal: {
                    opacity: 0.9,
                    width: 2,
                    curveness: 0,
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [{
                            offset: 0,
                            color: '#e0f55a'
                        }, {
                            offset: 1,
                            color: '#639564'
                        }],
                        globalCoord: false
                    }
                }
            },
            symbolKeepAspect: false,
            data: processedNodes,
            links: processedLinks
        }]
      };

      // 使用用户传入的option覆盖默认配置
      return this.deepMerge(defaultOption, this.option);
    },

    /**
     * @description 自定义Tooltip格式化函数
     * @param {Object} params - ECharts tooltip参数
     * @returns {string}
     */
    tooltipFormatter(params) {
        let res = '';
        if (params.dataType === 'node') {
            const nodeData = params.data;
            res = `<strong>${nodeData.name}</strong>`;
            if (nodeData.category) {
                res += `<br/>类别: ${nodeData.category}`;
            }
            // 展示额外数据
            if (nodeData.data && typeof nodeData.data === 'object') {
                Object.entries(nodeData.data).forEach(([key, value]) => {
                    res += `<br/>${key}: ${value}`;
                });
            }
        } else if (params.dataType === 'edge') {
            const linkData = params.data;
            const sourceNode = this.graphData.nodes[linkData.source];
            const targetNode = this.graphData.nodes[linkData.target];
            res = `<strong>${sourceNode.name}</strong> -> <strong>${targetNode.name}</strong>`;
            if (linkData.value) {
                res += `<br/>关系: ${linkData.value}`;
            }
             // 展示边的额外数据
            if (linkData.data && typeof linkData.data === 'object') {
                Object.entries(linkData.data).forEach(([key, value]) => {
                    res += `<br/>${key}: ${value}`;
                });
            }
        }
        return res;
    },

    /**
     * @description 深度合并两个对象
     * @param {Object} target - 目标对象
     * @param {Object} source - 源对象
     * @returns {Object} - 合并后的对象
     */
    deepMerge(target, source) {
        if (typeof target !== 'object' || target === null || typeof source !== 'object' || source === null) {
            return source;
        }

        const merged = Array.isArray(target) ? [...target] : { ...target };

        Object.keys(source).forEach(key => {
            if (Array.isArray(source[key]) && Array.isArray(target[key])) {
                merged[key] = [...target[key], ...source[key]];
            } else if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                merged[key] = this.deepMerge(target[key], source[key]);
            } else {
                merged[key] = source[key];
            }
        });

        return merged;
    },

    /**
     * @description 处理窗口大小变化
     */
    handleResize() {
      if (this.myChart) {
        this.myChart.resize();
      }
    },

    /**
     * @description 公共方法：获取ECharts实例
     * @returns {Object|null}
     */
    getEchartsInstance() {
      return this.myChart;
    }
  }
};
</script>

<style scoped>
/* 组件内部样式 */
</style>