<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form :inline="true" size="small" label-width="68px">
      <el-form-item label="处室">
        <el-select v-model="selectedDepartment" placeholder="请选择">
          <el-option v-for="item in departments" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="职务职级">
        <el-select v-model="selectedJobTitle" placeholder="请选择">
          <el-option v-for="item in jobTitles" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="考核等级">
        <el-select v-model="selectedAssessmentLevel" placeholder="请选择">
          <el-option v-for="item in assessmentLevels" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="姓名">
        <el-input v-model="searchName" placeholder="请输入姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-button type="primary" size="small" @click="handleQuery">查询</el-button>
      <el-button type="warning" plain icon="el-icon-download" size="small" @click="handleExport"
      v-hasPermi="['system:audit:export']">导出</el-button>
    </el-form>

    <!-- 表格 -->
    <div class="table-container" v-if="!isDetailsVisible">
      <!-- <el-table :data="filteredAuditList"> -->
      <el-table :data="filteredAuditList" @row-click="handleRowClick">
        <el-table-column class="table-column" label="排名" align="center" prop="xh" />
        <el-table-column class="table-column" label="部门" align="center" prop="bm" />
        <el-table-column class="table-column" label="类型" align="center" prop="name" />
        <!-- <el-table-column class="table-column" label="职务职称" align="center" prop="jobTitle" /> -->
        <el-table-column class="table-column" label="测评日期" align="center" prop="date" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.date }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考核等级" align="center" prop="assessmentLevel" />
        <!-- <el-table-column label="排名" align="center" prop="total" /> -->
        <el-table-column label="总分" align="center" prop="zf" />
        <el-table-column label="思想政治建设" align="center" prop="morals" />
        <el-table-column label="领导能力" align="center" prop="ability" />
        <el-table-column label="工作实绩" align="center" prop="diligent" />
        <el-table-column label="党风廉政建设" align="center" prop="achievement" />
        <el-table-column label="能力作风建设" align="center" prop="integrity" />
        <el-table-column label="评分明细" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="viewDetails(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 详细信息表格 -->
    <div class="detail-table" v-if="isDetailsVisible">
      <el-table :data="detailData">
        <el-table-column class="table-column" label="排名" align="center" prop="xh" />
        <el-table-column class="table-column" label="部门" align="center" prop="bm" />
        <el-table-column class="table-column" label="类型" align="center" prop="name" />
        <!-- <el-table-column class="table-column" label="职务职称" align="center" prop="jobTitle" /> -->
        <el-table-column class="table-column" label="测评日期" align="center" prop="date" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.date }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="考核等级" align="center" prop="assessmentLevel" /> -->
        <!-- <el-table-column label="排名" align="center" prop="total" /> -->
        <el-table-column label="总分" align="center" prop="zf" />
        <el-table-column label="思想政治建设" align="center" prop="morals" />
        <el-table-column label="领导能力" align="center" prop="ability" />
        <el-table-column label="工作实绩" align="center" prop="diligent" />
        <el-table-column label="党风廉政建设" align="center" prop="achievement" />
        <el-table-column label="能力作风建设" align="center" prop="integrity" />
      </el-table>
      <el-button type="primary" size="small" @click="isDetailsVisible = false">关闭</el-button>
    </div>

    <!-- 分页 -->
    <!-- <div class="pagination-container">
      <el-pagination background layout="prev, pager, next" :total="2">
      </el-pagination>
    </div> -->
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
export default {
  name: "Audit",
  data() {
    return {
      selectedDepartment: '',
      selectedJobTitle: '',
      selectedAssessmentLevel: '',
      searchName: '',
      departments: [
        { value: '厅领导', label: '厅领导' },
        { value: '两总师', label: '两总师' },
        { value: '利用处', label: '利用处' },
      ],
      jobTitles: [
        { value: '处长', label: '处长' },
        { value: '副处长', label: '副处长' },
        { value: '科员', label: '科员' }
      ],
      assessmentLevels: [
        { value: '优秀', label: '优秀' },
        { value: '称职', label: '称职' },
        { value: '基本称职', label: '基本称职' },
        { value: '不称职', label: '不称职' }
      ],
      auditList: [
        {
          xh: 1,
          bm: '利用处',
          name: '职能部门',
          jobTitle: '二级巡查员',
          date: '2024年',
          assessmentLevel: '好',
          total: 96,
          morals: 9,
          ability: 9,
          diligent: 29,
          achievement: 39,
          integrity: 10,
          zf: 96,
        },
        {
          xh: 2,
          bm: '黑龙江省国土空间规划研究院',
          name: '事业单位',
          jobTitle: '总工程师',
          date: '2024年',
          assessmentLevel: '好',
          total: 96,
          morals: 9,
          ability: 9,
          diligent: 29,
          achievement: 39,
          integrity: 10,
          zf: 96,
        },
        {
          xh: 3,
          bm: '办公室',
          name: '非职能部门',
          jobTitle: '处长',
          date: '2024年',
          assessmentLevel: '好',
          total: 97,
          morals: 10,
          ability: 9,
          diligent: 29,
          achievement: 39,
          integrity: 10,
          zf: 97,
        }
      ],
      detailData: [
        {
          xh: 1,
          bm: '利用处',
          name: '王厅',
          jobTitle: '二级巡查员',
          date: '2025年1月20日',
          total: 94,
          morals: 95,
          ability: 94,
          diligent: 93,
          achievement: 92,
          integrity: 91,
          zf: 94,
        },
        {
          xh: 2,
          bm: '黑龙江省国土空间规划研究院',
          name: '李处长',
          jobTitle: '处长',
          date: '2025年1月20日',
          total: 93,
          morals: 95,
          ability: 94,
          diligent: 93,
          achievement: 92,
          integrity: 91,
          zf: 93,
        },
        {
          xh: 3,
          bm: '办公室',
          name: '张总工',
          jobTitle: '总工程师',
          date: '2025年1月20日',
          total: 91,
          morals: 95,
          ability: 94,
          diligent: 93,
          achievement: 92,
          integrity: 91,
          zf: 91,
        }
      ],
      // 总条数
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bm: null,
        name: null,
        jobTitle: null,
        date: null,
        assessmentLevel: null,
        total: null,
        morals: null,
        ability: null,
        diligent: null,
        achievement: null,
        integrity: null,
        status: null
      },
      currentRow: null, // 当前显示详细信息的行的id
      isDetailsVisible: false, // 控制详细信息表格的显示
      currentlyViewing: null, // 当前查看的行数据
    };
  },
  computed: {
    filteredAuditList() {
      // 简单的过滤逻辑，可以根据选中的部门、职务职级、考核等级和姓名进行过滤
      return this.auditList.filter(item => {
        return (!this.selectedDepartment || item.bm === this.selectedDepartment) &&
          (!this.selectedJobTitle || item.jobTitle === this.selectedJobTitle) &&
          (!this.selectedAssessmentLevel || item.assessmentLevel === this.selectedAssessmentLevel) &&
          (!this.searchName || item.name.includes(this.searchName));
      });
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.total = this.auditList.length; // 更新总条数
    },
    handleQuery() {
      // 执行查询操作，可以在这里添加更复杂的逻辑
    },
    handleExport() {
      // 执行导出操作，可以在这里添加导出逻辑
    },
    viewDetails(row) {
      this.currentRow = row.id; // 设置当前行的id
    },
    handleRowClick(row) {
      this.currentRow = row.id; // 也可以在这里设置，如果你想要点击整行也能显示详情
    },
    viewDetails(row) {
      this.currentlyViewing = row; // 设置当前查看的行数据
      this.isDetailsVisible = true; // 显示详细信息表格
    },
  }
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #FFFFFF;
  box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.06);
  border-radius: 4px;
}

.department-select {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  line-height: 20px;
  font-weight: 400;
}

::v-deep .el-table th.el-table__cell>.cell {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #203F78;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 700;
}

::v-deep .el-table tr {
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  line-height: 20px;
  font-weight: 400;
}

.search-btn {
  background: #1B5DD8;
  border-radius: 16px;
  font-family: SourceHanSansSC-Regular;
  font-size: 14px;
  color: #FFFFFF;
  letter-spacing: 0;
  text-align: center;
  font-weight: 400;
}

.pagination-container {
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // right: 0;
  // height: 50px;
  // z-index: 1000;
}

.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}
</style>