<template>
  <div class="map-container">
    <div id="map" ref="mapRef"></div>
    <div class="legend">
      <div class="legend-item">
        <span class="legend-icon building"></span>
        <span>建筑设施</span>
      </div>
      <div class="legend-item">
        <span class="legend-icon road"></span>
        <span>交通道路</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import worldGeoJson from './world.js'

export default {
  name: 'WorldMap',
  props: {
    buildingData: {
      type: Array,
      default: () => [
        { name: '乌鲁木齐地窝堡国际机场', coords: [87.4833, 43.9022], type: '机场', status: '运营中', capacity: '2300万人次/年' },
        { name: '成都天府国际机场', coords: [104.3950, 30.4000], type: '机场', status: '运营中', capacity: '6000万人次/年' },
      ]
    },
    roadData: {
      type: Array,
      default: () => [
        { name: '中吉乌铁路', coords: [[82.9204, 41.3396], [74.7661, 41.2044], [69.2163, 41.5128]], type: '铁路', status: '建设中', length: '523公里' },
    { name: '川藏铁路延伸线', coords: [[94.9236, 29.6516], [97.0376, 29.7059], [98.6246, 30.1400]], type: '铁路', status: '建设中', length: '800公里' },

    // 中国西部出境管道
    { name: '中国-中亚天然气管道D线', coords: [[83.4512, 42.0281], [71.7887, 41.3774], [64.5219, 39.7837], [58.5868, 37.9497]], type: '管道', status: '运营中', length: '3000公里' },
    { name: '中缅原油管道', coords: [[97.3973, 24.8064], [95.9560, 22.1942], [94.7325, 19.7633]], type: '管道', status: '运营中', length: '771公里' },
      ]
    },
  },
  data() {
    return {
      myChart: null,
      highlightCountries: [
        "中国", "印度", "巴基斯坦", "阿富汗", "哈萨克斯坦", 
        "乌兹别克斯坦", "土库曼斯坦", "吉尔吉斯斯坦", "塔吉克斯坦",
        "伊朗", "土耳其", "埃及", "沙特阿拉伯", "伊拉克", "叙利亚",
        "约旦", "黎巴嫩", "以色列", "巴勒斯坦", "科威特", "阿曼",
        "卡塔尔", "巴林", "阿联酋", "也门"
      ],
//       buildingData: [
//         { name: '乌鲁木齐地窝堡国际机场', coords: [87.4833, 43.9022], type: '机场', status: '运营中', capacity: '2300万人次/年' },
//         { name: '成都天府国际机场', coords: [104.3950, 30.4000], type: '机场', status: '运营中', capacity: '6000万人次/年' },
//         // { name: '北京国际机场', coords: [116.4074, 39.9042], type: '机场', status: '运营中', capacity: '1亿人次/年' },
//         // { name: '上海港', coords: [121.4737, 31.2304], type: '港口', status: '运营中', capacity: '4300万标箱/年' },
//         { name: '迪拜塔', coords: [55.2744, 25.1972], type: '地标建筑', status: '运营中', height: '828米' },
//         { name: '伊斯坦布尔机场', coords: [28.7519, 41.2753], type: '机场', status: '运营中', capacity: '2亿人次/年' },
//         { name: '新德里地铁', coords: [77.2090, 28.6139], type: '交通枢纽', status: '运营中', lines: '12条线路' },
//         { name: '德黑兰炼油厂', coords: [51.3890, 35.6892], type: '工业设施', status: '运营中', capacity: '50万桶/日' },
//         { name: '吉达港', coords: [39.1925, 21.4858], type: '港口', status: '运营中', capacity: '1000万标箱/年' },
//         { name: '开罗国际机场', coords: [31.4056, 30.1219], type: '机场', status: '运营中', capacity: '3000万人次/年' },
//         { name: '阿拉木图数据中心', coords: [76.8512, 43.2220], type: '数据中心', status: '建设中', capacity: '10MW' },
//         { name: '喀布尔发电站', coords: [69.2075, 34.5553], type: '能源设施', status: '规划中', capacity: '500MW' },
//       ],
//       roadData: [
// // --- 以下为新增数据 ---

//     // 中国西部出境铁路
//     { name: '中吉乌铁路', coords: [[82.9204, 41.3396], [74.7661, 41.2044], [69.2163, 41.5128]], type: '铁路', status: '建设中', length: '523公里' },
//     { name: '川藏铁路延伸线', coords: [[94.9236, 29.6516], [97.0376, 29.7059], [98.6246, 30.1400]], type: '铁路', status: '建设中', length: '800公里' },

//     // 中国西部出境管道
//     { name: '中国-中亚天然气管道D线', coords: [[83.4512, 42.0281], [71.7887, 41.3774], [64.5219, 39.7837], [58.5868, 37.9497]], type: '管道', status: '运营中', length: '3000公里' },
//     { name: '中缅原油管道', coords: [[97.3973, 24.8064], [95.9560, 22.1942], [94.7325, 19.7633]], type: '管道', status: '运营中', length: '771公里' },

//     // 中国西部出境公路与光缆
//     { name: '中巴喀喇昆仑公路', coords: [[75.9812, 35.1484], [74.7661, 34.5227], [67.0011, 24.8607]], type: '公路', status: '运营中', length: '1300公里' },
//     { name: '中国-巴基斯坦光缆', coords: [[77.5946, 39.0742], [74.7661, 34.5227], [69.3451, 30.3753]], type: '光缆', status: '运营中', length: '2700公里' },

//     // 南亚区域内线路
//     { name: '印度-孟加拉国公路', coords: [[88.3639, 22.5726], [89.8332, 24.6232]], type: '公路', status: '运营中', length: '650公里' },
//     { name: '斯里兰卡科伦坡港口连接线', coords: [[79.8512, 6.9344], [80.3176, 7.2906]], type: '公路', status: '建设中', length: '29公里' },

//     // 西亚及中东区域线路
//     { name: '伊朗-伊拉克铁路', coords: [[48.5164, 35.8617], [45.7579, 33.2237], [44.3661, 33.3152]], type: '铁路', status: '规划中', length: '950公里' },
//     { name: '沙特-约旦-叙利亚公路', coords: [[46.7219, 24.7136], [36.2384, 30.5852], [36.7812, 33.5138]], type: '公路', status: '规划中', length: '1800公里' },
//     { name: '土耳其-叙利亚-伊拉克石油管道', coords: [[32.8597, 39.9334], [36.7812, 33.5138], [44.3661, 33.3152]], type: '管道', status: '规划中', length: '1500公里' },

//     // 新增海运线路
//     { name: '波斯湾-红海海运线', coords: [[55.1694, 25.2582], [45.0355, 12.5737]], type: '海运', status: '运营中', length: '1900公里' },
//     { name: '地中海-埃及亚历山大港', coords: [[29.9553, 31.2304], [31.2001, 30.0444]], type: '海运', status: '运营中', length: '250公里' },

//     // 新增航空线路 (中国起点均为西部城市)
//     { name: '乌鲁木齐-德黑兰-伊斯坦布尔航线', coords: [[87.6271, 43.8255], [51.3890, 35.6892], [28.9784, 41.0082]], type: '航空', status: '运营中', length: '6200公里' },
//     { name: '成都-迪拜-开罗航线', coords: [[104.0665, 30.5728], [55.3188, 25.2797], [31.2304, 30.0444]], type: '航空', status: '运营中', length: '7800公里' },

//     // 海湾地区线路
//     { name: '沙特-科威特-伊拉克公路', coords: [[46.7219, 24.7136], [47.9744, 29.3759], [44.3661, 33.3152]], type: '公路', status: '运营中', length: '1200公里' },
//     { name: '阿联酋-阿曼输油管道', coords: [[55.1694, 25.2582], [58.5922, 23.6101]], type: '管道', status: '建设中', length: '370公里' },
//     { name: '卡塔尔-沙特跨海大桥', coords: [[51.5310, 25.2854], [49.8944, 26.2541]], type: '公路', status: '建设中', length: '40公里' }
//       ],
      
      currentHighlightIndex: 0,
      highlightInterval: null
    };
  },
  mounted() {
    this.initEchartsMap();
    this.startAutoHighlight();
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
    }
    if (this.highlightInterval) {
      clearInterval(this.highlightInterval);
    }
  },
  methods: {
    initEchartsMap() {
      this.myChart = echarts.init(this.$refs.mapRef);
      echarts.registerMap('customRegion', worldGeoJson);

      const mapData = this.highlightCountries.map(countryName => ({
        name: countryName,
        value: countryName,
      }));

      const option = {
        backgroundColor: {
          type: 'radial',
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            { offset: 0, color: '#0f1729' },
            { offset: 0.5, color: '#1a2840' },
            { offset: 1, color: '#0a0e1f' }
          ]
        },
        // title: {
        //   text: '全球基础设施建设地图',
        //   left: 'center',
        //   top: '20',
        //   textStyle: {
        //     color: '#fff',
        //     fontSize: 28,
        //     fontWeight: 'bold',
        //     textShadowColor: 'rgba(0, 255, 255, 0.8)',
        //     textShadowBlur: 15,
        //     textShadowOffsetX: 0,
        //     textShadowOffsetY: 0
        //   }
        // },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(10, 20, 40, 0.95)',
          borderColor: '#00ffff',
          borderWidth: 2,
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (params) => {
            if (params.componentSubType === 'scatter' || params.componentSubType === 'effectScatter') {
              const data = params.data;
              return `
                <div style="padding: 10px;">
                  <div style="font-size: 18px; font-weight: bold; color: #00ffff; margin-bottom: 10px; text-shadow: 0 0 10px #00ffff;">
                    ${data.name}
                  </div>
                  <div style="margin: 5px 0; border-left: 3px solid #ffd700; padding-left: 8px;">类型: <span style="color: #ffd700;">${data.type}</span></div>
                  <div style="margin: 5px 0; border-left: 3px solid ${data.status === '运营中' ? '#00ff00' : data.status === '建设中' ? '#ff9900' : '#ff6600'}; padding-left: 8px;">状态: <span style="color: ${data.status === '运营中' ? '#00ff00' : data.status === '建设中' ? '#ff9900' : '#ff6600'}">${data.status}</span></div>
                  <div style="margin: 5px 0; border-left: 3px solid #fff; padding-left: 8px;">规模: <span style="color: #fff;">${data.capacity || data.height || data.lines || '详见规划'}</span></div>
                  <div style="margin-top: 10px; padding-top: 8px; border-top: 1px solid rgba(0,255,255,0.3); font-size: 12px; color: #aaa;">坐标: ${data.coords[0].toFixed(2)}°E, ${data.coords[1].toFixed(2)}°N</div>
                </div>
              `;
            } else if (params.componentSubType === 'lines') {
              const data = params.data;
              return `
                <div style="padding: 10px;">
                  <div style="font-size: 18px; font-weight: bold; color: #ff00ff; margin-bottom: 10px; text-shadow: 0 0 10px #ff00ff;">
                    ${data.name}
                  </div>
                  <div style="margin: 5px 0; border-left: 3px solid #ffd700; padding-left: 8px;">类型: <span style="color: #ffd700;">${data.type}</span></div>
                  <div style="margin: 5px 0; border-left: 3px solid ${data.status === '运营中' ? '#00ff00' : '#ff9900'}; padding-left: 8px;">状态: <span style="color: ${data.status === '运营中' ? '#00ff00' : '#ff9900'}">${data.status}</span></div>
                  <div style="margin: 5px 0; border-left: 3px solid #fff; padding-left: 8px;">长度: <span style="color: #fff;">${data.length}</span></div>
                </div>
              `;
            } else {
              return params.name;
            }
          }
        },
        geo: {
          map: 'customRegion',
          zoom: 1.7,
          center: [64, 30],
          roam: false,
          silent: false,
          label: {
            show: false
          },
          itemStyle: {
            areaColor: 'transparent',
            borderColor: 'transparent'
          },
          emphasis: {
            itemStyle: {
              areaColor: 'transparent'
            }
          }
        },
        visualMap: {
          show: false,
          min: 0,
          max: 1,
          inRange: {
            color: ['#313695', '#4575b4', '#74add1', '#abd9e9']
          }
        },
        series: [
          {
            name: '国家',
            type: 'map',
            mapType: 'customRegion',
            zoom: 1.7,
            center: [64, 30],
            roam: false,
            label: {
              show: true,
              color: '#6ea8c8',
              fontSize: 10,
              fontWeight: '500',
              emphasis: {
                color: '#00ffff',
                fontSize: 12,
                fontWeight: 'bold',
                textShadowColor: 'rgba(0, 255, 255, 0.8)',
                textShadowBlur: 10
              }
            },
            itemStyle: {
              areaColor: '#152238',
              borderColor: '#2d5a7b',
              borderWidth: 1.5,
              shadowColor: 'rgba(45, 90, 123, 0.4)',
              shadowBlur: 8,
              emphasis: {
                areaColor: '#1e3a52',
                borderColor: '#00d9ff',
                borderWidth: 2.5,
                shadowColor: 'rgba(0, 255, 255, 0.9)',
                shadowBlur: 20
              }
            },
            data: mapData
          },
          {
            name: '道路光晕',
            type: 'lines',
            coordinateSystem: 'geo',
            zlevel: 2,
            polyline: true,
            effect: {
              show: true,
              period: 3,
              trailLength: 0.5,
              symbol: 'circle',
              symbolSize: 5,
            },
            lineStyle: {
              width: 4,
              opacity: 0.2,
              curveness: 0.3
            },
            data: this.roadData.map(road => ({
              coords: road.coords,
              name: road.name,
              type: road.type,
              status: road.status,
              length: road.length,
              lineStyle: {
                color: road.type === '铁路' ? '#00ffff' : road.type === '管道' ? '#ffaa00' : road.type === '海运' ? '#0099ff' : '#aa00ff',
                shadowColor: road.type === '铁路' ? '#00ffff' : road.type === '管道' ? '#ffaa00' : road.type === '海运' ? '#0099ff' : '#aa00ff',
                shadowBlur: 25
              },
              effect: {
                color: road.type === '铁路' ? '#00ffff' : road.type === '管道' ? '#ffaa00' : road.type === '海运' ? '#0099ff' : '#aa00ff',
              }
            }))
          },
          {
            name: '道路线路',
            type: 'lines',
            coordinateSystem: 'geo',
            zlevel: 3,
            polyline: true,
            effect: {
              show: true,
              period: 4,
              trailLength: 0.2,
              symbol: 'arrow',
              symbolSize: 10,
            },
            lineStyle: {
              width: 3,
              opacity: 0.9,
              curveness: 0.3
            },
            data: this.roadData.map(road => ({
              coords: road.coords,
              name: road.name,
              type: road.type,
              status: road.status,
              length: road.length,
              lineStyle: {
                color: road.type === '铁路' ? '#00ffff' : road.type === '管道' ? '#ffaa00' : road.type === '海运' ? '#0099ff' : '#aa00ff',
                shadowColor: road.type === '铁路' ? '#00ffff' : road.type === '管道' ? '#ffaa00' : road.type === '海运' ? '#0099ff' : '#aa00ff',
                shadowBlur: 15
              },
              effect: {
                color: road.type === '铁路' ? '#00ffff' : road.type === '管道' ? '#ffaa00' : road.type === '海运' ? '#0099ff' : '#aa00ff',
              }
            }))
          },
          {
            name: '建筑光晕',
            type: 'effectScatter',
            coordinateSystem: 'geo',
            zlevel: 4,
            rippleEffect: {
              brushType: 'stroke',
              scale: 6,
              period: 3
            },
            showEffectOn: 'render',
            itemStyle: {
              shadowBlur: 30
            },
            data: this.buildingData.map(building => ({
              ...building,
              value: building.coords,
              symbolSize: 25,
              itemStyle: {
                color: building.status === '运营中' ? 'rgba(0, 255, 0, 0.4)' : building.status === '建设中' ? 'rgba(255, 153, 0, 0.4)' : 'rgba(255, 102, 0, 0.4)',
                shadowColor: building.status === '运营中' ? '#00ff00' : building.status === '建设中' ? '#ff9900' : '#ff6600',
                shadowBlur: 35
              }
            }))
          },
          {
            name: '建筑设施',
            type: 'scatter',
            coordinateSystem: 'geo',
            zlevel: 5,
            symbol: 'pin',
            symbolSize: 35,
            label: {
              show: false,
              formatter: '{b}',
              position: 'top',
              color: '#fff',
              fontSize: 12,
              fontWeight: 'bold',
              backgroundColor: 'rgba(0,0,0,0.8)',
              padding: [6, 10],
              borderRadius: 6,
              borderColor: '#00ffff',
              borderWidth: 1
            },
            emphasis: {
              label: {
                show: true
              },
              itemStyle: {
                shadowBlur: 40
              }
            },
            itemStyle: {
              color: (params) => {
                const status = params.data.status;
                if (status === '运营中') return '#00ff00';
                if (status === '建设中') return '#ff9900';
                return '#ff6600';
              },
              shadowBlur: 20,
              shadowColor: (params) => {
                const status = params.data.status;
                if (status === '运营中') return '#00ff00';
                if (status === '建设中') return '#ff9900';
                return '#ff6600';
              },
              borderColor: '#fff',
              borderWidth: 2
            },
            data: this.buildingData
          }
        ]
      };

      this.myChart.setOption(option);

      window.addEventListener('resize', () => {
        this.myChart.resize();
      });
    },

    startAutoHighlight() {
      if (this.highlightCountries.length === 0) return;

      this.highlightInterval = setInterval(() => {
        if (this.currentHighlightIndex > 0) {
          const prevCountry = this.highlightCountries[this.currentHighlightIndex - 1];
          this.myChart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            name: prevCountry
          });
        }
        
        const countryName = this.highlightCountries[this.currentHighlightIndex];
        
        this.myChart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          name: countryName
        });

        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.highlightCountries.length;
      }, 3000);
    }
  }
}
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 100vh;
  position: relative;
  background: radial-gradient(ellipse at center, #1a2840 0%, #0f1729 50%, #0a0e1f 100%);
  overflow: hidden;
}

.map-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(2px 2px at 20% 30%, rgba(255, 255, 255, 0.8), transparent),
    radial-gradient(2px 2px at 60% 70%, rgba(255, 255, 255, 0.6), transparent),
    radial-gradient(1px 1px at 50% 50%, rgba(255, 255, 255, 0.5), transparent),
    radial-gradient(1px 1px at 80% 10%, rgba(255, 255, 255, 0.7), transparent),
    radial-gradient(2px 2px at 90% 60%, rgba(255, 255, 255, 0.4), transparent),
    radial-gradient(1px 1px at 33% 80%, rgba(255, 255, 255, 0.6), transparent),
    radial-gradient(1px 1px at 15% 90%, rgba(255, 255, 255, 0.5), transparent);
  background-size: 200% 200%;
  animation: stars 60s linear infinite;
  pointer-events: none;
  z-index: 0;
}

@keyframes stars {
  from {
    background-position: 0% 0%;
  }
  to {
    background-position: 100% 100%;
  }
}

#map {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

.legend {
  position: absolute;
  top: 80px;
  right: 30px;
  background: linear-gradient(135deg, rgba(10, 20, 40, 0.85), rgba(20, 35, 60, 0.75));
  backdrop-filter: blur(15px);
  border: 2px solid rgba(0, 255, 255, 0.4);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 
    0 8px 32px rgba(0, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  z-index: 1000;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  color: #fff;
  font-size: 15px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-icon {
  width: 45px;
  height: 24px;
  margin-right: 14px;
  border-radius: 6px;
  position: relative;
  animation: iconGlow 3s ease-in-out infinite;
}

@keyframes iconGlow {
  0%, 100% {
    box-shadow: 0 0 15px currentColor;
  }
  50% {
    box-shadow: 0 0 25px currentColor, 0 0 35px currentColor;
  }
}

.legend-icon.building {
  background: linear-gradient(90deg, #00ff00 0%, #ff9900 50%, #ff6600 100%);
  color: rgba(0, 255, 0, 0.8);
}

.legend-icon.building::after {
  content: '●';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 20px;
  text-shadow: 0 0 15px #fff, 0 0 25px #fff;
  animation: pulse 2s ease-in-out infinite;
}

.legend-icon.road {
  background: linear-gradient(90deg, #aa00ff 0%, #00ffff 33%, #ffaa00 66%, #0099ff 100%);
  color: rgba(170, 0, 255, 0.8);
}

.legend-icon.road::after {
  content: '→';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 0 15px #fff, 0 0 25px #fff;
  animation: slideArrow 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
}

@keyframes slideArrow {
  0%, 100% {
    transform: translate(-50%, -50%) translateX(0);
  }
  50% {
    transform: translate(-50%, -50%) translateX(5px);
  }
}

.legend {
  animation: legendPulse 4s ease-in-out infinite;
}

@keyframes legendPulse {
  0%, 100% {
    box-shadow: 
      0 8px 32px rgba(0, 255, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow: 
      0 8px 40px rgba(0, 255, 255, 0.5),
      0 0 60px rgba(0, 255, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }
}
</style>
